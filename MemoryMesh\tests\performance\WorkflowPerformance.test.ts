// tests/performance/WorkflowPerformance.test.ts

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { WorkflowStateManager } from '../../src/core/workflow/WorkflowStateManager.js';
import { CachedJsonLineStorage } from '../../src/infrastructure/storage/CachedJsonLineStorage.js';
import { PredefinedTemplates } from '../../src/core/workflow/WorkflowTemplates.js';

describe('Workflow Performance Tests', () => {
    let workflowStateManager: WorkflowStateManager;
    let storage: CachedJsonLineStorage;

    beforeEach(() => {
        storage = new CachedJsonLineStorage();
        workflowStateManager = new WorkflowStateManager(storage);
    });

    afterEach(() => {
        storage.cleanup();
        workflowStateManager.cleanup();
    });

    describe('大量工作流程處理', () => {
        it('應該能夠處理100個並發工作流程創建', async () => {
            const startTime = Date.now();
            const template = PredefinedTemplates.NOVEL_TEMPLATE;
            const promises: Promise<any>[] = [];

            // 創建100個工作流程
            for (let i = 0; i < 100; i++) {
                promises.push(
                    workflowStateManager.createWorkflow(template, `Test Workflow ${i}`)
                );
            }

            const results = await Promise.all(promises);
            const endTime = Date.now();
            const duration = endTime - startTime;

            expect(results).toHaveLength(100);
            expect(duration).toBeLessThan(10000); // 應該在10秒內完成
            
            console.log(`Created 100 workflows in ${duration}ms (${duration/100}ms per workflow)`);
        }, 15000);

        it('應該能夠處理大量節點查詢而不出現性能問題', async () => {
            // 創建一個工作流程
            const template = PredefinedTemplates.NOVEL_TEMPLATE;
            const result = await workflowStateManager.createWorkflow(template, 'Performance Test');

            // 添加大量測試節點
            const nodes = [];
            for (let i = 0; i < 1000; i++) {
                nodes.push({
                    type: 'node',
                    name: `test-node-${i}`,
                    nodeType: 'character',
                    metadata: [`workflow_id: ${result.workflowId}`, `index: ${i}`]
                });
            }

            // 批量添加節點
            const startTime = Date.now();
            await storage.batchOperation(
                nodes.map(node => ({
                    type: 'create' as const,
                    target: 'node' as const,
                    data: node
                }))
            );

            // 執行查詢測試
            const queryStartTime = Date.now();
            const workflowNodes = await storage.queryNodesByWorkflow(result.workflowId);
            const queryEndTime = Date.now();

            expect(workflowNodes.length).toBeGreaterThan(1000);
            expect(queryEndTime - queryStartTime).toBeLessThan(1000); // 查詢應該在1秒內完成

            console.log(`Queried ${workflowNodes.length} nodes in ${queryEndTime - queryStartTime}ms`);
        }, 20000);

        it('應該正確管理緩存大小和內存使用', async () => {
            const initialStats = storage.getCacheStats();
            
            // 創建大量工作流程以測試緩存管理
            const template = PredefinedTemplates.NOVEL_TEMPLATE;
            for (let i = 0; i < 50; i++) {
                await workflowStateManager.createWorkflow(template, `Cache Test ${i}`);
            }

            const midStats = storage.getCacheStats();
            expect(midStats.workflowCache).toBeGreaterThan(initialStats.workflowCache);

            // 觸發緩存清理
            await storage.configureCaching({ maxSize: 10, ttl: 1, enablePersistence: true });
            await new Promise(resolve => setTimeout(resolve, 100)); // 等待清理

            const finalStats = storage.getCacheStats();
            
            // 驗證內存使用沒有無限增長
            const memoryIncrease = finalStats.memoryUsage.heapUsed - initialStats.memoryUsage.heapUsed;
            expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // 不應該增加超過50MB

            console.log('Memory usage increase:', Math.round(memoryIncrease / 1024 / 1024), 'MB');
        }, 15000);
    });

    describe('事件系統性能', () => {
        it('應該能夠處理大量事件監聽器而不出現性能問題', async () => {
            const eventCounts = new Map<string, number>();
            
            // 添加大量事件監聽器
            for (let i = 0; i < 50; i++) {
                workflowStateManager.on('workflow_created', (event) => {
                    const count = eventCounts.get('workflow_created') || 0;
                    eventCounts.set('workflow_created', count + 1);
                });
            }

            const startTime = Date.now();
            const template = PredefinedTemplates.NOVEL_TEMPLATE;
            
            // 創建工作流程觸發事件
            await workflowStateManager.createWorkflow(template, 'Event Test');
            
            const endTime = Date.now();
            const duration = endTime - startTime;

            expect(eventCounts.get('workflow_created')).toBe(50);
            expect(duration).toBeLessThan(1000); // 事件處理應該很快

            // 檢查性能統計
            const stats = workflowStateManager.getPerformanceStats();
            expect(stats.eventListeners).toBeGreaterThan(50);
        });

        it('應該正確清理事件監聽器以防止內存洩漏', async () => {
            const initialStats = workflowStateManager.getPerformanceStats();
            
            // 添加臨時監聽器
            const tempListeners: Array<() => void> = [];
            for (let i = 0; i < 20; i++) {
                const listener = () => {};
                workflowStateManager.on('test_event', listener);
                tempListeners.push(listener);
            }

            const midStats = workflowStateManager.getPerformanceStats();
            expect(midStats.eventListeners).toBeGreaterThan(initialStats.eventListeners);

            // 移除監聽器
            tempListeners.forEach(listener => {
                workflowStateManager.removeListener('test_event', listener);
            });

            const finalStats = workflowStateManager.getPerformanceStats();
            expect(finalStats.eventListeners).toBeLessThanOrEqual(initialStats.eventListeners + 5);
        });
    });

    describe('併發安全性', () => {
        it('應該能夠安全處理併發的工作流程狀態更新', async () => {
            const template = PredefinedTemplates.NOVEL_TEMPLATE;
            const result = await workflowStateManager.createWorkflow(template, 'Concurrency Test');

            // 併發更新同一個工作流程
            const updatePromises = [];
            for (let i = 0; i < 10; i++) {
                updatePromises.push(
                    workflowStateManager.updateWorkflowState(result.workflowId, {
                        metadata: { updateIndex: i, timestamp: Date.now() }
                    })
                );
            }

            const results = await Promise.allSettled(updatePromises);
            
            // 所有更新都應該成功或者有合理的錯誤處理
            const successful = results.filter(r => r.status === 'fulfilled').length;
            expect(successful).toBeGreaterThan(0);

            // 最終狀態應該是一致的
            const finalWorkflow = await storage.loadWorkflowState(result.workflowId);
            expect(finalWorkflow).toBeDefined();
            expect(finalWorkflow?.metadata.updateIndex).toBeDefined();
        });

        it('應該能夠處理併發的階段推進操作', async () => {
            const template = PredefinedTemplates.NOVEL_TEMPLATE;
            const result = await workflowStateManager.createWorkflow(template, 'Stage Concurrency Test');

            // 嘗試併發推進階段（應該只有一個成功）
            const advancePromises = [];
            for (let i = 0; i < 5; i++) {
                advancePromises.push(
                    workflowStateManager.advanceToNextStage(result.workflowId, { force: true })
                        .catch(error => ({ error: error.message }))
                );
            }

            const results = await Promise.all(advancePromises);
            
            // 檢查最終狀態的一致性
            const finalWorkflow = await storage.loadWorkflowState(result.workflowId);
            expect(finalWorkflow?.currentStage).toBeGreaterThanOrEqual(0);
            expect(finalWorkflow?.currentStage).toBeLessThan(template.stages.length);
        });
    });

    describe('資源清理', () => {
        it('應該正確清理已完成工作流程的資源', async () => {
            const template = PredefinedTemplates.NOVEL_TEMPLATE;
            
            // 創建並完成一個工作流程
            const result = await workflowStateManager.createWorkflow(template, 'Cleanup Test');
            
            // 強制推進到完成
            for (let i = 0; i < template.stages.length; i++) {
                await workflowStateManager.advanceToNextStage(result.workflowId, { force: true });
            }

            const beforeCleanup = workflowStateManager.getPerformanceStats();
            
            // 執行清理
            await workflowStateManager.cleanupCompletedWorkflows();
            
            const afterCleanup = workflowStateManager.getPerformanceStats();
            
            // 驗證資源被正確清理
            expect(afterCleanup.activeWorkflows).toBeLessThanOrEqual(beforeCleanup.activeWorkflows);
        });
    });
});
