import type { Graph } from './Graph.js';
import type { Node } from './Node.js';
import type { Edge } from './Edge.js';
/**
 * Provides validation methods for graph operations
 */
export declare class GraphValidator {
    /**
     * 檢測是否為章節節點名稱
     */
    private static isChapterNodeName;
    /**
     * 將節點類型映射到檢測上下文
     */
    private static mapNodeTypeToContext;
    /**
     * Validates that a node with the given name exists in the graph.
     */
    static validateNodeExists(graph: Graph, nodeName: string): void;
    /**
     * Validates that a node with the given name does not exist in the graph.
     * Enhanced with intelligent duplicate detection for Chinese text, punctuation, and spacing.
     */
    static validateNodeDoesNotExist(graph: Graph, nodeName: string): void;
    /**
     * Validates that an edge is unique in the graph.
     */
    static validateEdgeUniqueness(graph: Graph, edge: Edge): void;
    /**
     * Validates that a node has required properties.
     */
    static validateNodeProperties(node: Node): void;
    /**
     * Validates that a partial node update has a name property.
     */
    static validateNodeNameProperty(node: Partial<Node>): void;
    /**
     * Validates that the provided value is a valid array of node names.
     */
    static validateNodeNamesArray(nodeNames: unknown): asserts nodeNames is string[];
    /**
     * Validates edge properties.
     */
    static validateEdgeProperties(edge: Edge): void;
    /**
     * Validates that all referenced nodes in edges exist.
     */
    static validateEdgeReferences(graph: Graph, edges: Edge[]): void;
    /**
     * Validates the entire graph structure.
     */
    static validateGraphStructure(graph: Graph): void;
    /**
     * 檢測並報告圖中的重複實體
     */
    static detectDuplicateEntities(graph: Graph, threshold?: number): {
        duplicateGroups: string[][];
        suggestedMerges: Array<{
            duplicates: string[];
            suggestedName: string;
            similarityScores: number[];
        }>;
    };
}
export declare const validateNodeExists: typeof GraphValidator.validateNodeExists, validateNodeDoesNotExist: typeof GraphValidator.validateNodeDoesNotExist, validateEdgeUniqueness: typeof GraphValidator.validateEdgeUniqueness, validateNodeProperties: typeof GraphValidator.validateNodeProperties, validateNodeNameProperty: typeof GraphValidator.validateNodeNameProperty, validateNodeNamesArray: typeof GraphValidator.validateNodeNamesArray, validateEdgeProperties: typeof GraphValidator.validateEdgeProperties, validateEdgeReferences: typeof GraphValidator.validateEdgeReferences, validateGraphStructure: typeof GraphValidator.validateGraphStructure;
