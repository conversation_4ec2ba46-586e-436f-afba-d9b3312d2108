{"version": 3, "file": "staticTools.js", "sourceRoot": "", "sources": ["../../../../src/integration/tools/registry/staticTools.ts"], "names": [], "mappings": "AAAA,oCAAoC;AAIpC;;GAEG;AACH,MAAM,CAAC,MAAM,UAAU,GAAW;IAC9B;QACI,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,+CAA+C;QAC5D,WAAW,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACR,KAAK,EAAE;oBACH,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,uBAAuB;oBACpC,KAAK,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,aAAa;wBAC1B,UAAU,EAAE;4BACR,IAAI,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,EAAC;4BAC3D,QAAQ,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,EAAC;4BAC/D,QAAQ,EAAE;gCACN,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAC;gCACrD,WAAW,EAAE,wDAAwD;6BACxE;yBACJ;wBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC;qBAC7C;iBACJ;aACJ;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACtB;KACJ;IAED;QACI,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,8CAA8C;QAC3D,WAAW,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACR,KAAK,EAAE;oBACH,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,0BAA0B;oBACvC,KAAK,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,gBAAgB;wBAC7B,UAAU,EAAE;4BACR,IAAI,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,gCAAgC,EAAC;4BACrE,QAAQ,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAC;4BACnE,QAAQ,EAAE;gCACN,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAC;gCACrD,WAAW,EAAE,gDAAgD;6BAChE;yBACJ;wBACD,QAAQ,EAAE,CAAC,MAAM,CAAC;qBACrB;iBACJ;aACJ;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACtB;KACJ;IAED;QACI,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,8FAA8F;QAC3G,WAAW,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACR,KAAK,EAAE;oBACH,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,uBAAuB;oBACpC,KAAK,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,aAAa;wBAC1B,UAAU,EAAE;4BACR,IAAI,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,4CAA4C,EAAC;4BACjF,EAAE,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0CAA0C,EAAC;4BAC7E,QAAQ,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,EAAC;4BAC/D,MAAM,EAAE;gCACJ,IAAI,EAAE,QAAQ;gCACd,WAAW,EAAE,kEAAkE;gCAC/E,OAAO,EAAE,CAAC;gCACV,OAAO,EAAE,CAAC;6BACb;yBACJ;wBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC;qBACvC;iBACJ;aACJ;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACtB;KACJ;IAED;QACI,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,8CAA8C;QAC3D,WAAW,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACR,KAAK,EAAE;oBACH,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,0BAA0B;oBACvC,KAAK,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,gBAAgB;wBAC7B,UAAU,EAAE;4BACR,IAAI,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAC;4BAC/D,EAAE,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAC;4BAC7D,QAAQ,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,mBAAmB,EAAC;4BAC5D,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,EAAC;4BAC9D,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,EAAC;4BAC5D,WAAW,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAC;4BAC3D,SAAS,EAAE;gCACP,IAAI,EAAE,QAAQ;gCACd,WAAW,EAAE,sCAAsC;gCACnD,OAAO,EAAE,CAAC;gCACV,OAAO,EAAE,CAAC;6BACb;yBACJ;wBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC;qBACvC;iBACJ;aACJ;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACtB;KACJ;IAED;QACI,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,2EAA2E;QACxF,WAAW,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACR,SAAS,EAAE;oBACP,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,qBAAqB,EAAC;oBAC3D,WAAW,EAAE,kCAAkC;iBAClD;aACJ;YACD,QAAQ,EAAE,CAAC,WAAW,CAAC;SAC1B;KACJ;IAED;QACI,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,gDAAgD;QAC7D,WAAW,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACR,KAAK,EAAE;oBACH,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,0BAA0B;oBACvC,KAAK,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,gBAAgB;wBAC7B,UAAU,EAAE;4BACR,IAAI,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,4CAA4C,EAAC;4BACjF,EAAE,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0CAA0C,EAAC;4BAC7E,QAAQ,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,EAAC;yBAClE;wBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC;qBACvC;iBACJ;aACJ;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACtB;KACJ;CACJ,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAW;IAC/B;QACI,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,yDAAyD;QACtE,WAAW,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACR,eAAe,EAAE;oBACb,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,oEAAoE;iBACpF;gBACD,cAAc,EAAE;oBACZ,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,EAAC;oBAC5D,WAAW,EAAE,iFAAiF;iBACjG;gBACD,KAAK,EAAE;oBACH,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,sDAAsD;oBACnE,OAAO,EAAE,CAAC;iBACb;aACJ;SACJ;KACJ;IAED;QACI,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,0DAA0D;QACvE,WAAW,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACR,KAAK,EAAE;oBACH,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,2EAA2E;iBAC3F;aACJ;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACtB;KACJ;IAED;QACI,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,2DAA2D;QACxE,WAAW,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACR,KAAK,EAAE;oBACH,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,mBAAmB,EAAC;oBACzD,WAAW,EAAE,oCAAoC;iBACpD;aACJ;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACtB;KACJ;CACJ,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAW;IACjC;QACI,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,2DAA2D;QACxE,WAAW,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACR,QAAQ,EAAE;oBACN,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,0BAA0B;oBACvC,KAAK,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,iBAAiB;wBAC9B,UAAU,EAAE;4BACR,QAAQ,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,6CAA6C,EAAC;4BACtF,QAAQ,EAAE;gCACN,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,uBAAuB,EAAC;gCAC7D,WAAW,EAAE,sCAAsC;6BACtD;yBACJ;wBACD,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;qBACrC;iBACJ;aACJ;YACD,QAAQ,EAAE,CAAC,UAAU,CAAC;SACzB;KACJ;IAED;QACI,IAAI,EAAE,iBAAiB;QACvB,WAAW,EAAE,4DAA4D;QACzE,WAAW,EAAE;YACT,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACR,SAAS,EAAE;oBACP,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,6BAA6B;oBAC1C,KAAK,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,mBAAmB;wBAChC,UAAU,EAAE;4BACR,QAAQ,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,8CAA8C,EAAC;4BACvF,QAAQ,EAAE;gCACN,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,yBAAyB,EAAC;gCAC/D,WAAW,EAAE,gCAAgC;6BAChD;yBACJ;wBACD,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;qBACrC;iBACJ;aACJ;YACD,QAAQ,EAAE,CAAC,WAAW,CAAC;SAC1B;KACJ;CACJ,CAAC;AAEF,OAAO,EAAE,kCAAkC,EAAE,MAAM,oBAAoB,CAAC;AAExE;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAW;IAClC,GAAG,UAAU;IACb,GAAG,WAAW;IACd,GAAG,aAAa;IAChB,GAAG,kCAAkC,EAAE;CAC1C,CAAC"}