// MemoryMesh v0.3.1 完整功能測試腳本
import('./dist/integration/tools/handlers/WorkflowToolHandler.js').then(async (handlerModule) => {
  const storageModule = await import('./dist/infrastructure/storage/CachedJsonLineStorage.js');
  const appManagerModule = await import('./dist/application/managers/ApplicationManager.js');
  const dynamicToolsModule = await import('./dist/integration/tools/handlers/DynamicToolHandler.js');
  
  const storage = new storageModule.CachedJsonLineStorage();
  const appManager = new appManagerModule.ApplicationManager(storage);
  const workflowHandler = new handlerModule.WorkflowToolHandler(appManager, storage);
  const dynamicHandler = new dynamicToolsModule.DynamicToolHandler(appManager);
  
  console.log('🧪 MemoryMesh v0.3.1 完整功能測試驗證');
  console.log('📚 測試主題：《大奉打更人》小說創作流程');
  console.log('=' .repeat(60));
  
  try {
    // 第1階段：創建工作流程
    console.log('\n📋 第1階段：創建工作流程');
    const createResult = await workflowHandler.handleTool('workflow_create', {
      name: '大奉打更人_許七安的探案之路',
      type: 'novel',
      metadata: {
        genre: '玄幻探案',
        targetAudience: '成年讀者',
        estimatedLength: '200萬字',
        setting: '大奉王朝',
        themes: ['正義', '成長', '責任', '探索真相'],
        writingStyle: '第三人稱限制視角'
      }
    });
    
    if (createResult.isError) {
      console.log('❌ 工作流程創建失敗:', createResult);
      return;
    }
    
    const workflowData = JSON.parse(createResult.content[1].text.split('Data: ')[1]);
    const workflowId = workflowData.workflowId;
    console.log('✅ 工作流程創建成功，ID:', workflowId);
    
    // 第2階段：規劃階段測試
    console.log('\n👤 第2階段：規劃階段 - 創建角色');
    
    // 創建主角
    const xuQianResult = await dynamicHandler.handleTool('add_character', {
      character: {
        name: '許七安',
        role: 'protagonist',
        status: 'Active',
        currentLocation: ['京城', '打更人衙門'],
        description: '現代刑警穿越到大奉王朝，成為打更人，擁有現代偵探思維和古代修行體系',
        traits: ['機智', '正義感', '適應力強', '有時過於自信'],
        background: '21世紀刑警，因意外穿越到大奉王朝，利用現代知識在古代世界生存發展',
        motivation: '在新世界中生存並發揮自己的價值，維護正義',
        internalConflict: '現代思維與古代環境的衝突，身份認同的困惑',
        characterArc: '從迷茫的穿越者成長為真正的守護者',
        importance: 'Protagonist',
        moralAlignment: 'Good'
      }
    });
    
    console.log('許七安創建結果:', xuQianResult.isError ? '❌ 失敗' : '✅ 成功');
    
    // 創建設定
    console.log('\n🏛️ 創建設定');
    const settingResult = await dynamicHandler.handleTool('add_setting', {
      setting: {
        name: '大奉王朝',
        type: 'Urban',
        description: '古代封建王朝，有著複雜的政治體系和修行文化',
        status: 'Active',
        significance: 'Critical',
        atmosphere: '古樸莊嚴中透著暗流涌動的政治氣息',
        sensoryDetails: '青石板路面，紅牆黃瓦的宮殿，街市的叫賣聲',
        culturalContext: '儒家文化為主，佛道並存，修行者地位崇高'
      }
    });
    
    console.log('大奉王朝設定創建結果:', settingResult.isError ? '❌ 失敗' : '✅ 成功');
    
    // 創建主題
    console.log('\n🎭 創建主題');
    const themeResult = await dynamicHandler.handleTool('add_theme', {
      theme: {
        name: '正義與成長',
        type: 'Main Theme',
        description: '通過許七安在大奉王朝的探案經歷，探討正義的真諦和個人成長的意義',
        status: 'Developing',
        importance: 'Core',
        currentDevelopment: '主角正在通過各種案件逐步理解古代社會的正義觀念'
      }
    });
    
    console.log('正義與成長主題創建結果:', themeResult.isError ? '❌ 失敗' : '✅ 成功');
    
    // 第3階段：列出所有工作流程
    console.log('\n📊 第3階段：檢查工作流程列表');
    const listResult = await workflowHandler.handleTool('workflow_list', {
      type: 'novel',
      limit: 10
    });
    
    if (!listResult.isError) {
      const listData = JSON.parse(listResult.content[1].text.split('Data: ')[1]);
      console.log('✅ 找到', listData.workflows.length, '個小說工作流程');
      
      // 找到我們的工作流程
      const ourWorkflow = listData.workflows.find(w => w.workflowId === workflowId);
      if (ourWorkflow) {
        console.log('✅ 找到我們的工作流程:', ourWorkflow.name);
        console.log('   當前階段:', ourWorkflow.currentStage);
        console.log('   進度:', ourWorkflow.progress + '%');
        
        // 第4階段：測試v0.3.1增強的階段驗證
        console.log('\n🔍 第4階段：測試v0.3.1增強的階段驗證');
        const validationResult = await workflowHandler.handleTool('stage_validate', {
          workflowId: ourWorkflow.workflowId
        });
        
        if (!validationResult.isError) {
          const validationData = JSON.parse(validationResult.content[1].text.split('Data: ')[1]);
          
          console.log('✅ 階段驗證成功');
          console.log('📊 驗證結果:');
          console.log('  完成狀態:', validationData.isComplete ? '✅ 完成' : '❌ 未完成');
          console.log('  質量分數:', validationData.qualityAssessment?.overallScore || 'N/A', '/100');
          
          // 測試v0.3.1新功能
          if (validationData.nextStepGuidance) {
            console.log('\n🎯 v0.3.1新功能 - 下一步指導:');
            validationData.nextStepGuidance.immediateActions?.forEach(action => {
              console.log('  •', action);
            });
            
            console.log('\n💡 質量提升建議:');
            validationData.nextStepGuidance.qualityTips?.forEach(tip => {
              console.log('  •', tip);
            });
            
            console.log('\n📝 內容示例:');
            validationData.nextStepGuidance.contentExamples?.forEach(example => {
              console.log('  •', example);
            });
          }
          
          if (validationData.progressInsights) {
            console.log('\n🔍 v0.3.1新功能 - 進度洞察:');
            console.log('  當前重點:', validationData.progressInsights.currentFocus);
            
            if (validationData.progressInsights.strengthAreas?.length > 0) {
              console.log('  優勢領域:');
              validationData.progressInsights.strengthAreas.forEach(area => {
                console.log('    ✅', area);
              });
            }
            
            if (validationData.progressInsights.improvementAreas?.length > 0) {
              console.log('  改進領域:');
              validationData.progressInsights.improvementAreas.forEach(area => {
                console.log('    🔧', area);
              });
            }
          }
          
          if (validationData.qualityAssessment) {
            console.log('\n📈 v0.3.1新功能 - 質量評估:');
            console.log('  通過的檢查:');
            validationData.qualityAssessment.passedChecks?.forEach(check => {
              console.log('    ✅', check);
            });
            
            if (validationData.qualityAssessment.failedChecks?.length > 0) {
              console.log('  未通過的檢查:');
              validationData.qualityAssessment.failedChecks.forEach(check => {
                console.log('    ❌', check);
              });
            }
            
            if (validationData.qualityAssessment.recommendations?.length > 0) {
              console.log('  改進建議:');
              validationData.qualityAssessment.recommendations.forEach(rec => {
                console.log('    💡', rec);
              });
            }
          }
          
        } else {
          console.log('❌ 階段驗證失敗:', validationResult);
        }
        
      } else {
        console.log('❌ 未找到我們的工作流程');
      }
    } else {
      console.log('❌ 工作流程列表查詢失敗:', listResult);
    }
    
    console.log('\n🎉 測試完成！');
    
  } catch (error) {
    console.error('❌ 測試過程中發生錯誤:', error);
  }
  
}).catch(console.error);
