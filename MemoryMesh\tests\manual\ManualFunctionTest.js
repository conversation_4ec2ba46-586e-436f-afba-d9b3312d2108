// tests/manual/ManualFunctionTest.js
// 手動功能測試腳本

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 開始手動功能測試...\n');

// 測試1: 檢查編譯後的文件是否存在
console.log('📁 檢查編譯後的文件...');
const distPath = path.join(__dirname, '../../dist');
const requiredFiles = [
    'core/workflow/WorkflowTemplates.js',
    'core/workflow/WorkflowStateManager.js',
    'infrastructure/storage/CachedJsonLineStorage.js',
    'integration/tools/handlers/WorkflowToolHandler.js',
    'integration/tools/registry/workflowTools.js'
];

let filesExist = true;
requiredFiles.forEach(file => {
    const filePath = path.join(distPath, file);
    if (fs.existsSync(filePath)) {
        console.log(`  ✅ ${file}`);
    } else {
        console.log(`  ❌ ${file} - 文件不存在`);
        filesExist = false;
    }
});

if (!filesExist) {
    console.log('\n❌ 部分必需文件不存在，請先運行 npm run build');
    process.exit(1);
}

// 測試2: 檢查MemoryViewer.html是否包含工作流程功能
console.log('\n🎨 檢查MemoryViewer UI更新...');
const viewerPath = path.join(__dirname, '../../MemoryViewer.html');
if (fs.existsSync(viewerPath)) {
    const content = fs.readFileSync(viewerPath, 'utf-8');
    
    const requiredElements = [
        'workflow',
        'Workflow View',
        'workflowView',
        'workflow-container',
        'updateWorkflowView',
        'renderWorkflowList'
    ];
    
    let uiComplete = true;
    requiredElements.forEach(element => {
        if (content.includes(element)) {
            console.log(`  ✅ 包含 ${element}`);
        } else {
            console.log(`  ❌ 缺少 ${element}`);
            uiComplete = false;
        }
    });
    
    if (uiComplete) {
        console.log('  ✅ MemoryViewer UI更新完整');
    } else {
        console.log('  ⚠️  MemoryViewer UI更新不完整');
    }
} else {
    console.log('  ❌ MemoryViewer.html 文件不存在');
}

// 測試3: 檢查package.json是否包含測試依賴
console.log('\n📦 檢查package.json依賴...');
const packagePath = path.join(__dirname, '../../package.json');
if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf-8'));
    
    const requiredDeps = ['jest', 'ts-jest', '@types/jest'];
    let depsComplete = true;
    
    requiredDeps.forEach(dep => {
        if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
            console.log(`  ✅ ${dep}: ${packageJson.devDependencies[dep]}`);
        } else {
            console.log(`  ❌ 缺少依賴: ${dep}`);
            depsComplete = false;
        }
    });
    
    if (depsComplete) {
        console.log('  ✅ 測試依賴完整');
    } else {
        console.log('  ⚠️  測試依賴不完整');
    }
} else {
    console.log('  ❌ package.json 文件不存在');
}

// 測試4: 檢查文檔文件
console.log('\n📚 檢查文檔文件...');
const docFiles = [
    'docs/EXTENSION_INTERFACES.md',
    'jest.config.js',
    'tests/setup.ts'
];

let docsComplete = true;
docFiles.forEach(file => {
    const filePath = path.join(__dirname, '../../', file);
    if (fs.existsSync(filePath)) {
        console.log(`  ✅ ${file}`);
    } else {
        console.log(`  ❌ ${file} - 文件不存在`);
        docsComplete = false;
    }
});

if (docsComplete) {
    console.log('  ✅ 文檔文件完整');
} else {
    console.log('  ⚠️  文檔文件不完整');
}

// 測試5: 嘗試動態導入模塊（如果可能）
console.log('\n🔧 測試模塊導入...');
async function testModuleImports() {
    try {
        // 嘗試導入工作流程模板
        const templatesPath = path.join(distPath, 'core/workflow/WorkflowTemplates.js');
        if (fs.existsSync(templatesPath)) {
            console.log('  ✅ WorkflowTemplates.js 文件存在');
            
            // 檢查文件內容
            const content = fs.readFileSync(templatesPath, 'utf-8');
            if (content.includes('PredefinedTemplates') && content.includes('NOVEL_TEMPLATE')) {
                console.log('  ✅ WorkflowTemplates 內容正確');
            } else {
                console.log('  ⚠️  WorkflowTemplates 內容可能不完整');
            }
        }
        
        // 嘗試導入存儲模塊
        const storagePath = path.join(distPath, 'infrastructure/storage/CachedJsonLineStorage.js');
        if (fs.existsSync(storagePath)) {
            console.log('  ✅ CachedJsonLineStorage.js 文件存在');
            
            const content = fs.readFileSync(storagePath, 'utf-8');
            if (content.includes('CachedJsonLineStorage') && content.includes('configureCaching')) {
                console.log('  ✅ CachedJsonLineStorage 內容正確');
            } else {
                console.log('  ⚠️  CachedJsonLineStorage 內容可能不完整');
            }
        }
        
        // 檢查工具定義
        const toolsPath = path.join(distPath, 'integration/tools/registry/workflowTools.js');
        if (fs.existsSync(toolsPath)) {
            console.log('  ✅ workflowTools.js 文件存在');
            
            const content = fs.readFileSync(toolsPath, 'utf-8');
            if (content.includes('workflow_create') && content.includes('workflow_status')) {
                console.log('  ✅ workflowTools 內容正確');
            } else {
                console.log('  ⚠️  workflowTools 內容可能不完整');
            }
        }
        
    } catch (error) {
        console.log(`  ⚠️  模塊導入測試遇到問題: ${error.message}`);
    }
}

testModuleImports().then(() => {
    console.log('\n📊 測試總結:');
    console.log('✅ 編譯文件檢查完成');
    console.log('✅ UI更新檢查完成');
    console.log('✅ 依賴檢查完成');
    console.log('✅ 文檔檢查完成');
    console.log('✅ 模塊內容檢查完成');
    
    console.log('\n🎉 手動功能測試完成！');
    console.log('\n📝 建議下一步:');
    console.log('1. 運行 npm test 執行自動化測試');
    console.log('2. 在瀏覽器中打開 MemoryViewer.html 測試UI');
    console.log('3. 使用MCP客戶端測試工作流程工具');
    console.log('4. 查看 docs/EXTENSION_INTERFACES.md 了解擴展接口');
});
