// src/core/workflow/WorkflowTemplates.ts

import type { Node } from '@core/index.js';

/**
 * 工作流程模板接口
 * 定義不同類型創作工作流程的標準結構
 */
export interface WorkflowTemplate {
    readonly id: string;
    readonly name: string;
    readonly description: string;
    readonly category: 'novel' | 'article' | 'script' | 'academic' | 'technical' | 'creative' | 'custom';
    readonly version: string;
    readonly stages: StageTemplate[];
    
    // 🔮 預留模板擴展字段
    readonly extensions?: Record<string, any>;
    readonly customFields?: Record<string, any>;
}

/**
 * 階段模板接口
 */
export interface StageTemplate {
    readonly id: string;
    readonly name: string;
    readonly description: string;
    readonly order: number;
    readonly requiredNodeTypes: string[];
    readonly optionalNodeTypes: string[];
    readonly completionCriteria: CompletionCriteria;
    readonly estimatedDuration?: string; // 預估完成時間
    
    // 🔮 預留階段擴展字段
    readonly extensions?: Record<string, any>;
}

/**
 * 完成條件接口
 */
export interface CompletionCriteria {
    readonly minNodes: number;
    readonly requiredFields: string[];
    readonly customValidation?: string; // 自定義驗證邏輯的標識符
    
    // 🔮 預留條件擴展字段
    readonly extensions?: Record<string, any>;
}

/**
 * 工作流程節點生成器
 * 根據模板生成標準的工作流程節點
 */
export class WorkflowNodeGenerator {
    
    /**
     * 根據模板創建工作流程節點
     */
    static createWorkflowNode(
        template: WorkflowTemplate, 
        workflowId: string,
        customMetadata?: Record<string, any>
    ): Node {
        const metadata = [
            `workflow_id: ${workflowId}`,
            `template_id: ${template.id}`,
            `template_version: ${template.version}`,
            `category: ${template.category}`,
            `status: not_started`,
            `current_stage: 0`,
            `total_stages: ${template.stages.length}`,
            `progress: 0`,
            `created_at: ${new Date().toISOString()}`,
            `updated_at: ${new Date().toISOString()}`,
            
            // 階段信息
            `stages: ${JSON.stringify(template.stages.map(s => ({
                id: s.id,
                name: s.name,
                order: s.order,
                status: s.order === 0 ? 'active' : 'pending'
            })))}`,
            
            // 🔮 預留自定義metadata
            ...Object.entries(customMetadata || {}).map(([key, value]) => 
                `custom_${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`
            )
        ];

        return {
            type: 'node',
            name: `workflow_${workflowId}`,
            nodeType: 'workflow',
            metadata
        };
    }

    /**
     * 創建階段節點
     */
    static createStageNodes(
        template: WorkflowTemplate,
        workflowId: string
    ): Node[] {
        return template.stages.map(stage => ({
            type: 'node',
            name: `stage_${workflowId}_${stage.id}`,
            nodeType: 'stage',
            metadata: [
                `workflow_id: ${workflowId}`,
                `stage_id: ${stage.id}`,
                `stage_name: ${stage.name}`,
                `stage_order: ${stage.order}`,
                `status: ${stage.order === 0 ? 'active' : 'pending'}`,
                `required_node_types: ${JSON.stringify(stage.requiredNodeTypes)}`,
                `optional_node_types: ${JSON.stringify(stage.optionalNodeTypes)}`,
                `completion_criteria: ${JSON.stringify(stage.completionCriteria)}`,
                `estimated_duration: ${stage.estimatedDuration || 'unknown'}`,
                `created_at: ${new Date().toISOString()}`,
                
                // 🔮 預留階段擴展字段
                ...Object.entries(stage.extensions || {}).map(([key, value]) => 
                    `ext_${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`
                )
            ]
        }));
    }

    /**
     * 創建階段依賴邊
     */
    static createStageDependencyEdges(
        template: WorkflowTemplate,
        workflowId: string
    ): Array<{from: string, to: string, edgeType: string, weight?: number}> {
        const edges: Array<{from: string, to: string, edgeType: string, weight?: number}> = [];
        
        // 創建順序依賴邊
        for (let i = 0; i < template.stages.length - 1; i++) {
            edges.push({
                from: `stage_${workflowId}_${template.stages[i].id}`,
                to: `stage_${workflowId}_${template.stages[i + 1].id}`,
                edgeType: 'next_stage',
                weight: 1
            });
        }
        
        // 創建工作流程到階段的歸屬邊
        template.stages.forEach(stage => {
            edges.push({
                from: `workflow_${workflowId}`,
                to: `stage_${workflowId}_${stage.id}`,
                edgeType: 'contains_stage',
                weight: 1
            });
        });
        
        return edges;
    }
}

/**
 * 預定義的工作流程模板
 */
export class PredefinedTemplates {
    
    /**
     * 小說創作工作流程模板
     */
    static readonly NOVEL_TEMPLATE: WorkflowTemplate = {
        id: 'novel_standard_v1',
        name: '標準小說創作流程',
        description: '適用於長篇小說創作的標準化工作流程',
        category: 'novel',
        version: '1.0.0',
        stages: [
            {
                id: 'planning',
                name: '規劃階段',
                description: '建立小說的基本設定、角色和世界觀',
                order: 0,
                requiredNodeTypes: ['character', 'setting', 'theme'],
                optionalNodeTypes: ['timeline', 'worldbuilding'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['name', 'description', 'background']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'outline',
                name: '大綱階段',
                description: '制定詳細的故事大綱和情節結構',
                order: 1,
                requiredNodeTypes: ['plotarc', 'timeline'],
                optionalNodeTypes: ['conflict', 'theme'],
                completionCriteria: {
                    minNodes: 1,
                    requiredFields: ['main_plot', 'key_events']
                },
                estimatedDuration: '1 week'
            },
            {
                id: 'chapter',
                name: '章節階段',
                description: '規劃具體的章節內容和場景',
                order: 2,
                requiredNodeTypes: ['scene', 'chapter'],
                optionalNodeTypes: ['dialogue', 'description'],
                completionCriteria: {
                    minNodes: 5,
                    requiredFields: ['scene_setting', 'characters_involved']
                },
                estimatedDuration: '2-3 weeks'
            },
            {
                id: 'generation',
                name: '生成階段',
                description: '基於規劃內容生成完整的小說文本',
                order: 3,
                requiredNodeTypes: ['chapter'],
                optionalNodeTypes: ['revision_notes'],
                completionCriteria: {
                    minNodes: 1,
                    requiredFields: ['content', 'word_count']
                },
                estimatedDuration: '4-6 weeks'
            }
        ]
    };

    /**
     * 文章創作工作流程模板
     */
    static readonly ARTICLE_TEMPLATE: WorkflowTemplate = {
        id: 'article_standard_v1',
        name: '標準文章創作流程',
        description: '適用於專業文章和博客文章的創作流程',
        category: 'article',
        version: '1.0.0',
        stages: [
            {
                id: 'research',
                name: '研究階段',
                description: '收集資料和進行主題研究',
                order: 0,
                requiredNodeTypes: ['topic', 'source', 'research_note'],
                optionalNodeTypes: ['reference'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['topic', 'key_points']
                },
                estimatedDuration: '2-3 days'
            },
            {
                id: 'outline',
                name: '大綱階段',
                description: '制定文章結構和要點',
                order: 1,
                requiredNodeTypes: ['outline', 'section'],
                optionalNodeTypes: ['argument'],
                completionCriteria: {
                    minNodes: 1,
                    requiredFields: ['structure', 'main_points']
                },
                estimatedDuration: '1 day'
            },
            {
                id: 'draft',
                name: '草稿階段',
                description: '撰寫初稿內容',
                order: 2,
                requiredNodeTypes: ['draft'],
                optionalNodeTypes: ['paragraph', 'citation'],
                completionCriteria: {
                    minNodes: 1,
                    requiredFields: ['content', 'word_count']
                },
                estimatedDuration: '2-3 days'
            },
            {
                id: 'review',
                name: '審查階段',
                description: '校對和完善文章內容',
                order: 3,
                requiredNodeTypes: ['final_article'],
                optionalNodeTypes: ['revision_note', 'feedback'],
                completionCriteria: {
                    minNodes: 1,
                    requiredFields: ['final_content', 'review_status']
                },
                estimatedDuration: '1-2 days'
            }
        ]
    };

    /**
     * 劇本創作工作流程模板
     */
    static readonly SCRIPT_TEMPLATE: WorkflowTemplate = {
        id: 'script_standard_v1',
        name: '標準劇本創作流程',
        description: '適用於電影、電視劇和舞台劇本的創作流程',
        category: 'script',
        version: '1.0.0',
        stages: [
            {
                id: 'concept',
                name: '概念階段',
                description: '建立劇本的核心概念、主題和基本設定',
                order: 0,
                requiredNodeTypes: ['concept', 'theme', 'genre'],
                optionalNodeTypes: ['inspiration', 'reference'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['core_concept', 'target_audience', 'genre']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'character_development',
                name: '角色發展階段',
                description: '創建和發展主要角色，建立角色關係',
                order: 1,
                requiredNodeTypes: ['character', 'relationship'],
                optionalNodeTypes: ['character_arc', 'backstory'],
                completionCriteria: {
                    minNodes: 5,
                    requiredFields: ['character_motivation', 'character_conflict', 'dialogue_style']
                },
                estimatedDuration: '2-3 weeks'
            },
            {
                id: 'structure_outline',
                name: '結構大綱階段',
                description: '建立三幕結構和主要情節點',
                order: 2,
                requiredNodeTypes: ['act', 'scene_outline', 'plot_point'],
                optionalNodeTypes: ['subplot', 'turning_point'],
                completionCriteria: {
                    minNodes: 8,
                    requiredFields: ['act_structure', 'scene_breakdown', 'pacing']
                },
                estimatedDuration: '2-3 weeks'
            },
            {
                id: 'scene_writing',
                name: '場景撰寫階段',
                description: '撰寫具體的場景和對話',
                order: 3,
                requiredNodeTypes: ['scene', 'dialogue'],
                optionalNodeTypes: ['action_line', 'stage_direction'],
                completionCriteria: {
                    minNodes: 15,
                    requiredFields: ['scene_content', 'character_dialogue', 'visual_elements']
                },
                estimatedDuration: '4-6 weeks'
            },
            {
                id: 'revision_polish',
                name: '修訂潤色階段',
                description: '修訂劇本結構、對話和格式',
                order: 4,
                requiredNodeTypes: ['revision'],
                optionalNodeTypes: ['feedback', 'format_check'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['final_script', 'format_compliance', 'readthrough_notes']
                },
                estimatedDuration: '2-3 weeks'
            }
        ]
    };

    /**
     * 學術論文工作流程模板
     */
    static readonly ACADEMIC_TEMPLATE: WorkflowTemplate = {
        id: 'academic_standard_v1',
        name: '標準學術論文流程',
        description: '適用於學術研究論文和期刊文章的創作流程',
        category: 'academic',
        version: '1.0.0',
        stages: [
            {
                id: 'literature_review',
                name: '文獻回顧階段',
                description: '收集和分析相關學術文獻',
                order: 0,
                requiredNodeTypes: ['literature', 'citation', 'research_gap'],
                optionalNodeTypes: ['database_search', 'keyword'],
                completionCriteria: {
                    minNodes: 10,
                    requiredFields: ['literature_summary', 'research_gap', 'theoretical_framework']
                },
                estimatedDuration: '3-4 weeks'
            },
            {
                id: 'methodology',
                name: '研究方法階段',
                description: '設計研究方法和實驗設計',
                order: 1,
                requiredNodeTypes: ['methodology', 'research_design', 'data_collection'],
                optionalNodeTypes: ['ethics_approval', 'pilot_study'],
                completionCriteria: {
                    minNodes: 5,
                    requiredFields: ['research_method', 'data_analysis_plan', 'validity_measures']
                },
                estimatedDuration: '2-3 weeks'
            },
            {
                id: 'data_analysis',
                name: '數據分析階段',
                description: '收集和分析研究數據',
                order: 2,
                requiredNodeTypes: ['data', 'analysis', 'results'],
                optionalNodeTypes: ['statistical_test', 'visualization'],
                completionCriteria: {
                    minNodes: 8,
                    requiredFields: ['raw_data', 'analysis_results', 'statistical_significance']
                },
                estimatedDuration: '4-6 weeks'
            },
            {
                id: 'writing_drafting',
                name: '論文撰寫階段',
                description: '撰寫論文各個章節',
                order: 3,
                requiredNodeTypes: ['abstract', 'introduction', 'discussion', 'conclusion'],
                optionalNodeTypes: ['appendix', 'acknowledgments'],
                completionCriteria: {
                    minNodes: 6,
                    requiredFields: ['complete_draft', 'proper_citations', 'academic_style']
                },
                estimatedDuration: '3-4 weeks'
            },
            {
                id: 'peer_review',
                name: '同行評議階段',
                description: '獲取反饋並修訂論文',
                order: 4,
                requiredNodeTypes: ['peer_feedback', 'revision'],
                optionalNodeTypes: ['supervisor_review', 'language_check'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['final_paper', 'response_to_reviewers', 'submission_ready']
                },
                estimatedDuration: '2-3 weeks'
            }
        ]
    };

    /**
     * 技術文檔工作流程模板
     */
    static readonly TECHNICAL_TEMPLATE: WorkflowTemplate = {
        id: 'technical_standard_v1',
        name: '標準技術文檔流程',
        description: '適用於API文檔、用戶手冊和技術規範的創作流程',
        category: 'technical',
        version: '1.0.0',
        stages: [
            {
                id: 'requirements_analysis',
                name: '需求分析階段',
                description: '分析文檔需求和目標受眾',
                order: 0,
                requiredNodeTypes: ['requirement', 'audience', 'scope'],
                optionalNodeTypes: ['stakeholder', 'use_case'],
                completionCriteria: {
                    minNodes: 4,
                    requiredFields: ['target_audience', 'document_scope', 'success_criteria']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'information_architecture',
                name: '信息架構階段',
                description: '設計文檔結構和信息組織',
                order: 1,
                requiredNodeTypes: ['structure', 'navigation', 'taxonomy'],
                optionalNodeTypes: ['wireframe', 'user_journey'],
                completionCriteria: {
                    minNodes: 5,
                    requiredFields: ['document_structure', 'navigation_design', 'content_hierarchy']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'content_creation',
                name: '內容創建階段',
                description: '撰寫技術內容和代碼示例',
                order: 2,
                requiredNodeTypes: ['content', 'code_example', 'diagram'],
                optionalNodeTypes: ['screenshot', 'video', 'interactive_demo'],
                completionCriteria: {
                    minNodes: 10,
                    requiredFields: ['technical_content', 'code_samples', 'visual_aids']
                },
                estimatedDuration: '3-4 weeks'
            },
            {
                id: 'review_testing',
                name: '審查測試階段',
                description: '技術審查和用戶測試',
                order: 3,
                requiredNodeTypes: ['technical_review', 'user_testing'],
                optionalNodeTypes: ['accessibility_check', 'seo_optimization'],
                completionCriteria: {
                    minNodes: 4,
                    requiredFields: ['review_feedback', 'test_results', 'accuracy_verification']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'publication_maintenance',
                name: '發布維護階段',
                description: '發布文檔並建立維護流程',
                order: 4,
                requiredNodeTypes: ['publication', 'maintenance_plan'],
                optionalNodeTypes: ['analytics', 'feedback_system'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['published_document', 'update_schedule', 'feedback_mechanism']
                },
                estimatedDuration: '1 week'
            }
        ]
    };

    /**
     * 創意寫作工作流程模板
     */
    static readonly CREATIVE_TEMPLATE: WorkflowTemplate = {
        id: 'creative_standard_v1',
        name: '標準創意寫作流程',
        description: '適用於詩歌、散文、短篇小說等創意寫作的流程',
        category: 'creative',
        version: '1.0.0',
        stages: [
            {
                id: 'inspiration_gathering',
                name: '靈感收集階段',
                description: '收集創作靈感和素材',
                order: 0,
                requiredNodeTypes: ['inspiration', 'mood', 'image'],
                optionalNodeTypes: ['music', 'memory', 'observation'],
                completionCriteria: {
                    minNodes: 5,
                    requiredFields: ['core_inspiration', 'emotional_tone', 'sensory_details']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'experimentation',
                name: '實驗階段',
                description: '嘗試不同的寫作風格和技巧',
                order: 1,
                requiredNodeTypes: ['experiment', 'style_trial', 'voice'],
                optionalNodeTypes: ['form_exploration', 'technique'],
                completionCriteria: {
                    minNodes: 6,
                    requiredFields: ['writing_voice', 'chosen_style', 'experimental_pieces']
                },
                estimatedDuration: '2-3 weeks'
            },
            {
                id: 'drafting',
                name: '草稿階段',
                description: '創作初稿和多個版本',
                order: 2,
                requiredNodeTypes: ['draft', 'version'],
                optionalNodeTypes: ['fragment', 'alternative_ending'],
                completionCriteria: {
                    minNodes: 4,
                    requiredFields: ['complete_draft', 'alternative_versions', 'self_reflection']
                },
                estimatedDuration: '2-4 weeks'
            },
            {
                id: 'refinement',
                name: '精煉階段',
                description: '修訂和完善作品',
                order: 3,
                requiredNodeTypes: ['revision', 'polish'],
                optionalNodeTypes: ['peer_feedback', 'reading_aloud'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['refined_work', 'artistic_vision', 'emotional_impact']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'sharing_reflection',
                name: '分享反思階段',
                description: '分享作品並反思創作過程',
                order: 4,
                requiredNodeTypes: ['final_work', 'reflection'],
                optionalNodeTypes: ['audience_feedback', 'publication'],
                completionCriteria: {
                    minNodes: 2,
                    requiredFields: ['completed_work', 'creative_reflection', 'next_steps']
                },
                estimatedDuration: '1 week'
            }
        ]
    };

    /**
     * 獲取所有預定義模板
     */
    static getAllTemplates(): WorkflowTemplate[] {
        return [
            this.NOVEL_TEMPLATE,
            this.ARTICLE_TEMPLATE,
            this.SCRIPT_TEMPLATE,
            this.ACADEMIC_TEMPLATE,
            this.TECHNICAL_TEMPLATE,
            this.CREATIVE_TEMPLATE
        ];
    }

    /**
     * 根據類別獲取模板
     */
    static getTemplatesByCategory(category: string): WorkflowTemplate[] {
        return this.getAllTemplates().filter(template => template.category === category);
    }

    /**
     * 根據ID獲取特定模板
     */
    static getTemplateById(id: string): WorkflowTemplate | null {
        return this.getAllTemplates().find(template => template.id === id) || null;
    }

    /**
     * 解析時長字符串為週數
     */
    private static parseDuration(duration?: string): number {
        if (!duration) return 1;
        const match = duration.match(/(\d+)(?:-(\d+))?\s*(day|week|month)s?/);
        if (!match) return 1;

        const min = parseInt(match[1]);
        const max = match[2] ? parseInt(match[2]) : min;
        const avg = (min + max) / 2;
        const unit = match[3];

        switch (unit) {
            case 'day': return avg / 7;
            case 'week': return avg;
            case 'month': return avg * 4;
            default: return avg;
        }
    }

    /**
     * 獲取適用受眾
     */
    private static getSuitableAudience(category: string): string[] {
        const audiences: Record<string, string[]> = {
            'novel': ['小說作家', '創意寫作者', '文學愛好者'],
            'article': ['博客作者', '記者', '內容創作者', '營銷人員'],
            'script': ['編劇', '導演', '戲劇創作者', '影視工作者'],
            'academic': ['研究人員', '學者', '研究生', '博士生'],
            'technical': ['技術寫作者', '產品經理', '開發者', 'UX設計師'],
            'creative': ['詩人', '散文家', '創意寫作者', '藝術家']
        };
        return audiences[category] || ['創作者'];
    }

    /**
     * 獲取模板預覽信息
     */
    static getTemplatePreview(id: string): {
        id: string;
        name: string;
        description: string;
        category: string;
        stageCount: number;
        estimatedDuration: string;
        complexity: 'Simple' | 'Moderate' | 'Complex';
        suitableFor: string[];
    } | null {
        const template = this.getTemplateById(id);
        if (!template) return null;

        const stageCount = template.stages.length;
        const complexity = stageCount <= 3 ? 'Simple' : stageCount <= 5 ? 'Moderate' : 'Complex';

        const totalWeeks = template.stages.reduce((total, stage) => {
            const duration = stage.estimatedDuration;
            const weeks = this.parseDuration(duration);
            return total + weeks;
        }, 0);

        const estimatedDuration = totalWeeks <= 4 ? `${totalWeeks} weeks` :
                                 totalWeeks <= 12 ? `${Math.ceil(totalWeeks/4)} months` :
                                 `${Math.ceil(totalWeeks/12)} quarters`;

        const suitableFor = this.getSuitableAudience(template.category);

        return {
            id: template.id,
            name: template.name,
            description: template.description,
            category: template.category,
            stageCount,
            estimatedDuration,
            complexity,
            suitableFor
        };
    }

    // 🔮 預留自定義模板註冊接口
    private static customTemplates: Map<string, WorkflowTemplate> = new Map();

    /**
     * 註冊自定義模板
     */
    static registerCustomTemplate(template: WorkflowTemplate): void {
        this.customTemplates.set(template.id, template);
    }

    /**
     * 獲取自定義模板
     */
    static getCustomTemplate(id: string): WorkflowTemplate | null {
        return this.customTemplates.get(id) || null;
    }

    /**
     * 獲取所有模板（包括自定義）
     */
    static getAllTemplatesIncludingCustom(): WorkflowTemplate[] {
        return [
            ...this.getAllTemplates(),
            ...Array.from(this.customTemplates.values())
        ];
    }

    /**
     * 模板版本控制和升級機制
     */
    static getTemplateVersions(): Record<string, string[]> {
        return {
            'novel_standard': ['v1.0.0'],
            'article_standard': ['v1.0.0'],
            'script_standard': ['v1.0.0'],
            'academic_standard': ['v1.0.0'],
            'technical_standard': ['v1.0.0'],
            'creative_standard': ['v1.0.0']
        };
    }

    /**
     * 檢查模板是否需要升級
     */
    static checkForUpgrade(templateId: string, currentVersion: string): {
        needsUpgrade: boolean;
        latestVersion: string;
        upgradeNotes?: string;
    } {
        const template = this.getTemplateById(templateId);
        if (!template) {
            return { needsUpgrade: false, latestVersion: currentVersion };
        }

        const latestVersion = template.version;
        const needsUpgrade = this.compareVersions(currentVersion, latestVersion) < 0;

        return {
            needsUpgrade,
            latestVersion,
            upgradeNotes: needsUpgrade ? this.getUpgradeNotes(templateId, currentVersion, latestVersion) : undefined
        };
    }

    /**
     * 比較版本號
     */
    private static compareVersions(version1: string, version2: string): number {
        const v1Parts = version1.split('.').map(Number);
        const v2Parts = version2.split('.').map(Number);

        for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
            const v1Part = v1Parts[i] || 0;
            const v2Part = v2Parts[i] || 0;

            if (v1Part < v2Part) return -1;
            if (v1Part > v2Part) return 1;
        }

        return 0;
    }

    /**
     * 獲取升級說明
     */
    private static getUpgradeNotes(templateId: string, fromVersion: string, toVersion: string): string {
        return `模板 ${templateId} 從版本 ${fromVersion} 升級到 ${toVersion}。建議檢查新的階段要求和完成標準。`;
    }

}
