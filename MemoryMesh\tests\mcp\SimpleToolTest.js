// tests/mcp/SimpleToolTest.js
// 簡單工具測試

console.log('🚀 開始簡單工具測試...');

async function testWorkflowTools() {
    try {
        console.log('📦 導入模塊...');
        
        // 嘗試導入工作流程工具
        const workflowToolsModule = await import('../../dist/integration/tools/registry/workflowTools.js');
        console.log('✅ 工作流程工具模塊導入成功');
        
        // 檢查工具定義
        const tools = workflowToolsModule.getAllWorkflowToolsIncludingCustom();
        console.log(`✅ 發現 ${tools.length} 個工作流程工具:`);
        
        tools.forEach(tool => {
            console.log(`  - ${tool.name}: ${tool.description}`);
        });

        // 嘗試導入存儲模塊
        const storageModule = await import('../../dist/infrastructure/storage/CachedJsonLineStorage.js');
        console.log('✅ 存儲模塊導入成功');
        
        const storage = new storageModule.CachedJsonLineStorage();
        console.log('✅ 存儲實例創建成功');

        // 嘗試導入應用管理器
        const appManagerModule = await import('../../dist/application/managers/ApplicationManager.js');
        console.log('✅ 應用管理器模塊導入成功');
        
        const appManager = new appManagerModule.ApplicationManager(storage);
        console.log('✅ 應用管理器實例創建成功');

        // 嘗試導入工作流程處理器
        const handlerModule = await import('../../dist/integration/tools/handlers/WorkflowToolHandler.js');
        console.log('✅ 工作流程處理器模塊導入成功');
        
        const handler = new handlerModule.WorkflowToolHandler(appManager, storage);
        console.log('✅ 工作流程處理器實例創建成功');

        // 測試基本功能
        console.log('\n🧪 測試基本功能...');

        // 測試1: 獲取模板
        console.log('\n=== 測試1: 獲取工作流程模板 ===');
        try {
            const templatesResult = await handler.handleTool('workflow_templates', {});
            console.log('✅ workflow_templates 調用成功');
            console.log('📄 完整結果:', JSON.stringify(templatesResult, null, 2));
            if (templatesResult && templatesResult.toolResult) {
                console.log('📄 工具結果:', JSON.stringify(templatesResult.toolResult, null, 2));
            }
        } catch (error) {
            console.log('❌ workflow_templates 調用失敗:', error.message);
            console.log('錯誤堆棧:', error.stack);
        }

        // 測試2: 創建工作流程
        console.log('\n=== 測試2: 創建工作流程 ===');
        try {
            const createResult = await handler.handleTool('workflow_create', {
                name: `簡單測試項目_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                type: 'novel'
            });
            console.log('✅ workflow_create 調用成功');
            console.log('📄 完整結果:', JSON.stringify(createResult, null, 2));
            if (createResult && createResult.toolResult) {
                console.log('📄 工具結果:', JSON.stringify(createResult.toolResult, null, 2));
            }
            
            // 提取工作流程ID
            let workflowId = null;
            if (createResult.toolResult && createResult.toolResult.data) {
                workflowId = createResult.toolResult.data.workflowId;
                console.log(`📝 工作流程ID: ${workflowId}`);
                
                // 測試3: 查看狀態
                console.log('\n=== 測試3: 查看工作流程狀態 ===');
                try {
                    const statusResult = await handler.handleTool('workflow_status', { workflowId });
                    console.log('✅ workflow_status 調用成功');
                    console.log('📄 結果:', JSON.stringify(statusResult.toolResult, null, 2));
                } catch (error) {
                    console.log('❌ workflow_status 調用失敗:', error.message);
                }

                // 測試4: 列出工作流程
                console.log('\n=== 測試4: 列出工作流程 ===');
                try {
                    const listResult = await handler.handleTool('workflow_list', {});
                    console.log('✅ workflow_list 調用成功');
                    console.log('📄 結果:', JSON.stringify(listResult.toolResult, null, 2));
                } catch (error) {
                    console.log('❌ workflow_list 調用失敗:', error.message);
                }

                // 測試5: 驗證階段
                console.log('\n=== 測試5: 驗證階段 ===');
                try {
                    const validateResult = await handler.handleTool('stage_validate', { workflowId });
                    console.log('✅ stage_validate 調用成功');
                    console.log('📄 結果:', JSON.stringify(validateResult.toolResult, null, 2));
                } catch (error) {
                    console.log('❌ stage_validate 調用失敗:', error.message);
                }

                // 測試6: 強制推進階段
                console.log('\n=== 測試6: 強制推進階段 ===');
                try {
                    const advanceResult = await handler.handleTool('workflow_advance', { 
                        workflowId, 
                        force: true 
                    });
                    console.log('✅ workflow_advance 調用成功');
                    console.log('📄 結果:', JSON.stringify(advanceResult.toolResult, null, 2));
                } catch (error) {
                    console.log('❌ workflow_advance 調用失敗:', error.message);
                }

                // 測試7: 暫停工作流程
                console.log('\n=== 測試7: 暫停工作流程 ===');
                try {
                    const pauseResult = await handler.handleTool('workflow_pause', { 
                        workflowId,
                        reason: '測試暫停功能'
                    });
                    console.log('✅ workflow_pause 調用成功');
                    console.log('📄 結果:', JSON.stringify(pauseResult.toolResult, null, 2));
                } catch (error) {
                    console.log('❌ workflow_pause 調用失敗:', error.message);
                }

                // 測試8: 恢復工作流程
                console.log('\n=== 測試8: 恢復工作流程 ===');
                try {
                    const resumeResult = await handler.handleTool('workflow_resume', { 
                        workflowId,
                        notes: '測試恢復功能'
                    });
                    console.log('✅ workflow_resume 調用成功');
                    console.log('📄 結果:', JSON.stringify(resumeResult.toolResult, null, 2));
                } catch (error) {
                    console.log('❌ workflow_resume 調用失敗:', error.message);
                }
            }
        } catch (error) {
            console.log('❌ workflow_create 調用失敗:', error.message);
        }

        // 測試錯誤處理
        console.log('\n❌ 測試錯誤處理...');

        // 測試9: 不存在的工作流程
        console.log('\n=== 測試9: 不存在的工作流程 ===');
        try {
            const errorResult = await handler.handleTool('workflow_status', {
                workflowId: 'non-existent-123'
            });
            console.log('📄 完整錯誤結果:', JSON.stringify(errorResult, null, 2));
        } catch (error) {
            console.log('❌ 錯誤處理測試失敗:', error.message);
        }

        // 測試10: 無效參數
        console.log('\n=== 測試10: 無效參數 ===');
        try {
            const invalidResult = await handler.handleTool('workflow_create', {
                // 缺少name參數
                type: 'novel'
            });
            console.log('📄 完整無效參數結果:', JSON.stringify(invalidResult, null, 2));
        } catch (error) {
            console.log('❌ 無效參數測試失敗:', error.message);
        }

        // 清理
        console.log('\n🧹 清理資源...');
        if (storage && typeof storage.cleanup === 'function') {
            storage.cleanup();
            console.log('✅ 存儲已清理');
        }

        console.log('\n🎉 簡單工具測試完成！');
        return true;

    } catch (error) {
        console.error('❌ 測試過程中發生錯誤:', error);
        console.error('錯誤堆棧:', error.stack);
        return false;
    }
}

// 運行測試
testWorkflowTools().then(success => {
    if (success) {
        console.log('\n✅ 所有測試完成');
        process.exit(0);
    } else {
        console.log('\n❌ 測試失敗');
        process.exit(1);
    }
}).catch(error => {
    console.error('❌ 未捕獲的錯誤:', error);
    process.exit(1);
});
