// src/core/workflow/WorkflowStateManager.ts

import type { Node, Edge } from '@core/index.js';
import type { IExtendedStorage, WorkflowState, StageState } from '../../infrastructure/storage/IExtendedStorage.js';
import type { WorkflowTemplate } from './WorkflowTemplates.js';
import { WorkflowNodeGenerator } from './WorkflowTemplates.js';
import { EventEmitter } from 'events';

/**
 * 工作流程狀態變更事件
 */
export interface WorkflowStateChangeEvent {
    workflowId: string;
    previousState: string;
    newState: string;
    timestamp: string;
    metadata?: Record<string, any>;
}

/**
 * 階段狀態變更事件
 */
export interface StageStateChangeEvent {
    workflowId: string;
    stageId: string;
    previousState: string;
    newState: string;
    timestamp: string;
    metadata?: Record<string, any>;
}

/**
 * 工作流程驗證結果
 */
export interface WorkflowValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    suggestions: string[];
    // 🔮 預留驗證結果擴展
    extensions?: Record<string, any>;
}

/**
 * 下一步指導接口
 */
export interface NextStepGuidance {
    immediateActions: string[];      // 立即應該做的事
    suggestedTools: string[];        // 推薦使用的工具
    contentExamples: string[];       // 內容示例
    qualityTips: string[];          // 質量提升建議
}

/**
 * 進度洞察接口
 */
export interface ProgressInsights {
    currentFocus: string;           // 當前階段重點
    upcomingChallenges: string[];   // 即將面臨的挑戰
    strengthAreas: string[];        // 已經做得好的方面
    improvementAreas: string[];     // 需要改進的方面
}

/**
 * 質量評估結果
 */
export interface QualityAssessment {
    overallScore: number;           // 整體質量分數 (0-100)
    passedChecks: string[];         // 通過的質量檢查
    failedChecks: string[];         // 未通過的質量檢查
    recommendations: string[];       // 改進建議
}

/**
 * 階段驗證結果 (增強版)
 */
export interface StageValidationResult {
    stageId: string;
    isComplete: boolean;
    requirements: {
        type: string;
        required: boolean;
        found: number;
        met: boolean;
    }[];
    missingRequirements: string[];

    // 🔮 v0.3.1 新增：智能指導
    nextStepGuidance?: NextStepGuidance;
    progressInsights?: ProgressInsights;
    qualityAssessment?: QualityAssessment;

    // 🔮 預留階段驗證擴展
    extensions?: Record<string, any>;
}

/**
 * 工作流程狀態管理器
 * 負責管理工作流程的狀態變更、驗證和事件處理
 */
export class WorkflowStateManager extends EventEmitter {
    private storage: IExtendedStorage;

    // 🔮 預留狀態管理擴展
    private stateValidators: Map<string, (state: WorkflowState) => Promise<boolean>> = new Map();
    private stateTransitions: Map<string, string[]> = new Map();

    // 🔧 性能監控
    private activeWorkflows: Set<string> = new Set();
    private eventListenerCount = 0;
    private maxEventListeners = 100;

    // 🔒 併發控制
    private operationLocks: Map<string, Promise<any>> = new Map();
    private pendingOperations: Map<string, number> = new Map();

    constructor(storage: IExtendedStorage) {
        super();
        this.storage = storage;
        this.initializeStateTransitions();

        // 設置最大監聽器數量以防止內存洩漏
        this.setMaxListeners(this.maxEventListeners);

        // 監控事件監聽器數量
        this.on('newListener', () => {
            this.eventListenerCount++;
            if (this.eventListenerCount > this.maxEventListeners * 0.8) {
                console.warn('[WorkflowStateManager] High number of event listeners detected:', this.eventListenerCount);
            }
        });

        this.on('removeListener', () => {
            this.eventListenerCount--;
        });
    }

    // === 工作流程生命週期管理 ===

    /**
     * 創建新的工作流程
     */
    async createWorkflow(
        template: WorkflowTemplate,
        name: string,
        customMetadata?: Record<string, any>
    ): Promise<{ workflowId: string; nodes: Node[]; edges: any[] }> {
        const workflowId = this.generateWorkflowId();
        
        try {
            // 創建工作流程節點
            const workflowNode = WorkflowNodeGenerator.createWorkflowNode(
                template, 
                workflowId, 
                customMetadata
            );
            
            // 創建階段節點
            const stageNodes = WorkflowNodeGenerator.createStageNodes(template, workflowId);
            
            // 創建依賴邊
            const dependencyEdges = WorkflowNodeGenerator.createStageDependencyEdges(template, workflowId);
            
            // 創建工作流程狀態
            const workflowState: WorkflowState = {
                workflowId,
                name,
                status: 'not_started',
                currentStage: 0,
                totalStages: template.stages.length,
                progress: 0,
                metadata: {
                    templateId: template.id,
                    templateVersion: template.version,
                    category: template.category,
                    ...customMetadata
                },
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            // 創建階段狀態
            const stageStates: StageState[] = template.stages.map((stage, index) => ({
                stageId: `${workflowId}_stage_${stage.id}`,
                workflowId,
                name: stage.name,
                order: index,
                status: index === 0 ? 'active' : 'pending',
                requiredNodeTypes: stage.requiredNodeTypes,
                completionCriteria: stage.completionCriteria
            }));
            
            // 保存到存儲
            await this.storage.saveWorkflowState(workflowState);
            for (const stageState of stageStates) {
                await this.storage.saveStageState(stageState);
            }
            
            // 發送創建事件
            this.emit('workflow_created', {
                workflowId,
                template: template.id,
                timestamp: new Date().toISOString()
            });
            
            return {
                workflowId,
                nodes: [workflowNode, ...stageNodes],
                edges: dependencyEdges
            };
            
        } catch (error) {
            this.emit('workflow_creation_failed', {
                workflowId,
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            });
            throw error;
        }
    }

    /**
     * 更新工作流程狀態（併發安全）
     */
    async updateWorkflowState(
        workflowId: string,
        updates: Partial<WorkflowState>
    ): Promise<WorkflowState> {
        return this.withLock(workflowId, async () => {
            const currentState = await this.storage.loadWorkflowState(workflowId);
            if (!currentState) {
                throw new Error(`Workflow not found: ${workflowId}`);
            }

            const previousStatus = currentState.status;
            const updatedState: WorkflowState = {
                ...currentState,
                ...updates,
                updatedAt: new Date().toISOString()
            };

            // 驗證狀態轉換
            if (updates.status && !this.isValidStateTransition(previousStatus, updates.status)) {
                throw new Error(`Invalid state transition from ${previousStatus} to ${updates.status}`);
            }

            await this.storage.saveWorkflowState(updatedState);

            // 發送狀態變更事件
            if (updates.status && updates.status !== previousStatus) {
                const event: WorkflowStateChangeEvent = {
                    workflowId,
                    previousState: previousStatus,
                    newState: updates.status,
                    timestamp: new Date().toISOString(),
                    metadata: updates.metadata
                };
                this.emit('workflow_state_changed', event);
            }

            return updatedState;
        });
    }

    /**
     * 推進工作流程到下一階段（併發安全）
     */
    async advanceToNextStage(
        workflowId: string,
        options: { force?: boolean; skipValidation?: boolean } = {}
    ): Promise<{ workflow: WorkflowState; nextStage?: StageState }> {
        return this.withLock(`advance_${workflowId}`, async () => {
        const workflow = await this.storage.loadWorkflowState(workflowId);
        if (!workflow) {
            throw new Error(`Workflow not found: ${workflowId}`);
        }
        
        if (workflow.status === 'completed') {
            throw new Error('Workflow is already completed');
        }
        
        const stages = await this.storage.loadStageStates(workflowId);
        const currentStage = stages.find(s => s.order === workflow.currentStage);
        
        if (!currentStage) {
            throw new Error(`Current stage not found for workflow: ${workflowId}`);
        }
        
        // 驗證當前階段是否可以完成
        if (!options.skipValidation && !options.force) {
            const validation = await this.validateStage(workflowId, currentStage.stageId);
            if (!validation.isComplete) {
                throw new Error(`Current stage "${currentStage.name}" is not complete. Missing: ${validation.missingRequirements.join(', ')}`);
            }
        }
        
        // 完成當前階段
        currentStage.status = 'completed';
        await this.storage.saveStageState(currentStage);
        
        // 推進到下一階段
        let nextStage: StageState | undefined;
        if (workflow.currentStage < workflow.totalStages - 1) {
            workflow.currentStage += 1;
            workflow.progress = Math.round((workflow.currentStage / workflow.totalStages) * 100);
            
            nextStage = stages.find(s => s.order === workflow.currentStage);
            if (nextStage) {
                nextStage.status = 'active';
                await this.storage.saveStageState(nextStage);
            }
        } else {
            // 工作流程完成
            workflow.status = 'completed';
            workflow.progress = 100;
        }
        
        workflow.updatedAt = new Date().toISOString();
        await this.storage.saveWorkflowState(workflow);
        
        // 發送階段變更事件
        const stageEvent: StageStateChangeEvent = {
            workflowId,
            stageId: currentStage.stageId,
            previousState: 'active',
            newState: 'completed',
            timestamp: new Date().toISOString()
        };
        this.emit('stage_state_changed', stageEvent);
        
        if (workflow.status === 'completed') {
            this.emit('workflow_completed', {
                workflowId,
                timestamp: new Date().toISOString()
            });
        }
        
        return { workflow, nextStage };
        });
    }

    // === 驗證功能 ===

    /**
     * 驗證工作流程
     */
    async validateWorkflow(workflowId: string): Promise<WorkflowValidationResult> {
        const workflow = await this.storage.loadWorkflowState(workflowId);
        if (!workflow) {
            return {
                isValid: false,
                errors: [`Workflow not found: ${workflowId}`],
                warnings: [],
                suggestions: []
            };
        }
        
        const stages = await this.storage.loadStageStates(workflowId);
        const errors: string[] = [];
        const warnings: string[] = [];
        const suggestions: string[] = [];
        
        // 驗證階段完整性
        if (stages.length !== workflow.totalStages) {
            errors.push(`Stage count mismatch: expected ${workflow.totalStages}, found ${stages.length}`);
        }
        
        // 驗證當前階段狀態
        const currentStage = stages.find(s => s.order === workflow.currentStage);
        if (!currentStage) {
            errors.push(`Current stage not found: order ${workflow.currentStage}`);
        } else if (currentStage.status !== 'active' && workflow.status !== 'completed') {
            warnings.push(`Current stage "${currentStage.name}" is not active`);
        }
        
        // 驗證進度一致性
        const expectedProgress = Math.round((workflow.currentStage / workflow.totalStages) * 100);
        if (Math.abs(workflow.progress - expectedProgress) > 5) {
            warnings.push(`Progress inconsistency: expected ~${expectedProgress}%, found ${workflow.progress}%`);
        }
        
        // 提供改進建議
        if (workflow.status === 'not_started' && workflow.currentStage === 0) {
            suggestions.push('Consider starting the workflow by advancing to the first stage');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            suggestions
        };
    }

    /**
     * 驗證階段完成條件 (增強版)
     */
    async validateStage(workflowId: string, stageId: string): Promise<StageValidationResult> {
        const stages = await this.storage.loadStageStates(workflowId);
        const stage = stages.find(s => s.stageId === stageId);

        if (!stage) {
            throw new Error(`Stage not found: ${stageId}`);
        }

        // 獲取工作流程相關的節點
        const workflowNodes = await this.storage.queryNodesByWorkflow(workflowId);

        // 檢查每個必需的節點類型
        const requirements = stage.requiredNodeTypes.map(nodeType => {
            const nodesOfType = workflowNodes.filter(node => node.nodeType === nodeType);
            return {
                type: nodeType,
                required: true,
                found: nodesOfType.length,
                met: nodesOfType.length >= (stage.completionCriteria.minNodes || 1)
            };
        });

        const missingRequirements = requirements
            .filter(req => !req.met)
            .map(req => `${req.type} (need ${stage.completionCriteria.minNodes || 1}, found ${req.found})`);

        const isComplete = missingRequirements.length === 0;

        // 生成智能指導
        const nextStepGuidance = this.generateNextStepGuidance(stage, requirements, workflowNodes);
        const progressInsights = this.generateProgressInsights(stage, workflowNodes, stages);
        const qualityAssessment = this.generateQualityAssessment(stage, workflowNodes);

        return {
            stageId,
            isComplete,
            requirements,
            missingRequirements,
            nextStepGuidance,
            progressInsights,
            qualityAssessment
        };
    }

    // === 🔮 預留擴展接口 ===

    /**
     * 註冊自定義狀態驗證器
     */
    registerStateValidator(
        stateName: string, 
        validator: (state: WorkflowState) => Promise<boolean>
    ): void {
        this.stateValidators.set(stateName, validator);
    }

    /**
     * 註冊自定義狀態轉換規則
     */
    registerStateTransition(fromState: string, toStates: string[]): void {
        this.stateTransitions.set(fromState, toStates);
    }

    // === 智能指導生成方法 ===

    /**
     * 生成下一步指導
     */
    private generateNextStepGuidance(stage: any, requirements: any[], workflowNodes: any[]): NextStepGuidance {
        const immediateActions: string[] = [];
        const suggestedTools: string[] = [];
        const contentExamples: string[] = [];
        const qualityTips: string[] = [];

        // 根據階段類型和缺失要求生成指導
        const missingTypes = requirements.filter(req => !req.met).map(req => req.type);

        for (const nodeType of missingTypes) {
            switch (nodeType) {
                case 'character':
                    immediateActions.push('創建主要角色，包含動機、衝突和成長弧線');
                    suggestedTools.push('add_character');
                    contentExamples.push('主角：年輕騎士，目標是拯救王國，內在需求是證明自己的價值');
                    qualityTips.push('確保每個角色都有獨特的聲音和行為模式');
                    break;
                case 'setting':
                    immediateActions.push('建立故事背景設定，包含環境描述和文化背景');
                    suggestedTools.push('add_setting');
                    contentExamples.push('古老的圖書館：塵埃在陽光中飛舞，羊皮紙的味道混合著蠟燭的香氣');
                    qualityTips.push('使用五感描述來增強沉浸感');
                    break;
                case 'theme':
                    immediateActions.push('確定故事主題，確保能通過情節和角色自然體現');
                    suggestedTools.push('add_nodes');
                    contentExamples.push('成長主題：通過主角面對挑戰、犯錯、學習的過程來體現');
                    qualityTips.push('避免直接說教，讓主題通過故事自然流露');
                    break;
                case 'plotarc':
                    immediateActions.push('構建完整的情節弧線，包含起承轉合');
                    suggestedTools.push('add_nodes');
                    contentExamples.push('三幕結構：建立(25%) → 對抗(50%) → 解決(25%)');
                    qualityTips.push('確保每個情節點都推動故事前進');
                    break;
                case 'scene':
                    immediateActions.push('創建具體的場景，平衡行動、對話和描述');
                    suggestedTools.push('add_nodes');
                    contentExamples.push('劍鬥場景：快節奏，短句，感官細節，情緒張力');
                    qualityTips.push('每個場景都要有明確的目的和衝突');
                    break;
                case 'chapter':
                    immediateActions.push('完成章節內容，確保有開頭鉤子和結尾懸念');
                    suggestedTools.push('add_nodes');
                    contentExamples.push('完整章節：開頭引人入勝，中段發展充實，結尾留有懸念');
                    qualityTips.push('保持敘述聲音的一致性');
                    break;
            }
        }

        // 如果沒有缺失項目，提供質量提升建議
        if (missingTypes.length === 0) {
            immediateActions.push('檢查現有內容的質量，進行必要的改進和完善');
            suggestedTools.push('update_nodes');
            qualityTips.push('審視內容的一致性、深度和表現力');
        }

        return {
            immediateActions,
            suggestedTools,
            contentExamples,
            qualityTips
        };
    }

    /**
     * 生成進度洞察
     */
    private generateProgressInsights(stage: any, workflowNodes: any[], allStages: any[]): ProgressInsights {
        const currentStageIndex = stage.order;
        const totalStages = allStages.length;
        const completedNodes = workflowNodes.length;

        // 確定當前重點
        let currentFocus = '';
        switch (stage.id) {
            case 'planning':
                currentFocus = '建立故事基礎：角色、設定和主題的深度開發';
                break;
            case 'outline':
                currentFocus = '構建故事結構：情節弧線和時間線的邏輯安排';
                break;
            case 'chapter':
                currentFocus = '創作具體內容：場景描寫和章節組織的藝術平衡';
                break;
            case 'generation':
                currentFocus = '完善最終作品：整體一致性和文學品質的提升';
                break;
            default:
                currentFocus = '推進當前階段的核心任務';
        }

        // 預測即將面臨的挑戰
        const upcomingChallenges: string[] = [];
        if (currentStageIndex < totalStages - 1) {
            const nextStage = allStages.find(s => s.order === currentStageIndex + 1);
            if (nextStage) {
                switch (nextStage.id) {
                    case 'outline':
                        upcomingChallenges.push('將角色和設定整合到連貫的情節中');
                        upcomingChallenges.push('平衡主線和支線的發展');
                        break;
                    case 'chapter':
                        upcomingChallenges.push('將大綱轉化為生動的場景');
                        upcomingChallenges.push('保持章節間的節奏和連貫性');
                        break;
                    case 'generation':
                        upcomingChallenges.push('確保整體敘述風格的一致性');
                        upcomingChallenges.push('平衡描述、對話和行動的比例');
                        break;
                }
            }
        }

        // 識別優勢領域
        const strengthAreas: string[] = [];
        const nodeTypes = [...new Set(workflowNodes.map(node => node.nodeType))];

        if (nodeTypes.includes('character') && workflowNodes.filter(n => n.nodeType === 'character').length >= 2) {
            strengthAreas.push('角色發展：已建立多個角色，為故事提供豐富的人物基礎');
        }
        if (nodeTypes.includes('setting') && workflowNodes.filter(n => n.nodeType === 'setting').length >= 1) {
            strengthAreas.push('世界建構：設定描述為故事提供了生動的背景');
        }
        if (completedNodes >= 5) {
            strengthAreas.push('內容豐富度：已創建充足的故事元素，為後續發展奠定基礎');
        }

        // 識別改進領域
        const improvementAreas: string[] = [];
        if (nodeTypes.length < 3) {
            improvementAreas.push('內容多樣性：考慮添加更多類型的故事元素');
        }
        if (completedNodes < stage.completionCriteria.minNodes) {
            improvementAreas.push('內容數量：需要創建更多內容以滿足階段要求');
        }

        return {
            currentFocus,
            upcomingChallenges,
            strengthAreas,
            improvementAreas
        };
    }

    /**
     * 生成質量評估
     */
    private generateQualityAssessment(stage: any, workflowNodes: any[]): QualityAssessment {
        let overallScore = 60; // 基礎分數
        const passedChecks: string[] = [];
        const failedChecks: string[] = [];
        const recommendations: string[] = [];

        // 基於節點數量的評估
        const nodeCount = workflowNodes.length;
        const minNodes = stage.completionCriteria.minNodes || 1;

        if (nodeCount >= minNodes) {
            overallScore += 20;
            passedChecks.push('節點數量要求已滿足');
        } else {
            failedChecks.push(`節點數量不足：需要${minNodes}個，目前${nodeCount}個`);
            recommendations.push(`添加${minNodes - nodeCount}個必需的內容節點`);
        }

        // 基於內容多樣性的評估
        const nodeTypes = [...new Set(workflowNodes.map(node => node.nodeType))];
        const requiredTypes = stage.requiredNodeTypes.length;

        if (nodeTypes.length >= requiredTypes) {
            overallScore += 15;
            passedChecks.push('內容類型多樣性良好');
        } else {
            failedChecks.push('內容類型不夠多樣化');
            recommendations.push('添加更多類型的故事元素以豐富內容');
        }

        // 基於階段特定的質量檢查
        if (stage.completionCriteria.qualityChecks) {
            const qualityChecks = stage.completionCriteria.qualityChecks;
            let qualityScore = 0;

            for (const check of qualityChecks) {
                // 這裡是簡化的質量檢查，實際實現中會調用具體的驗證器
                const passed = this.simulateQualityCheck(check.type, workflowNodes);
                if (passed) {
                    passedChecks.push(check.description);
                    qualityScore += check.weight * 100;
                } else {
                    failedChecks.push(check.description);
                    recommendations.push(`改進${check.type}：${check.description}`);
                }
            }

            overallScore = Math.min(100, overallScore + qualityScore * 0.05);
        }

        return {
            overallScore: Math.round(overallScore),
            passedChecks,
            failedChecks,
            recommendations
        };
    }

    /**
     * 模擬質量檢查（簡化版本）
     */
    private simulateQualityCheck(checkType: string, workflowNodes: any[]): boolean {
        // 這是一個簡化的實現，實際中會有更複雜的邏輯
        switch (checkType) {
            case 'character_depth':
                return workflowNodes.filter(n => n.nodeType === 'character').length >= 1;
            case 'setting_richness':
                return workflowNodes.filter(n => n.nodeType === 'setting').length >= 1;
            case 'theme_clarity':
                return workflowNodes.filter(n => n.nodeType === 'theme').length >= 1;
            case 'plot_structure':
                return workflowNodes.filter(n => n.nodeType === 'plotarc').length >= 1;
            case 'scene_depth':
                return workflowNodes.filter(n => n.nodeType === 'scene').length >= 3;
            default:
                return true;
        }
    }

    // === 私有輔助方法 ===

    private generateWorkflowId(): string {
        return `wf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private initializeStateTransitions(): void {
        // 定義默認的狀態轉換規則
        this.stateTransitions.set('not_started', ['in_progress', 'paused']);
        this.stateTransitions.set('in_progress', ['paused', 'completed']);
        this.stateTransitions.set('paused', ['in_progress']);
        this.stateTransitions.set('completed', []); // 完成狀態不能轉換
    }

    private isValidStateTransition(fromState: string, toState: string): boolean {
        const allowedTransitions = this.stateTransitions.get(fromState) || [];
        return allowedTransitions.includes(toState);
    }

    /**
     * 併發安全的操作鎖機制
     */
    private async withLock<T>(key: string, operation: () => Promise<T>): Promise<T> {
        // 檢查是否有正在進行的操作
        const existingLock = this.operationLocks.get(key);
        if (existingLock) {
            // 等待現有操作完成
            try {
                await existingLock;
            } catch (error) {
                // 忽略其他操作的錯誤，繼續執行當前操作
            }
        }

        // 記錄待處理操作數量
        const pendingCount = this.pendingOperations.get(key) || 0;
        this.pendingOperations.set(key, pendingCount + 1);

        // 如果有太多待處理操作，拋出錯誤
        if (pendingCount > 10) {
            this.pendingOperations.set(key, pendingCount - 1);
            throw new Error(`Too many concurrent operations for workflow: ${key}`);
        }

        // 創建新的操作Promise
        const operationPromise = (async () => {
            try {
                return await operation();
            } finally {
                // 清理
                this.operationLocks.delete(key);
                const currentPending = this.pendingOperations.get(key) || 1;
                if (currentPending <= 1) {
                    this.pendingOperations.delete(key);
                } else {
                    this.pendingOperations.set(key, currentPending - 1);
                }
            }
        })();

        this.operationLocks.set(key, operationPromise);
        return operationPromise;
    }

    // === 🔧 資源管理方法 ===

    /**
     * 清理已完成的工作流程資源
     */
    public async cleanupCompletedWorkflows(): Promise<void> {
        const workflows = await this.storage.listWorkflows();
        const completedWorkflows = workflows.filter(w => w.status === 'completed');

        for (const workflow of completedWorkflows) {
            this.activeWorkflows.delete(workflow.workflowId);

            // 移除該工作流程的所有事件監聽器
            const eventNames = this.eventNames();
            for (const eventName of eventNames) {
                const listeners = this.listeners(eventName) as ((...args: any[]) => void)[];
                for (const listener of listeners) {
                    // 檢查監聽器是否與該工作流程相關
                    if (listener.toString().includes(workflow.workflowId)) {
                        this.removeListener(eventName, listener);
                    }
                }
            }
        }
    }

    /**
     * 獲取性能統計信息
     */
    public getPerformanceStats(): {
        activeWorkflows: number;
        eventListeners: number;
        stateValidators: number;
        stateTransitions: number;
        memoryUsage: NodeJS.MemoryUsage;
    } {
        return {
            activeWorkflows: this.activeWorkflows.size,
            eventListeners: this.eventListenerCount,
            stateValidators: this.stateValidators.size,
            stateTransitions: this.stateTransitions.size,
            memoryUsage: process.memoryUsage()
        };
    }

    /**
     * 清理所有資源
     */
    public cleanup(): void {
        this.activeWorkflows.clear();
        this.stateValidators.clear();
        this.stateTransitions.clear();
        this.removeAllListeners();
        this.eventListenerCount = 0;
    }
}
