// src/core/workflow/WorkflowStateManager.ts

import type { Node, Edge } from '@core/index.js';
import type { IExtendedStorage, WorkflowState, StageState } from '../../infrastructure/storage/IExtendedStorage.js';
import type { WorkflowTemplate } from './WorkflowTemplates.js';
import { WorkflowNodeGenerator } from './WorkflowTemplates.js';
import { EventEmitter } from 'events';

/**
 * 工作流程狀態變更事件
 */
export interface WorkflowStateChangeEvent {
    workflowId: string;
    previousState: string;
    newState: string;
    timestamp: string;
    metadata?: Record<string, any>;
}

/**
 * 階段狀態變更事件
 */
export interface StageStateChangeEvent {
    workflowId: string;
    stageId: string;
    previousState: string;
    newState: string;
    timestamp: string;
    metadata?: Record<string, any>;
}

/**
 * 工作流程驗證結果
 */
export interface WorkflowValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    suggestions: string[];
    // 🔮 預留驗證結果擴展
    extensions?: Record<string, any>;
}

/**
 * 階段驗證結果
 */
export interface StageValidationResult {
    stageId: string;
    isComplete: boolean;
    requirements: {
        type: string;
        required: boolean;
        found: number;
        met: boolean;
    }[];
    missingRequirements: string[];
    // 🔮 預留階段驗證擴展
    extensions?: Record<string, any>;
}

/**
 * 工作流程狀態管理器
 * 負責管理工作流程的狀態變更、驗證和事件處理
 */
export class WorkflowStateManager extends EventEmitter {
    private storage: IExtendedStorage;

    // 🔮 預留狀態管理擴展
    private stateValidators: Map<string, (state: WorkflowState) => Promise<boolean>> = new Map();
    private stateTransitions: Map<string, string[]> = new Map();

    // 🔧 性能監控
    private activeWorkflows: Set<string> = new Set();
    private eventListenerCount = 0;
    private maxEventListeners = 100;

    // 🔒 併發控制
    private operationLocks: Map<string, Promise<any>> = new Map();
    private pendingOperations: Map<string, number> = new Map();

    constructor(storage: IExtendedStorage) {
        super();
        this.storage = storage;
        this.initializeStateTransitions();

        // 設置最大監聽器數量以防止內存洩漏
        this.setMaxListeners(this.maxEventListeners);

        // 監控事件監聽器數量
        this.on('newListener', () => {
            this.eventListenerCount++;
            if (this.eventListenerCount > this.maxEventListeners * 0.8) {
                console.warn('[WorkflowStateManager] High number of event listeners detected:', this.eventListenerCount);
            }
        });

        this.on('removeListener', () => {
            this.eventListenerCount--;
        });
    }

    // === 工作流程生命週期管理 ===

    /**
     * 創建新的工作流程
     */
    async createWorkflow(
        template: WorkflowTemplate,
        name: string,
        customMetadata?: Record<string, any>
    ): Promise<{ workflowId: string; nodes: Node[]; edges: any[] }> {
        const workflowId = this.generateWorkflowId();
        
        try {
            // 創建工作流程節點
            const workflowNode = WorkflowNodeGenerator.createWorkflowNode(
                template, 
                workflowId, 
                customMetadata
            );
            
            // 創建階段節點
            const stageNodes = WorkflowNodeGenerator.createStageNodes(template, workflowId);
            
            // 創建依賴邊
            const dependencyEdges = WorkflowNodeGenerator.createStageDependencyEdges(template, workflowId);
            
            // 創建工作流程狀態
            const workflowState: WorkflowState = {
                workflowId,
                name,
                status: 'not_started',
                currentStage: 0,
                totalStages: template.stages.length,
                progress: 0,
                metadata: {
                    templateId: template.id,
                    templateVersion: template.version,
                    category: template.category,
                    ...customMetadata
                },
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            // 創建階段狀態
            const stageStates: StageState[] = template.stages.map((stage, index) => ({
                stageId: `${workflowId}_stage_${stage.id}`,
                workflowId,
                name: stage.name,
                order: index,
                status: index === 0 ? 'active' : 'pending',
                requiredNodeTypes: stage.requiredNodeTypes,
                completionCriteria: stage.completionCriteria
            }));
            
            // 保存到存儲
            await this.storage.saveWorkflowState(workflowState);
            for (const stageState of stageStates) {
                await this.storage.saveStageState(stageState);
            }
            
            // 發送創建事件
            this.emit('workflow_created', {
                workflowId,
                template: template.id,
                timestamp: new Date().toISOString()
            });
            
            return {
                workflowId,
                nodes: [workflowNode, ...stageNodes],
                edges: dependencyEdges
            };
            
        } catch (error) {
            this.emit('workflow_creation_failed', {
                workflowId,
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            });
            throw error;
        }
    }

    /**
     * 更新工作流程狀態（併發安全）
     */
    async updateWorkflowState(
        workflowId: string,
        updates: Partial<WorkflowState>
    ): Promise<WorkflowState> {
        return this.withLock(workflowId, async () => {
            const currentState = await this.storage.loadWorkflowState(workflowId);
            if (!currentState) {
                throw new Error(`Workflow not found: ${workflowId}`);
            }

            const previousStatus = currentState.status;
            const updatedState: WorkflowState = {
                ...currentState,
                ...updates,
                updatedAt: new Date().toISOString()
            };

            // 驗證狀態轉換
            if (updates.status && !this.isValidStateTransition(previousStatus, updates.status)) {
                throw new Error(`Invalid state transition from ${previousStatus} to ${updates.status}`);
            }

            await this.storage.saveWorkflowState(updatedState);

            // 發送狀態變更事件
            if (updates.status && updates.status !== previousStatus) {
                const event: WorkflowStateChangeEvent = {
                    workflowId,
                    previousState: previousStatus,
                    newState: updates.status,
                    timestamp: new Date().toISOString(),
                    metadata: updates.metadata
                };
                this.emit('workflow_state_changed', event);
            }

            return updatedState;
        });
    }

    /**
     * 推進工作流程到下一階段（併發安全）
     */
    async advanceToNextStage(
        workflowId: string,
        options: { force?: boolean; skipValidation?: boolean } = {}
    ): Promise<{ workflow: WorkflowState; nextStage?: StageState }> {
        return this.withLock(`advance_${workflowId}`, async () => {
        const workflow = await this.storage.loadWorkflowState(workflowId);
        if (!workflow) {
            throw new Error(`Workflow not found: ${workflowId}`);
        }
        
        if (workflow.status === 'completed') {
            throw new Error('Workflow is already completed');
        }
        
        const stages = await this.storage.loadStageStates(workflowId);
        const currentStage = stages.find(s => s.order === workflow.currentStage);
        
        if (!currentStage) {
            throw new Error(`Current stage not found for workflow: ${workflowId}`);
        }
        
        // 驗證當前階段是否可以完成
        if (!options.skipValidation && !options.force) {
            const validation = await this.validateStage(workflowId, currentStage.stageId);
            if (!validation.isComplete) {
                throw new Error(`Current stage "${currentStage.name}" is not complete. Missing: ${validation.missingRequirements.join(', ')}`);
            }
        }
        
        // 完成當前階段
        currentStage.status = 'completed';
        await this.storage.saveStageState(currentStage);
        
        // 推進到下一階段
        let nextStage: StageState | undefined;
        if (workflow.currentStage < workflow.totalStages - 1) {
            workflow.currentStage += 1;
            workflow.progress = Math.round((workflow.currentStage / workflow.totalStages) * 100);
            
            nextStage = stages.find(s => s.order === workflow.currentStage);
            if (nextStage) {
                nextStage.status = 'active';
                await this.storage.saveStageState(nextStage);
            }
        } else {
            // 工作流程完成
            workflow.status = 'completed';
            workflow.progress = 100;
        }
        
        workflow.updatedAt = new Date().toISOString();
        await this.storage.saveWorkflowState(workflow);
        
        // 發送階段變更事件
        const stageEvent: StageStateChangeEvent = {
            workflowId,
            stageId: currentStage.stageId,
            previousState: 'active',
            newState: 'completed',
            timestamp: new Date().toISOString()
        };
        this.emit('stage_state_changed', stageEvent);
        
        if (workflow.status === 'completed') {
            this.emit('workflow_completed', {
                workflowId,
                timestamp: new Date().toISOString()
            });
        }
        
        return { workflow, nextStage };
        });
    }

    // === 驗證功能 ===

    /**
     * 驗證工作流程
     */
    async validateWorkflow(workflowId: string): Promise<WorkflowValidationResult> {
        const workflow = await this.storage.loadWorkflowState(workflowId);
        if (!workflow) {
            return {
                isValid: false,
                errors: [`Workflow not found: ${workflowId}`],
                warnings: [],
                suggestions: []
            };
        }
        
        const stages = await this.storage.loadStageStates(workflowId);
        const errors: string[] = [];
        const warnings: string[] = [];
        const suggestions: string[] = [];
        
        // 驗證階段完整性
        if (stages.length !== workflow.totalStages) {
            errors.push(`Stage count mismatch: expected ${workflow.totalStages}, found ${stages.length}`);
        }
        
        // 驗證當前階段狀態
        const currentStage = stages.find(s => s.order === workflow.currentStage);
        if (!currentStage) {
            errors.push(`Current stage not found: order ${workflow.currentStage}`);
        } else if (currentStage.status !== 'active' && workflow.status !== 'completed') {
            warnings.push(`Current stage "${currentStage.name}" is not active`);
        }
        
        // 驗證進度一致性
        const expectedProgress = Math.round((workflow.currentStage / workflow.totalStages) * 100);
        if (Math.abs(workflow.progress - expectedProgress) > 5) {
            warnings.push(`Progress inconsistency: expected ~${expectedProgress}%, found ${workflow.progress}%`);
        }
        
        // 提供改進建議
        if (workflow.status === 'not_started' && workflow.currentStage === 0) {
            suggestions.push('Consider starting the workflow by advancing to the first stage');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            suggestions
        };
    }

    /**
     * 驗證階段完成條件
     */
    async validateStage(workflowId: string, stageId: string): Promise<StageValidationResult> {
        const stages = await this.storage.loadStageStates(workflowId);
        const stage = stages.find(s => s.stageId === stageId);
        
        if (!stage) {
            throw new Error(`Stage not found: ${stageId}`);
        }
        
        // 獲取工作流程相關的節點
        const workflowNodes = await this.storage.queryNodesByWorkflow(workflowId);
        
        // 檢查每個必需的節點類型
        const requirements = stage.requiredNodeTypes.map(nodeType => {
            const nodesOfType = workflowNodes.filter(node => node.nodeType === nodeType);
            return {
                type: nodeType,
                required: true,
                found: nodesOfType.length,
                met: nodesOfType.length >= (stage.completionCriteria.minNodes || 1)
            };
        });
        
        const missingRequirements = requirements
            .filter(req => !req.met)
            .map(req => `${req.type} (need ${stage.completionCriteria.minNodes || 1}, found ${req.found})`);
        
        return {
            stageId,
            isComplete: missingRequirements.length === 0,
            requirements,
            missingRequirements
        };
    }

    // === 🔮 預留擴展接口 ===

    /**
     * 註冊自定義狀態驗證器
     */
    registerStateValidator(
        stateName: string, 
        validator: (state: WorkflowState) => Promise<boolean>
    ): void {
        this.stateValidators.set(stateName, validator);
    }

    /**
     * 註冊自定義狀態轉換規則
     */
    registerStateTransition(fromState: string, toStates: string[]): void {
        this.stateTransitions.set(fromState, toStates);
    }

    // === 私有輔助方法 ===

    private generateWorkflowId(): string {
        return `wf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private initializeStateTransitions(): void {
        // 定義默認的狀態轉換規則
        this.stateTransitions.set('not_started', ['in_progress', 'paused']);
        this.stateTransitions.set('in_progress', ['paused', 'completed']);
        this.stateTransitions.set('paused', ['in_progress']);
        this.stateTransitions.set('completed', []); // 完成狀態不能轉換
    }

    private isValidStateTransition(fromState: string, toState: string): boolean {
        const allowedTransitions = this.stateTransitions.get(fromState) || [];
        return allowedTransitions.includes(toState);
    }

    /**
     * 併發安全的操作鎖機制
     */
    private async withLock<T>(key: string, operation: () => Promise<T>): Promise<T> {
        // 檢查是否有正在進行的操作
        const existingLock = this.operationLocks.get(key);
        if (existingLock) {
            // 等待現有操作完成
            try {
                await existingLock;
            } catch (error) {
                // 忽略其他操作的錯誤，繼續執行當前操作
            }
        }

        // 記錄待處理操作數量
        const pendingCount = this.pendingOperations.get(key) || 0;
        this.pendingOperations.set(key, pendingCount + 1);

        // 如果有太多待處理操作，拋出錯誤
        if (pendingCount > 10) {
            this.pendingOperations.set(key, pendingCount - 1);
            throw new Error(`Too many concurrent operations for workflow: ${key}`);
        }

        // 創建新的操作Promise
        const operationPromise = (async () => {
            try {
                return await operation();
            } finally {
                // 清理
                this.operationLocks.delete(key);
                const currentPending = this.pendingOperations.get(key) || 1;
                if (currentPending <= 1) {
                    this.pendingOperations.delete(key);
                } else {
                    this.pendingOperations.set(key, currentPending - 1);
                }
            }
        })();

        this.operationLocks.set(key, operationPromise);
        return operationPromise;
    }

    // === 🔧 資源管理方法 ===

    /**
     * 清理已完成的工作流程資源
     */
    public async cleanupCompletedWorkflows(): Promise<void> {
        const workflows = await this.storage.listWorkflows();
        const completedWorkflows = workflows.filter(w => w.status === 'completed');

        for (const workflow of completedWorkflows) {
            this.activeWorkflows.delete(workflow.workflowId);

            // 移除該工作流程的所有事件監聽器
            const eventNames = this.eventNames();
            for (const eventName of eventNames) {
                const listeners = this.listeners(eventName) as ((...args: any[]) => void)[];
                for (const listener of listeners) {
                    // 檢查監聽器是否與該工作流程相關
                    if (listener.toString().includes(workflow.workflowId)) {
                        this.removeListener(eventName, listener);
                    }
                }
            }
        }
    }

    /**
     * 獲取性能統計信息
     */
    public getPerformanceStats(): {
        activeWorkflows: number;
        eventListeners: number;
        stateValidators: number;
        stateTransitions: number;
        memoryUsage: NodeJS.MemoryUsage;
    } {
        return {
            activeWorkflows: this.activeWorkflows.size,
            eventListeners: this.eventListenerCount,
            stateValidators: this.stateValidators.size,
            stateTransitions: this.stateTransitions.size,
            memoryUsage: process.memoryUsage()
        };
    }

    /**
     * 清理所有資源
     */
    public cleanup(): void {
        this.activeWorkflows.clear();
        this.stateValidators.clear();
        this.stateTransitions.clear();
        this.removeAllListeners();
        this.eventListenerCount = 0;
    }
}
