{"version": 3, "file": "responseFormatter.js", "sourceRoot": "", "sources": ["../../../src/shared/utils/responseFormatter.ts"], "names": [], "mappings": "AAAA,iCAAiC;AAUjC;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAU,EACI,IAAI,EACJ,OAAO,EACP,WAAW,EACX,WAAW,GAAG,EAAE,EACK;IACjE,mCAAmC;IACnC,MAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,IAAI,OAAO,EAAE,CAAC;QACV,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAC,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QACd,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,WAAW,EAAE,EAAC,CAAC,CAAC;IACvE,CAAC;IAED,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC;IACjF,CAAC;IAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAC,CAAC,CAAC;IACjF,CAAC;IAED,kDAAkD;IAClD,OAAO;QACH,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,kCAAkC,EAAC,CAAC;QAClG,OAAO,EAAE,KAAK;KACjB,CAAC;AACN,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,EACI,SAAS,EACT,KAAK,EACL,OAAO,EACP,WAAW,GAAG,EAAE,EAChB,aAAa,GAAG,EAAE,EACH;IAC/C,MAAM,OAAO,GAAG;QACZ,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,SAAS,KAAK,KAAK,EAAE,EAAC;KAC9D,CAAC;IAEF,IAAI,OAAO,EAAE,CAAC;QACV,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC;IACvF,CAAC;IAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAC,CAAC,CAAC;IACjF,CAAC;IAED,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,mBAAmB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAC,CAAC,CAAC;IACtF,CAAC;IAED,kDAAkD;IAClD,OAAO;QACH,OAAO;QACP,OAAO,EAAE,IAAI;KAChB,CAAC;AACN,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAI,EACI,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM,EACN,OAAO,EACgB;IAC/D,MAAM,OAAO,GAAG;QACZ;YACI,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,uBAAuB,SAAS,KAAK,SAAS,CAAC,MAAM,eAAe,MAAM,CAAC,MAAM,SAAS;SACnG;QACD;YACI,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,YAAY,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;SACvD;KACJ,CAAC;IAEF,2BAA2B;IAC3B,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,CAAC,IAAI,CAAC;YACT,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,oBAAoB,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;SACjE,CAAC,CAAC;IACP,CAAC;IAED,wBAAwB;IACxB,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACpB,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,IAAI;YACJ,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,eAAe;SACnD,CAAC,CAAC,CAAC;QACJ,OAAO,CAAC,IAAI,CAAC;YACT,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,iBAAiB,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;SAC/D,CAAC,CAAC;IACP,CAAC;IAED,OAAO,CAAC,IAAI,CAAC;QACT,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,4IAA4I;KACrJ,CAAC,CAAC;IAEH,OAAO;QACH,OAAO;QACP,OAAO,EAAE,IAAI;KAChB,CAAC;AACN,CAAC"}