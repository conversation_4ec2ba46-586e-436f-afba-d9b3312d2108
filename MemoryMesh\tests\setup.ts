// tests/setup.ts

import { jest } from '@jest/globals';

// 全局測試設置

// 模擬文件系統操作
jest.mock('fs', () => ({
  promises: {
    readFile: jest.fn(),
    writeFile: jest.fn(),
    access: jest.fn(),
    mkdir: jest.fn()
  }
}));

// 模擬配置 - 移除不存在的模塊模擬
// jest.mock('../src/config/config.js', () => ({
//   CONFIG: {
//     SERVER: {
//       NAME: 'test-memorymesh',
//       VERSION: '0.0.0-test'
//     },
//     PATHS: {
//       MEMORY_FILE: './test-data/memory.json'
//     }
//   }
// }));

// 全局測試工具
global.testUtils = {
  // 創建測試用的節點
  createTestNode: (overrides = {}) => ({
    type: 'node',
    name: 'test-node',
    nodeType: 'test',
    metadata: ['test: true'],
    ...overrides
  }),
  
  // 創建測試用的邊
  createTestEdge: (overrides = {}) => ({
    type: 'edge',
    from: 'node1',
    to: 'node2',
    edgeType: 'test',
    weight: 1,
    ...overrides
  }),
  
  // 創建測試用的工作流程
  createTestWorkflow: (overrides = {}) => ({
    workflowId: 'test-workflow-123',
    name: 'Test Workflow',
    status: 'not_started',
    currentStage: 0,
    totalStages: 3,
    progress: 0,
    metadata: {
      type: 'novel',
      templateId: 'novel_standard_v1'
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides
  }),
  
  // 創建測試用的階段
  createTestStage: (overrides = {}) => ({
    stageId: 'test-stage-123',
    workflowId: 'test-workflow-123',
    name: 'Test Stage',
    order: 0,
    status: 'active',
    requiredNodeTypes: ['character'],
    completionCriteria: {
      minNodes: 1,
      requiredFields: ['name']
    },
    ...overrides
  }),
  
  // 等待異步操作
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 模擬存儲數據
  mockStorageData: {
    nodes: [
      {
        type: 'node',
        name: 'test-character',
        nodeType: 'character',
        metadata: ['name: Test Character', 'role: protagonist']
      },
      {
        type: 'node',
        name: 'workflow_test',
        nodeType: 'workflow',
        metadata: [
          'workflow_id: wf_test_123',
          'status: in_progress',
          'progress: 50',
          'category: novel',
          'current_stage: 1',
          'total_stages: 3'
        ]
      }
    ],
    edges: [
      {
        type: 'edge',
        from: 'workflow_test',
        to: 'test-character',
        edgeType: 'contains',
        weight: 1
      }
    ]
  }
};

// 擴展Jest匹配器
expect.extend({
  toBeValidWorkflow(received) {
    const pass = received && 
                 typeof received.workflowId === 'string' &&
                 typeof received.name === 'string' &&
                 ['not_started', 'in_progress', 'completed', 'paused'].includes(received.status) &&
                 typeof received.currentStage === 'number' &&
                 typeof received.totalStages === 'number' &&
                 typeof received.progress === 'number';
    
    if (pass) {
      return {
        message: () => `expected ${JSON.stringify(received)} not to be a valid workflow`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${JSON.stringify(received)} to be a valid workflow`,
        pass: false,
      };
    }
  },
  
  toBeValidStage(received) {
    const pass = received && 
                 typeof received.stageId === 'string' &&
                 typeof received.workflowId === 'string' &&
                 typeof received.name === 'string' &&
                 typeof received.order === 'number' &&
                 ['pending', 'active', 'completed', 'skipped'].includes(received.status);
    
    if (pass) {
      return {
        message: () => `expected ${JSON.stringify(received)} not to be a valid stage`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${JSON.stringify(received)} to be a valid stage`,
        pass: false,
      };
    }
  }
});

// 類型聲明
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidWorkflow(): R;
      toBeValidStage(): R;
    }
  }
  
  var testUtils: {
    createTestNode: (overrides?: any) => any;
    createTestEdge: (overrides?: any) => any;
    createTestWorkflow: (overrides?: any) => any;
    createTestStage: (overrides?: any) => any;
    waitFor: (ms: number) => Promise<void>;
    mockStorageData: {
      nodes: any[];
      edges: any[];
    };
  };
}

// 測試環境清理
afterEach(() => {
  jest.clearAllMocks();
});

// 控制台輸出過濾（減少測試噪音）
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = (...args: any[]) => {
    // 只在非測試相關的錯誤時輸出
    if (!args[0]?.toString().includes('[Test]')) {
      originalConsoleError(...args);
    }
  };
  
  console.warn = (...args: any[]) => {
    // 只在非測試相關的警告時輸出
    if (!args[0]?.toString().includes('[Test]')) {
      originalConsoleWarn(...args);
    }
  };
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});
