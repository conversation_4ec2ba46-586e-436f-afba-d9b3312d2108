import type { ApplicationManager } from '../../application/index.js';
import type { Tool, ToolResponse } from '../../shared/index.js';
/**
 * Interface defining the public contract for dynamic schema tool registry
 */
export interface IDynamicSchemaToolRegistry {
    getTools(): Tool[];
    handleToolCall(toolName: string, args: Record<string, any>, knowledgeGraphManager: ApplicationManager): Promise<ToolResponse>;
}
/**
 * Manages dynamic tools generated from schema definitions
 */
declare class DynamicSchemaToolRegistry implements IDynamicSchemaToolRegistry {
    private schemas;
    private toolsCache;
    private static instance;
    private constructor();
    /**
     * Gets the singleton instance
     */
    static getInstance(): DynamicSchemaToolRegistry;
    /**
     * Initializes the registry by loading schemas and generating tools
     */
    initialize(): Promise<void>;
    /**
     * Retrieves all generated tools
     */
    getTools(): Tool[];
    /**
     * Generates tools for a given schema
     */
    private generateToolsForSchema;
    /**
     * <PERSON>les tool calls for dynamically generated schema-based tools
     */
    handleToolCall(toolName: string, args: Record<string, any>, knowledgeGraphManager: ApplicationManager): Promise<ToolResponse>;
}
export declare const dynamicSchemaTools: DynamicSchemaToolRegistry;
/**
 * Initializes the dynamic tools registry
 */
export declare function initializeDynamicTools(): Promise<IDynamicSchemaToolRegistry>;
export {};
