// tests/unit/CachedStorage.test.ts
// 緩存存儲層單元測試

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';

describe('CachedJsonLineStorage 單元測試', () => {
    let storage: any;

    beforeEach(async () => {
        const { CachedJsonLineStorage } = await import('../../dist/infrastructure/storage/CachedJsonLineStorage.js');
        storage = new CachedJsonLineStorage();
    });

    afterEach(() => {
        if (storage && typeof storage.cleanup === 'function') {
            storage.cleanup();
        }
    });

    describe('基本功能測試', () => {
        it('應該成功創建存儲實例', () => {
            expect(storage).toBeDefined();
            expect(typeof storage.configureCaching).toBe('function');
            expect(typeof storage.getCacheStats).toBe('function');
        });

        it('應該支持緩存配置', async () => {
            const config = {
                maxSize: 100,
                ttl: 1000,
                enablePersistence: true
            };

            await storage.configureCaching(config);
            
            const stats = storage.getCacheStats();
            expect(stats).toBeDefined();
            expect(typeof stats.nodeCache).toBe('number');
            expect(typeof stats.memoryUsage).toBe('object');
        });

        it('應該支持緩存統計查詢', () => {
            const stats = storage.getCacheStats();
            
            expect(stats).toBeDefined();
            expect(stats).toHaveProperty('nodeCache');
            expect(stats).toHaveProperty('workflowCache');
            expect(stats).toHaveProperty('memoryUsage');
            expect(stats).toHaveProperty('hitRate');
        });
    });

    describe('批量操作測試', () => {
        it('應該支持空的批量操作', async () => {
            const result = await storage.batchOperation([]);
            
            expect(result).toBeDefined();
            expect(result.success).toBe(true);
            expect(result.results).toBeDefined();
            expect(Array.isArray(result.results)).toBe(true);
        });

        it('應該支持批量節點創建', async () => {
            const operations = [
                {
                    type: 'create',
                    target: 'node',
                    data: {
                        type: 'node',
                        name: 'test-node-1',
                        nodeType: 'test',
                        metadata: ['test: true', 'batch: 1']
                    }
                },
                {
                    type: 'create',
                    target: 'node',
                    data: {
                        type: 'node',
                        name: 'test-node-2',
                        nodeType: 'test',
                        metadata: ['test: true', 'batch: 2']
                    }
                }
            ];

            const result = await storage.batchOperation(operations);
            
            expect(result).toBeDefined();
            expect(result.success).toBe(true);
            expect(result.results.length).toBe(2);
        });

        it('應該處理批量操作中的錯誤', async () => {
            const operations = [
                {
                    type: 'create',
                    target: 'node',
                    data: {
                        type: 'node',
                        name: 'valid-node',
                        nodeType: 'test',
                        metadata: ['test: true']
                    }
                },
                {
                    type: 'create',
                    target: 'node',
                    data: {
                        // 缺少必需字段
                        type: 'node',
                        nodeType: 'test'
                    }
                }
            ];

            const result = await storage.batchOperation(operations);
            
            expect(result).toBeDefined();
            // 應該有部分成功，部分失敗的結果
        });
    });

    describe('增量更新測試', () => {
        it('應該支持空的增量更新', async () => {
            await storage.updateNodesIncremental([]);
            // 不應該拋出錯誤
        });

        it('應該支持節點增量更新', async () => {
            const updates = [
                {
                    name: 'incremental-test-1',
                    nodeType: 'test',
                    metadata: ['updated: true', 'timestamp: ' + Date.now()]
                },
                {
                    name: 'incremental-test-2',
                    nodeType: 'test',
                    metadata: ['updated: true', 'timestamp: ' + Date.now()]
                }
            ];

            await storage.updateNodesIncremental(updates);
            // 不應該拋出錯誤
        });

        it('應該處理大量增量更新', async () => {
            const updates = [];
            for (let i = 0; i < 100; i++) {
                updates.push({
                    name: `bulk-update-${i}`,
                    nodeType: 'test',
                    metadata: [`index: ${i}`, 'bulk: true']
                });
            }

            const startTime = Date.now();
            await storage.updateNodesIncremental(updates);
            const duration = Date.now() - startTime;

            expect(duration).toBeLessThan(5000); // 應該在5秒內完成
        });
    });

    describe('查詢功能測試', () => {
        beforeEach(async () => {
            // 準備測試數據
            const operations = [
                {
                    type: 'create',
                    target: 'node',
                    data: {
                        type: 'node',
                        name: 'query-test-character',
                        nodeType: 'character',
                        metadata: ['role: protagonist', 'test: true']
                    }
                },
                {
                    type: 'create',
                    target: 'node',
                    data: {
                        type: 'node',
                        name: 'query-test-setting',
                        nodeType: 'setting',
                        metadata: ['type: urban', 'test: true']
                    }
                }
            ];

            await storage.batchOperation(operations);
        });

        it('應該支持按類型查詢節點', async () => {
            const characters = await storage.queryNodesByType('character');
            
            expect(Array.isArray(characters)).toBe(true);
            // 應該包含我們創建的角色節點
        });

        it('應該支持按工作流程查詢節點', async () => {
            const nodes = await storage.queryNodesByWorkflow('test-workflow');
            
            expect(Array.isArray(nodes)).toBe(true);
        });

        it('應該支持限制查詢結果數量', async () => {
            const nodes = await storage.queryNodesByType('character', 1);
            
            expect(Array.isArray(nodes)).toBe(true);
            expect(nodes.length).toBeLessThanOrEqual(1);
        });
    });

    describe('索引配置測試', () => {
        it('應該支持索引配置', async () => {
            const indexConfig = {
                nodeNameIndex: true,
                nodeTypeIndex: true,
                workflowIndex: true,
                customIndexes: ['metadata.role', 'metadata.status']
            };

            await storage.configureIndexes(indexConfig);
            // 不應該拋出錯誤
        });

        it('應該支持自定義索引', async () => {
            const indexConfig = {
                nodeNameIndex: true,
                nodeTypeIndex: true,
                workflowIndex: true,
                customIndexes: ['metadata.importance', 'metadata.category']
            };

            await storage.configureIndexes(indexConfig);
            // 不應該拋出錯誤
        });
    });

    describe('緩存管理測試', () => {
        it('應該支持緩存失效', async () => {
            // 先獲取初始統計
            const initialStats = storage.getCacheStats();
            
            // 執行緩存失效
            await storage.invalidateCache();
            
            // 獲取失效後統計
            const afterStats = storage.getCacheStats();
            
            // 緩存應該被清理
            expect(afterStats.nodeCache).toBeLessThanOrEqual(initialStats.nodeCache);
        });

        it('應該支持選擇性緩存失效', async () => {
            await storage.invalidateCache(['nodes']);
            // 不應該拋出錯誤
        });

        it('應該正確計算緩存命中率', async () => {
            // 執行一些查詢操作
            await storage.queryNodesByType('test');
            await storage.queryNodesByType('test'); // 重複查詢應該命中緩存
            
            const stats = storage.getCacheStats();
            expect(typeof stats.hitRate).toBe('number');
            expect(stats.hitRate).toBeGreaterThanOrEqual(0);
            expect(stats.hitRate).toBeLessThanOrEqual(1);
        });
    });

    describe('內存管理測試', () => {
        it('應該監控內存使用', () => {
            const stats = storage.getCacheStats();
            
            expect(stats.memoryUsage).toBeDefined();
            expect(typeof stats.memoryUsage.heapUsed).toBe('number');
            expect(typeof stats.memoryUsage.heapTotal).toBe('number');
        });

        it('應該支持內存限制配置', async () => {
            await storage.configureCaching({
                maxSize: 50, // 較小的限制
                ttl: 1000,
                enablePersistence: true
            });

            // 添加大量數據測試內存限制
            const operations = [];
            for (let i = 0; i < 100; i++) {
                operations.push({
                    type: 'create',
                    target: 'node',
                    data: {
                        type: 'node',
                        name: `memory-test-${i}`,
                        nodeType: 'test',
                        metadata: [`index: ${i}`, 'data: ' + 'x'.repeat(100)]
                    }
                });
            }

            await storage.batchOperation(operations);
            
            const stats = storage.getCacheStats();
            // 緩存大小應該受到限制
            expect(stats.nodeCache).toBeLessThanOrEqual(50);
        });
    });

    describe('併發安全測試', () => {
        it('應該安全處理併發讀操作', async () => {
            const promises = [];
            
            for (let i = 0; i < 10; i++) {
                promises.push(storage.queryNodesByType('test'));
            }

            const results = await Promise.allSettled(promises);
            
            // 所有操作都應該成功
            const successful = results.filter(r => r.status === 'fulfilled').length;
            expect(successful).toBe(10);
        });

        it('應該安全處理併發寫操作', async () => {
            const promises = [];
            
            for (let i = 0; i < 5; i++) {
                promises.push(
                    storage.batchOperation([{
                        type: 'create',
                        target: 'node',
                        data: {
                            type: 'node',
                            name: `concurrent-${i}-${Date.now()}`,
                            nodeType: 'test',
                            metadata: [`thread: ${i}`]
                        }
                    }])
                );
            }

            const results = await Promise.allSettled(promises);
            
            // 大部分操作應該成功
            const successful = results.filter(r => r.status === 'fulfilled').length;
            expect(successful).toBeGreaterThan(0);
        });
    });

    describe('錯誤處理測試', () => {
        it('應該處理無效的緩存配置', async () => {
            const invalidConfig = {
                maxSize: -1, // 無效值
                ttl: 'invalid', // 無效類型
                enablePersistence: 'yes' // 無效類型
            };

            try {
                await storage.configureCaching(invalidConfig as any);
                // 應該處理錯誤而不是崩潰
            } catch (error) {
                expect(error).toBeDefined();
            }
        });

        it('應該處理無效的批量操作', async () => {
            const invalidOperations = [
                {
                    type: 'invalid_type',
                    target: 'node',
                    data: {}
                }
            ];

            const result = await storage.batchOperation(invalidOperations as any);
            
            expect(result).toBeDefined();
            // 應該有錯誤信息
        });

        it('應該處理存儲錯誤', async () => {
            // 嘗試查詢不存在的類型
            const result = await storage.queryNodesByType('non_existent_type');
            
            expect(Array.isArray(result)).toBe(true);
            expect(result.length).toBe(0);
        });
    });

    describe('性能測試', () => {
        it('緩存配置應該快速完成', async () => {
            const startTime = Date.now();
            
            await storage.configureCaching({
                maxSize: 1000,
                ttl: 300000,
                enablePersistence: true
            });
            
            const duration = Date.now() - startTime;
            expect(duration).toBeLessThan(100); // 100ms內完成
        });

        it('批量操作應該有良好的性能', async () => {
            const operations = [];
            for (let i = 0; i < 50; i++) {
                operations.push({
                    type: 'create',
                    target: 'node',
                    data: {
                        type: 'node',
                        name: `perf-test-${i}`,
                        nodeType: 'test',
                        metadata: [`index: ${i}`]
                    }
                });
            }

            const startTime = Date.now();
            await storage.batchOperation(operations);
            const duration = Date.now() - startTime;

            expect(duration).toBeLessThan(3000); // 3秒內完成50個操作
        });
    });
});
