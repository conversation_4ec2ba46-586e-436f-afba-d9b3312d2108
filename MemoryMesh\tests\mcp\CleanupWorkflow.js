// tests/mcp/CleanupWorkflow.js
// 清理舊的工作流程節點

console.log('🧹 清理舊的工作流程節點...');

async function cleanupOldWorkflow() {
    try {
        const handlerModule = await import('../../dist/integration/tools/handlers/WorkflowToolHandler.js');
        const storageModule = await import('../../dist/infrastructure/storage/CachedJsonLineStorage.js');
        const appManagerModule = await import('../../dist/application/managers/ApplicationManager.js');
        
        const storage = new storageModule.CachedJsonLineStorage();
        const appManager = new appManagerModule.ApplicationManager(storage);
        const handler = new handlerModule.WorkflowToolHandler(appManager, storage);

        console.log('✅ 模塊加載成功');

        // 要刪除的節點
        const nodesToDelete = [
            'workflow_wf_1751799104279_4jsukd1eb',
            'stage_wf_1751799104279_4jsukd1eb_planning',
            'stage_wf_1751799104279_4jsukd1eb_outline',
            'stage_wf_1751799104279_4jsukd1eb_chapter',
            'stage_wf_1751799104279_4jsukd1eb_generation'
        ];

        console.log('🗑️ 刪除舊的工作流程節點...');
        
        for (const nodeName of nodesToDelete) {
            try {
                const deleteResult = await handler.handleTool('delete_nodes', {
                    nodeNames: [nodeName]
                });
                console.log(`${deleteResult.isError ? '❌' : '✅'} 刪除 ${nodeName}: ${deleteResult.isError ? '失敗' : '成功'}`);
            } catch (error) {
                console.log(`❌ 刪除 ${nodeName} 時出錯: ${error.message}`);
            }
        }

        // 驗證清理結果
        console.log('\n📋 驗證清理結果...');
        const listResult = await handler.handleTool('workflow_list', {});
        console.log('📄 清理後的工作流程列表:', JSON.stringify(listResult, null, 2));

        // 清理
        if (storage && typeof storage.cleanup === 'function') {
            storage.cleanup();
        }

        console.log('✅ 清理完成');
        return true;

    } catch (error) {
        console.error('❌ 清理過程中發生錯誤:', error);
        return false;
    }
}

cleanupOldWorkflow();
