<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Viewer</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.32.2/ace.js"></script>
    <style>
        /* --- Base Styles --- */
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #60a5fa;
            --success: #22c55e;
            --danger: #ef4444;
            --warning: #f59e0b;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
            color: var(--gray-800);
            background-color: var(--gray-50);
        }

        /* --- Typography --- */
        h1 {
            margin-bottom: 2rem;
            color: var(--gray-900);
            font-size: 2rem;
            font-weight: 600;
        }

        /* --- Layout Components --- */
        .container {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        /* --- Buttons --- */
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.25rem;
            cursor: pointer;
            font-size: 1rem;
            background: var(--primary);
            color: white;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: var(--primary-dark);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }

        .btn-outline:hover {
            background: var(--primary);
            color: white;
        }

        .btn-secondary {
            background: var(--gray-600);
            color: white;
        }

        .btn-secondary:hover {
            background: var(--gray-700);
        }

        /* --- Tabs --- */
        .tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid var(--gray-200);
            padding-bottom: 0.5rem;
        }

        .tab {
            padding: 0.5rem 1rem;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 1rem;
            color: var(--gray-600);
            border-bottom: 2px solid transparent;
            margin-bottom: -0.5rem;
            transition: all 0.2s ease;
        }

        .tab:hover {
            color: var(--primary);
        }

        .tab.active {
            color: var(--primary);
            border-bottom: 2px solid var(--primary);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* --- File Selection --- */
        .file-section {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .file-path {
            flex: 1;
            font-family: monospace;
            padding: 0.5rem;
            background: var(--gray-50);
            border: 1px solid var(--gray-300);
            border-radius: 0.25rem;
        }

        /* --- Table View --- */
        .table-container {
            overflow-x: auto;
            margin-bottom: 2rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th, td {
            padding: 0.75rem;
            border: 1px solid var(--gray-200);
            text-align: left;
        }

        th {
            background: var(--gray-100);
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tbody tr:hover {
            background: var(--gray-50);
        }

        /* --- Metadata and Expandable Controls --- */
        .expand-button {
            padding: 0.25rem 0.5rem;
            background: var(--gray-100);
            border: 1px solid var(--gray-300);
            border-radius: 0.25rem;
            cursor: pointer;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            color: var(--gray-700);
            transition: all 0.2s ease;
        }

        .expand-button:hover {
            background: var(--gray-200);
        }

        .metadata-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .metadata-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .metadata-item {
            background: var(--gray-100);
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }

        .metadata-key {
            color: var(--gray-700);
            font-weight: 500;
        }

        .metadata-value {
            color: var(--gray-900);
        }

        .expandable-cell {
            cursor: pointer;
            position: relative;
        }

        .expandable-cell::after {
            content: '▼';
            position: absolute;
            right: 0.5rem;
            color: var(--gray-500);
            transition: transform 0.2s;
        }

        .expandable-cell.expanded::after {
            transform: rotate(180deg);
        }

        .metadata-summary {
            color: var(--gray-600);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        /* --- Edge Links --- */
        .edge-link {
            color: var(--primary);
            text-decoration: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .edge-link:hover {
            text-decoration: underline;
        }

        .edge-count {
            background: var(--primary-light);
            color: white;
            padding: 0.125rem 0.375rem;
            border-radius: 1rem;
            font-size: 0.75rem;
        }

        .edge-list {
            margin-top: 0.5rem;
            padding-left: 1rem;
            border-left: 2px solid var(--gray-200);
        }

        .edge-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0;
            font-size: 0.875rem;
        }

        .edge-type {
            color: var(--gray-600);
        }

        .edge-target {
            color: var(--primary);
            cursor: pointer;
        }

        .edge-target:hover {
            text-decoration: underline;
        }

        /* --- Stats Panel --- */
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-title {
            font-size: 0.875rem;
            color: var(--gray-600);
            margin-bottom: 0.5rem;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
        }

        /* --- Toast Notifications --- */
        #toastContainer {
            position: fixed;
            top: 1rem;
            right: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            z-index: 1000;
        }

        .toast {
            min-width: 250px;
            padding: 1rem 1.5rem;
            border-radius: 0.25rem;
            color: white;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
            opacity: 0;
            transform: translateX(100%);
            animation: slideIn 0.5s forwards, fadeOut 0.5s forwards 2.5s;
        }

        .toast-success {
            background-color: var(--success);
        }

        .toast-error {
            background-color: var(--danger);
        }

        @keyframes slideIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeOut {
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* --- Search and Filter --- */
        .search-container {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .search-input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid var(--gray-300);
            border-radius: 0.25rem;
            font-size: 1rem;
        }

        .filter-select {
            padding: 0.5rem;
            border: 1px solid var(--gray-300);
            border-radius: 0.25rem;
            font-size: 1rem;
            min-width: 150px;
        }

        /* --- JSON Editor --- */
        .editor-container {
            height: 600px;
            border: 1px solid var(--gray-300);
            border-radius: 0.25rem;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        #jsonEditor {
            height: 100%;
            font-size: 14px;
        }

        /* --- Responsive Design --- */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .search-container {
                flex-direction: column;
            }

            .metadata-grid {
                grid-template-columns: 1fr;
            }
        }

        /* --- Enhanced Workflow View Styles --- */
        .workflow-container {
            padding: 1rem;
            position: relative;
        }

        .workflow-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--gray-200);
            flex-wrap: wrap;
            gap: 1rem;
        }

        .workflow-header h3 {
            margin: 0;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .workflow-stats-badge {
            background: var(--primary);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .workflow-controls {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .workflow-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background-color: var(--gray-100);
            border-radius: 8px;
            border: 1px solid var(--gray-200);
        }

        .workflow-search {
            grid-column: 1 / -1;
            padding: 0.5rem;
            border: 1px solid var(--gray-300);
            border-radius: 4px;
            font-size: 1rem;
        }

        .workflow-list {
            min-height: 300px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .workflow-placeholder {
            grid-column: 1 / -1;
            text-align: center;
            padding: 3rem;
            color: var(--gray-500);
            background: var(--gray-50);
            border: 2px dashed var(--gray-300);
            border-radius: 8px;
        }

        /* Real-time update indicator */
        .workflow-live-indicator {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            border-radius: 20px;
            font-size: 0.8rem;
            color: #166534;
        }

        .workflow-live-indicator.updating {
            background: rgba(251, 191, 36, 0.1);
            border-color: rgba(251, 191, 36, 0.3);
            color: #92400e;
        }

        .live-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #22c55e;
            animation: pulse 2s infinite;
        }

        .live-dot.updating {
            background: #f59e0b;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .workflow-item {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: 12px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .workflow-item:hover {
            border-color: var(--primary);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
            transform: translateY(-2px);
        }

        .workflow-item.selected {
            border-color: var(--primary);
            background-color: rgba(37, 99, 235, 0.05);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        .workflow-item.dragging {
            opacity: 0.7;
            transform: rotate(5deg);
            z-index: 1000;
        }

        .workflow-item.drag-over {
            border-color: var(--success);
            background-color: rgba(34, 197, 94, 0.05);
        }

        .workflow-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--primary-light));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .workflow-item.selected::before,
        .workflow-item:hover::before {
            opacity: 1;
        }

        .workflow-item-actions {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.5rem;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .workflow-item:hover .workflow-item-actions {
            opacity: 1;
        }

        .workflow-action-btn {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 50%;
            background: var(--gray-100);
            color: var(--gray-600);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .workflow-action-btn:hover {
            background: var(--primary);
            color: white;
        }

        .workflow-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
        }

        .workflow-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: var(--gray-600);
        }

        .workflow-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .workflow-status.not_started {
            background-color: var(--gray-200);
            color: var(--gray-700);
        }

        .workflow-status.in_progress {
            background-color: rgba(251, 191, 36, 0.2);
            color: #92400e;
        }

        .workflow-status.completed {
            background-color: rgba(34, 197, 94, 0.2);
            color: #166534;
        }

        .workflow-status.paused {
            background-color: rgba(239, 68, 68, 0.2);
            color: #991b1b;
        }

        .workflow-progress {
            margin-top: 1rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--primary);
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.8rem;
            color: var(--gray-600);
            margin-top: 0.25rem;
        }

        .workflow-details {
            margin-top: 2rem;
            padding: 1.5rem;
            background-color: var(--gray-50);
            border-radius: 8px;
        }

        .stage-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .stage-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: white;
            border-radius: 6px;
            border-left: 4px solid var(--gray-300);
        }

        .stage-item.active {
            border-left-color: var(--primary);
            background-color: rgba(37, 99, 235, 0.05);
        }

        .stage-item.completed {
            border-left-color: var(--success);
            background-color: rgba(34, 197, 94, 0.05);
        }

        .stage-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: bold;
        }

        .stage-icon.pending {
            background-color: var(--gray-300);
            color: var(--gray-600);
        }

        .stage-icon.active {
            background-color: var(--primary);
            color: white;
        }

        .stage-icon.completed {
            background-color: var(--success);
            color: white;
        }

        .stage-name {
            flex: 1;
            font-weight: 500;
        }

        .stage-requirements {
            font-size: 0.8rem;
            color: var(--gray-500);
        }

        /* Workflow Statistics and Charts */
        .workflow-stats-panel {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: white;
            border-radius: 12px;
            border: 1px solid var(--gray-200);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .workflow-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            padding: 1rem;
            background: var(--gray-50);
            border-radius: 8px;
            text-align: center;
            border: 1px solid var(--gray-200);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--gray-600);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .workflow-chart-container {
            height: 200px;
            background: var(--gray-50);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-500);
            border: 1px solid var(--gray-200);
        }

        .progress-ring {
            width: 120px;
            height: 120px;
            position: relative;
            display: inline-block;
        }

        .progress-ring-circle {
            width: 100%;
            height: 100%;
            fill: transparent;
            stroke: var(--gray-200);
            stroke-width: 8;
            stroke-dasharray: 283;
            stroke-dashoffset: 283;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }

        .progress-ring-progress {
            stroke: var(--primary);
            stroke-linecap: round;
            transition: stroke-dashoffset 0.5s ease;
        }

        .progress-ring-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--gray-800);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .workflow-header {
                flex-direction: column;
                align-items: stretch;
            }

            .workflow-controls {
                justify-content: center;
            }

            .workflow-filters {
                grid-template-columns: 1fr;
            }

            .workflow-list {
                grid-template-columns: 1fr;
            }

            .workflow-stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .stat-number {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .workflow-container {
                padding: 0.5rem;
            }

            .workflow-stats-grid {
                grid-template-columns: 1fr;
            }

            .workflow-item {
                padding: 1rem;
            }
        }

        /* 🔮 預留UI擴展樣式 */
        .workflow-extension-panel {
            margin-top: 1rem;
            padding: 1rem;
            border: 1px dashed var(--gray-300);
            border-radius: 6px;
            background-color: var(--gray-50);
            text-align: center;
            color: var(--gray-500);
            font-style: italic;
        }
    </style>
</head>
<body>
<h1>Memory Viewer</h1>

<div class="file-section">
    <button class="btn" id="selectMemoryFileBtn">Select Memory File</button>
    <div class="file-path" id="filePath">No file selected</div>
</div>

<!-- Toast Notifications Container -->
<div id="toastContainer"></div>

<!-- Stats Panel -->
<div class="stats-panel">
    <div class="stat-card">
        <div class="stat-title">Total Nodes</div>
        <div class="stat-value" id="totalNodes">0</div>
    </div>
    <div class="stat-card">
        <div class="stat-title">Total Edges</div>
        <div class="stat-value" id="totalEdges">0</div>
    </div>
    <div class="stat-card">
        <div class="stat-title">Node Types</div>
        <div class="stat-value" id="nodeTypes">0</div>
    </div>
    <div class="stat-card">
        <div class="stat-title">Edge Types</div>
        <div class="stat-value" id="edgeTypes">0</div>
    </div>
</div>

<!-- Tabs -->
<div class="tabs">
    <button class="tab active" data-tab="table">Table View</button>
    <button class="tab" data-tab="workflow">Workflow View</button>
    <button class="tab" data-tab="raw">Raw JSON</button>
</div>

<!-- Workflow View -->
<div id="workflowView" class="tab-content">
    <div class="workflow-container">
        <!-- Live Update Indicator -->
        <div id="workflowLiveIndicator" class="workflow-live-indicator">
            <div class="live-dot"></div>
            <span>即時更新</span>
        </div>

        <div class="workflow-header">
            <h3>
                工作流程管理
                <span id="workflowStatsBadge" class="workflow-stats-badge">0</span>
            </h3>
            <div class="workflow-controls">
                <button class="btn btn-primary" onclick="refreshWorkflows()">🔄 刷新</button>
                <button class="btn btn-secondary" onclick="showCreateWorkflowModal()">➕ 創建工作流程</button>
                <button class="btn btn-outline" onclick="toggleStatsPanel()">📊 統計</button>
                <button class="btn btn-outline" onclick="exportWorkflows()">📤 導出</button>
            </div>
        </div>

        <!-- Statistics Panel -->
        <div id="workflowStatsPanel" class="workflow-stats-panel" style="display: none;">
            <div class="workflow-stats-grid">
                <div class="stat-card">
                    <div id="totalWorkflowsStat" class="stat-number">0</div>
                    <div class="stat-label">總工作流程</div>
                </div>
                <div class="stat-card">
                    <div id="activeWorkflowsStat" class="stat-number">0</div>
                    <div class="stat-label">進行中</div>
                </div>
                <div class="stat-card">
                    <div id="completedWorkflowsStat" class="stat-number">0</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-card">
                    <div id="avgProgressStat" class="stat-number">0%</div>
                    <div class="stat-label">平均進度</div>
                </div>
            </div>
            <div class="workflow-chart-container">
                <div class="progress-ring">
                    <svg class="progress-ring-circle">
                        <circle cx="60" cy="60" r="45" class="progress-ring-circle"></circle>
                        <circle cx="60" cy="60" r="45" class="progress-ring-progress" id="overallProgressRing"></circle>
                    </svg>
                    <div class="progress-ring-text" id="overallProgressText">0%</div>
                </div>
            </div>
        </div>

        <div class="workflow-filters">
            <input type="text" id="workflowSearch" class="workflow-search" placeholder="🔍 搜索工作流程..." oninput="filterWorkflows()">
            <select id="workflowStatusFilter" onchange="filterWorkflows()">
                <option value="all">所有狀態</option>
                <option value="not_started">未開始</option>
                <option value="in_progress">進行中</option>
                <option value="completed">已完成</option>
                <option value="paused">已暫停</option>
            </select>
            <select id="workflowTypeFilter" onchange="filterWorkflows()">
                <option value="all">所有類型</option>
                <option value="novel">小說</option>
                <option value="article">文章</option>
                <option value="script">劇本</option>
                <option value="academic">學術論文</option>
                <option value="technical">技術文檔</option>
                <option value="creative">創意寫作</option>
                <option value="custom">自定義</option>
            </select>
            <select id="workflowSortBy" onchange="filterWorkflows()">
                <option value="name">按名稱排序</option>
                <option value="progress">按進度排序</option>
                <option value="status">按狀態排序</option>
                <option value="type">按類型排序</option>
            </select>
        </div>

        <div id="workflowList" class="workflow-list">
            <div class="workflow-placeholder">
                <p>請選擇一個memory文件來查看工作流程</p>
                <p style="font-size: 0.9rem; color: var(--gray-400); margin-top: 0.5rem;">
                    支持拖拽排序和批量操作
                </p>
            </div>
        </div>

        <div id="workflowDetails" class="workflow-details" style="display: none;">
            <h4>工作流程詳情</h4>
            <div id="workflowInfo"></div>
            <div id="stageProgress"></div>
        </div>
    </div>
</div>

<!-- Table View -->
<div id="tableView" class="tab-content active">
    <div class="search-container">
        <input type="text" class="search-input" id="searchInput" placeholder="Search...">
        <select class="filter-select" id="typeFilter">
            <option value="all">All Types</option>
        </select>
        <select class="filter-select" id="viewFilter">
            <option value="all">All Items</option>
            <option value="nodes">Nodes Only</option>
            <option value="edges">Edges Only</option>
        </select>
    </div>
    <div class="table-container">
        <table id="memoryTable">
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Name/From</th>
                    <th>NodeType/To</th>
                    <th>Metadata/EdgeType</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
</div>

<!-- Raw JSON View -->
<div id="rawView" class="tab-content">
    <div class="editor-container">
        <div id="jsonEditor"></div>
    </div>
</div>

<script>
    // --- Constants and State ---
    const DB_NAME = 'MemoryViewerDB';
    const STORE_NAME = 'file';
    let db = null;
    let fileHandle = null;
    let jsonEditor = null;
    let currentData = null;

    // --- Utility Functions ---
    function showToast(message, type = 'success', duration = 3000) {
        const toastContainer = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.classList.add('toast', type === 'success' ? 'toast-success' : 'toast-error');
        toast.textContent = message;
        toastContainer.appendChild(toast);
        setTimeout(() => toast.remove(), duration + 1000);
    }

    function parseMetadata(metadata) {
        if (!metadata || !Array.isArray(metadata)) return [];
        
        return metadata.map(item => {
            const colonIndex = item.indexOf(':');
            if (colonIndex === -1) return { key: '', value: item };
            
            const key = item.substring(0, colonIndex).trim();
            const value = item.substring(colonIndex + 1).trim();
            return { key, value };
        });
    }

    function createMetadataDisplay(metadata) {
        if (!metadata || !Array.isArray(metadata)) return '';

        const parsedMetadata = parseMetadata(metadata);
        const container = document.createElement('div');
        
        // Add summary
        const summary = document.createElement('div');
        summary.className = 'metadata-summary';
        summary.textContent = `${parsedMetadata.length} metadata entries`;
        container.appendChild(summary);

        // Create metadata grid
        const grid = document.createElement('div');
        grid.className = 'metadata-grid';
        
        parsedMetadata.forEach(({ key, value }) => {
            const item = document.createElement('div');
            item.className = 'metadata-item';
            
            if (key) {
                item.innerHTML = `<span class="metadata-key">${key}:</span> <span class="metadata-value">${value}</span>`;
            } else {
                item.innerHTML = `<span class="metadata-value">${value}</span>`;
            }
            
            grid.appendChild(item);
        });
        
        container.appendChild(grid);
        return container;
    }

    // --- IndexedDB Operations ---
    const idb = {
        initDB: async () => {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(DB_NAME, 1);
                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains(STORE_NAME)) {
                        db.createObjectStore(STORE_NAME);
                    }
                };
                request.onsuccess = (event) => {
                    db = event.target.result;
                    resolve();
                };
                request.onerror = (event) => reject(event.target.error);
            });
        },
        saveFileHandle: async (handle) => {
            return new Promise((resolve, reject) => {
const transaction = db.transaction([STORE_NAME], 'readwrite');
                const store = transaction.objectStore(STORE_NAME);
                const request = store.put(handle, 'memoryFile');
                request.onsuccess = () => resolve();
                request.onerror = (event) => reject(event.target.error);
            });
        },
        getFileHandleFromDB: async () => {
            return new Promise((resolve, reject) => {
                const transaction = db.transaction([STORE_NAME], 'readonly');
                const store = transaction.objectStore(STORE_NAME);
                const request = store.get('memoryFile');
                request.onsuccess = async (event) => {
                    const handle = event.target.result;
                    if (handle) {
                        const permission = await handle.queryPermission({mode: 'read'});
                        if (permission === 'granted') {
                            resolve(handle);
                            return;
                        }
                    }
                    resolve(null);
                };
                request.onerror = (event) => reject(event.target.error);
            });
        },
        removeFileHandleFromDB: async () => {
            return new Promise((resolve, reject) => {
                const transaction = db.transaction([STORE_NAME], 'readwrite');
                const store = transaction.objectStore(STORE_NAME);
                const request = store.delete('memoryFile');
                request.onsuccess = () => resolve();
                request.onerror = (event) => reject(event.target.error);
            });
        }
    };

    // --- File System Operations ---
    const fileSystem = {
        selectFile: async () => {
            try {
                let options = {id: 'memory-file', mode: 'read'};
                const storedHandle = await idb.getFileHandleFromDB();
                options.startIn = storedHandle || 'documents';
                [fileHandle] = await window.showOpenFilePicker(options);
                if (storedHandle && fileHandle.name !== storedHandle.name) {
                    await idb.removeFileHandleFromDB();
                }
                document.getElementById('filePath').textContent = fileHandle.name;
                await fileSystem.loadFile();
                showToast('File selected successfully!', 'success');
                await idb.saveFileHandle(fileHandle);
            } catch (error) {
                if (error.name !== 'AbortError') {
                    showToast('Error selecting file: ' + error.message, 'error');
                }
            }
        },
        loadFile: async () => {
            if (!fileHandle) {
                showToast('Please select a memory file first', 'error');
                return;
            }
            try {
                const file = await fileHandle.getFile();
                const content = await file.text();

                // Split content into lines and parse each line as JSON
                const lines = content.split('\n');
                const jsonObjects = lines
                    .filter(line => line.trim() !== '')
                    .map(line => JSON.parse(line));

                currentData = jsonObjects;
                updateViews(jsonObjects);
                showToast('Memory file loaded successfully!', 'success');
            } catch (error) {
                showToast('Error loading memory file: ' + error.message, 'error');
            }
        }
    };

    // --- Update Views ---
    function updateStats(data) {
        const nodes = data.filter(item => item.type === 'node');
        const edges = data.filter(item => item.type === 'edge');
        const nodeTypeSet = new Set(nodes.map(node => node.nodeType));
        const edgeTypeSet = new Set(edges.map(edge => edge.edgeType));

        document.getElementById('totalNodes').textContent = nodes.length;
        document.getElementById('totalEdges').textContent = edges.length;
        document.getElementById('nodeTypes').textContent = nodeTypeSet.size;
        document.getElementById('edgeTypes').textContent = edgeTypeSet.size;

        // Update filters
        const typeFilter = document.getElementById('typeFilter');
        typeFilter.innerHTML = '<option value="all">All Types</option>';
        [...nodeTypeSet].sort().forEach(type => {
            const option = document.createElement('option');
            option.value = `node:${type}`;
            option.textContent = `Node: ${type}`;
            typeFilter.appendChild(option);
        });
        [...edgeTypeSet].sort().forEach(type => {
            const option = document.createElement('option');
            option.value = `edge:${type}`;
            option.textContent = `Edge: ${type}`;
            typeFilter.appendChild(option);
        });
    }

    function updateTable(data, searchTerm = '', typeFilter = 'all', viewFilter = 'all') {
        const tbody = document.querySelector('#memoryTable tbody');
        tbody.innerHTML = '';

        // Create a map of node relationships
        const nodeEdges = new Map();
        data.forEach(item => {
            if (item.type === 'edge') {
                if (!nodeEdges.has(item.from)) {
                    nodeEdges.set(item.from, []);
                }
                nodeEdges.get(item.from).push(item);
            }
        });

        const filteredData = data.filter(item => {
            if (viewFilter === 'nodes' && item.type !== 'node') return false;
            if (viewFilter === 'edges' && item.type !== 'edge') return false;

            if (typeFilter !== 'all') {
                const [filterType, filterValue] = typeFilter.split(':');
                if (filterType === 'node' && (item.type !== 'node' || item.nodeType !== filterValue)) return false;
                if (filterType === 'edge' && (item.type !== 'edge' || item.edgeType !== filterValue)) return false;
            }

            if (searchTerm) {
                const searchLower = searchTerm.toLowerCase();
                const itemString = JSON.stringify(item).toLowerCase();
                return itemString.includes(searchLower);
            }

            return true;
        });

        // Show only nodes in the main table if edge integration is enabled
        const displayData = viewFilter === 'edges' ? filteredData : filteredData.filter(item => item.type === 'node');

        displayData.forEach(item => {
            const row = document.createElement('tr');
            
            // Type column
            const typeCell = document.createElement('td');
            typeCell.textContent = item.type;
            row.appendChild(typeCell);
            
            // Name column
            const nameCell = document.createElement('td');
            nameCell.textContent = item.name;
            
            // Add edge information if available
            const edges = nodeEdges.get(item.name);
            if (edges && edges.length > 0) {
                const edgeLink = document.createElement('div');
                edgeLink.innerHTML = `
                    <a class="edge-link">
                        View Edges
                        <span class="edge-count">${edges.length}</span>
                    </a>
                `;
                
                const edgeList = document.createElement('div');
                edgeList.className = 'edge-list';
                edgeList.style.display = 'none';
                
                edges.forEach(edge => {
                    const edgeItem = document.createElement('div');
                    edgeItem.className = 'edge-item';
                    edgeItem.innerHTML = `
                        <span class="edge-type">${edge.edgeType}</span>
                        <span class="edge-target" data-node="${edge.to}">→ ${edge.to}</span>
                    `;
                    edgeList.appendChild(edgeItem);
                });
                
                edgeLink.querySelector('.edge-link').addEventListener('click', () => {
                    edgeList.style.display = edgeList.style.display === 'none' ? 'block' : 'none';
                });
                
                nameCell.appendChild(edgeLink);
                nameCell.appendChild(edgeList);
            }
            
            row.appendChild(nameCell);
            
            // NodeType column
            const nodeTypeCell = document.createElement('td');
            nodeTypeCell.textContent = item.nodeType;
            row.appendChild(nodeTypeCell);
            
            // Metadata column
            const metadataCell = document.createElement('td');
            if (item.metadata) {
                const metadataHeader = document.createElement('div');
                metadataHeader.className = 'metadata-header';
                
                const expandButton = document.createElement('button');
                expandButton.className = 'expand-button';
                expandButton.textContent = 'Show Metadata';
                
                const metadataCount = document.createElement('span');
                metadataCount.className = 'metadata-summary';
                metadataCount.textContent = `${item.metadata.length} entries`;
                
                metadataHeader.appendChild(expandButton);
                metadataHeader.appendChild(metadataCount);
                
                const metadataContent = createMetadataDisplay(item.metadata);
                metadataContent.querySelector('.metadata-grid').style.display = 'none';
                
                expandButton.addEventListener('click', () => {
                    const grid = metadataContent.querySelector('.metadata-grid');
                    const isExpanded = grid.style.display !== 'none';
                    grid.style.display = isExpanded ? 'none' : 'grid';
                    expandButton.textContent = isExpanded ? 'Show Metadata' : 'Hide Metadata';
                });
                
                metadataCell.appendChild(metadataHeader);
                metadataCell.appendChild(metadataContent);
            }
            row.appendChild(metadataCell);
            
            tbody.appendChild(row);
        });

        // Add click handlers for edge targets
        document.querySelectorAll('.edge-target').forEach(target => {
            target.addEventListener('click', () => {
                const nodeName = target.dataset.node;
                const nodeRow = [...tbody.rows].find(row => 
                    row.cells[1].textContent.trim() === nodeName
                );
                if (nodeRow) {
                    nodeRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    nodeRow.style.backgroundColor = '#fff2cc';
                    setTimeout(() => {
                        nodeRow.style.backgroundColor = '';
                    }, 2000);
                }
            });
        });
    }

    function updateViews(data) {
        // Update statistics
        updateStats(data);

        // Update table view
        const searchInput = document.getElementById('searchInput');
        const typeFilter = document.getElementById('typeFilter');
        const viewFilter = document.getElementById('viewFilter');
        updateTable(data, searchInput.value, typeFilter.value, viewFilter.value);

        // Update workflow view
        updateWorkflowView(data);

        // Update raw JSON view
        if (jsonEditor) {
            jsonEditor.setValue(JSON.stringify(data, null, 2));
            jsonEditor.clearSelection();
        }
    }

    // --- UI Management ---
    const ui = {
        switchTab: (tabName) => {
            // 停止之前標籤的自動刷新
            if (document.querySelector('.tab.active')?.dataset.tab === 'workflow') {
                onWorkflowTabDeactivated();
            }

            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
            document.querySelector(`.tab[data-tab="${tabName}"]`).classList.add('active');
            document.getElementById(`${tabName}View`).classList.add('active');

            // 啟動新標籤的功能
            if (tabName === 'workflow') {
                onWorkflowTabActivated();
            }
        },
        setupEditor: () => {
            jsonEditor = ace.edit("jsonEditor");
            jsonEditor.setTheme("ace/theme/monokai");
            jsonEditor.session.setMode("ace/mode/json");
            jsonEditor.setOptions({
                fontSize: "14px",
                showPrintMargin: false,
                showGutter: true,
                highlightActiveLine: true,
                readOnly: true
            });
        }
    };

    // --- Event Handlers ---
    function setupEventListeners() {
        document.getElementById('selectMemoryFileBtn').addEventListener('click', fileSystem.selectFile);

        // Tab switching
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => ui.switchTab(tab.dataset.tab));
        });

        // Search and filter handlers
        document.getElementById('searchInput').addEventListener('input', (e) => {
            if (currentData) {
                const typeFilter = document.getElementById('typeFilter').value;
                const viewFilter = document.getElementById('viewFilter').value;
                updateTable(currentData, e.target.value, typeFilter, viewFilter);
            }
        });

        document.getElementById('typeFilter').addEventListener('change', (e) => {
            if (currentData) {
                const searchTerm = document.getElementById('searchInput').value;
                const viewFilter = document.getElementById('viewFilter').value;
                updateTable(currentData, searchTerm, e.target.value, viewFilter);
            }
        });

        document.getElementById('viewFilter').addEventListener('change', (e) => {
            if (currentData) {
                const searchTerm = document.getElementById('searchInput').value;
                const typeFilter = document.getElementById('typeFilter').value;
                updateTable(currentData, searchTerm, typeFilter, e.target.value);
            }
        });
    }

    // --- Initialization ---
    async function initialize() {
        try {
            await idb.initDB();
        } catch (error) {
            showToast('Error initializing database: ' + error.message, 'error');
            return;
        }

        if (!('showOpenFilePicker' in window)) {
            showToast('Your browser does not support the File System Access API. Please use a modern browser like Chrome or Edge.', 'error', 0);
            return;
        }

        try {
            const storedHandle = await idb.getFileHandleFromDB();
            if (storedHandle) {
                const permission = await storedHandle.queryPermission({mode: 'read'});
                if (permission === 'granted') {
                    fileHandle = storedHandle;
                    document.getElementById('filePath').textContent = fileHandle.name;
                    await fileSystem.loadFile();
                    showToast('Loaded previously selected file.', 'success');
                } else {
                    const requestPermission = await storedHandle.requestPermission({mode: 'read'});
                    if (requestPermission === 'granted') {
                        fileHandle = storedHandle;
                        document.getElementById('filePath').textContent = fileHandle.name;
                        await fileSystem.loadFile();
                        showToast('Loaded previously selected file.', 'success');
                    } else {
                        await idb.removeFileHandleFromDB();
                        showToast('Permission to access the previously selected file was denied.', 'error');
                    }
                }
            }
        } catch (error) {
            showToast('Error accessing stored file: ' + error.message, 'error');
        }

        ui.setupEditor();
        setupEventListeners();
    }

    // === 增強工作流程視圖功能 ===

    let workflowData = [];
    let selectedWorkflow = null;
    let statsVisible = false;
    let autoRefreshInterval = null;

    function updateWorkflowView(data) {
        // 顯示更新指示器
        showUpdateIndicator();

        // 提取工作流程相關的節點
        const workflows = data.filter(item =>
            item.type === 'node' && item.nodeType === 'workflow'
        );

        const stages = data.filter(item =>
            item.type === 'node' && item.nodeType === 'stage'
        );

        workflowData = workflows.map(workflow => {
            const workflowId = extractMetadataValue(workflow.metadata, 'workflow_id');
            const relatedStages = stages.filter(stage =>
                extractMetadataValue(stage.metadata, 'workflow_id') === workflowId
            );

            return {
                ...workflow,
                stages: relatedStages,
                status: extractMetadataValue(workflow.metadata, 'status') || 'unknown',
                progress: parseInt(extractMetadataValue(workflow.metadata, 'progress')) || 0,
                type: extractMetadataValue(workflow.metadata, 'category') || 'unknown',
                currentStage: parseInt(extractMetadataValue(workflow.metadata, 'current_stage')) || 0,
                totalStages: parseInt(extractMetadataValue(workflow.metadata, 'total_stages')) || 0,
                createdAt: extractMetadataValue(workflow.metadata, 'created_at') || new Date().toISOString(),
                updatedAt: extractMetadataValue(workflow.metadata, 'updated_at') || new Date().toISOString()
            };
        });

        renderWorkflowList();
        updateWorkflowStats();
        updateStatsBadge();

        // 隱藏更新指示器
        setTimeout(hideUpdateIndicator, 500);
    }

    function showUpdateIndicator() {
        const indicator = document.getElementById('workflowLiveIndicator');
        const dot = indicator.querySelector('.live-dot');
        const text = indicator.querySelector('span');

        indicator.classList.add('updating');
        dot.classList.add('updating');
        text.textContent = '更新中...';
    }

    function hideUpdateIndicator() {
        const indicator = document.getElementById('workflowLiveIndicator');
        const dot = indicator.querySelector('.live-dot');
        const text = indicator.querySelector('span');

        indicator.classList.remove('updating');
        dot.classList.remove('updating');
        text.textContent = '即時更新';
    }

    function updateStatsBadge() {
        const badge = document.getElementById('workflowStatsBadge');
        badge.textContent = workflowData.length;
    }

    function updateWorkflowStats() {
        const total = workflowData.length;
        const active = workflowData.filter(w => w.status === 'in_progress').length;
        const completed = workflowData.filter(w => w.status === 'completed').length;
        const avgProgress = total > 0 ? Math.round(workflowData.reduce((sum, w) => sum + w.progress, 0) / total) : 0;

        document.getElementById('totalWorkflowsStat').textContent = total;
        document.getElementById('activeWorkflowsStat').textContent = active;
        document.getElementById('completedWorkflowsStat').textContent = completed;
        document.getElementById('avgProgressStat').textContent = avgProgress + '%';

        // 更新進度環
        updateProgressRing(avgProgress);
    }

    function updateProgressRing(progress) {
        const ring = document.getElementById('overallProgressRing');
        const text = document.getElementById('overallProgressText');

        if (ring && text) {
            const circumference = 2 * Math.PI * 45; // r=45
            const offset = circumference - (progress / 100) * circumference;

            ring.style.strokeDasharray = circumference;
            ring.style.strokeDashoffset = offset;
            text.textContent = progress + '%';
        }
    }

    function extractMetadataValue(metadata, key) {
        const item = metadata.find(meta => meta.startsWith(key + ':'));
        return item ? item.split(':').slice(1).join(':').trim() : null;
    }

    function renderWorkflowList() {
        const workflowList = document.getElementById('workflowList');

        if (workflowData.length === 0) {
            workflowList.innerHTML = `
                <div class="workflow-placeholder">
                    <p>未找到工作流程節點</p>
                    <p style="font-size: 0.9rem; color: var(--gray-400);">
                        工作流程節點應該具有 nodeType: "workflow"
                    </p>
                </div>
            `;
            return;
        }

        const filteredWorkflows = filterWorkflowData();

        workflowList.innerHTML = filteredWorkflows.map((workflow, index) => `
            <div class="workflow-item ${selectedWorkflow?.name === workflow.name ? 'selected' : ''}"
                 draggable="true"
                 data-workflow-id="${workflow.name}"
                 onclick="selectWorkflow('${workflow.name}')"
                 ondragstart="handleDragStart(event)"
                 ondragover="handleDragOver(event)"
                 ondrop="handleDrop(event)"
                 ondragend="handleDragEnd(event)">

                <div class="workflow-item-actions">
                    <button class="workflow-action-btn" onclick="event.stopPropagation(); pauseWorkflow('${workflow.name}')" title="暫停/恢復">
                        ${workflow.status === 'paused' ? '▶️' : '⏸️'}
                    </button>
                    <button class="workflow-action-btn" onclick="event.stopPropagation(); duplicateWorkflow('${workflow.name}')" title="複製">
                        📋
                    </button>
                    <button class="workflow-action-btn" onclick="event.stopPropagation(); deleteWorkflow('${workflow.name}')" title="刪除">
                        🗑️
                    </button>
                </div>

                <div class="workflow-title">${workflow.name}</div>
                <div class="workflow-meta">
                    <span class="workflow-status ${workflow.status}">${getStatusText(workflow.status)}</span>
                    <span>類型: ${getTypeText(workflow.type)}</span>
                    <span>階段: ${workflow.currentStage + 1}/${workflow.totalStages}</span>
                </div>
                <div class="workflow-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${workflow.progress}%"></div>
                    </div>
                    <div class="progress-text">${workflow.progress}% 完成</div>
                </div>

                ${workflow.updatedAt ? `
                    <div style="font-size: 0.8rem; color: var(--gray-500); margin-top: 0.5rem;">
                        更新: ${formatDate(workflow.updatedAt)}
                    </div>
                ` : ''}
            </div>
        `).join('');

        // 添加拖拽事件監聽器
        setupDragAndDrop();
    }

    // 拖拽功能
    let draggedElement = null;

    function handleDragStart(event) {
        draggedElement = event.target;
        event.target.classList.add('dragging');
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('text/html', event.target.outerHTML);
    }

    function handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';

        const target = event.target.closest('.workflow-item');
        if (target && target !== draggedElement) {
            target.classList.add('drag-over');
        }
    }

    function handleDrop(event) {
        event.preventDefault();

        const target = event.target.closest('.workflow-item');
        if (target && target !== draggedElement) {
            // 重新排序邏輯
            const draggedId = draggedElement.dataset.workflowId;
            const targetId = target.dataset.workflowId;

            reorderWorkflows(draggedId, targetId);
            showToast('工作流程順序已更新', 'success');
        }

        // 清理樣式
        document.querySelectorAll('.workflow-item').forEach(item => {
            item.classList.remove('drag-over');
        });
    }

    function handleDragEnd(event) {
        event.target.classList.remove('dragging');
        draggedElement = null;
    }

    function setupDragAndDrop() {
        // 為新創建的元素設置拖拽事件
        document.querySelectorAll('.workflow-item').forEach(item => {
            item.addEventListener('dragenter', (e) => e.preventDefault());
            item.addEventListener('dragleave', (e) => {
                if (!e.target.closest('.workflow-item')) {
                    e.target.classList.remove('drag-over');
                }
            });
        });
    }

    function reorderWorkflows(draggedId, targetId) {
        const draggedIndex = workflowData.findIndex(w => w.name === draggedId);
        const targetIndex = workflowData.findIndex(w => w.name === targetId);

        if (draggedIndex !== -1 && targetIndex !== -1) {
            const [draggedItem] = workflowData.splice(draggedIndex, 1);
            workflowData.splice(targetIndex, 0, draggedItem);
            renderWorkflowList();
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return '剛剛';
        if (diffMins < 60) return `${diffMins}分鐘前`;
        if (diffHours < 24) return `${diffHours}小時前`;
        if (diffDays < 7) return `${diffDays}天前`;

        return date.toLocaleDateString('zh-TW');
    }

    function filterWorkflowData() {
        const searchTerm = document.getElementById('workflowSearch')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('workflowStatusFilter').value;
        const typeFilter = document.getElementById('workflowTypeFilter').value;
        const sortBy = document.getElementById('workflowSortBy')?.value || 'name';

        let filtered = workflowData.filter(workflow => {
            const searchMatch = !searchTerm ||
                workflow.name.toLowerCase().includes(searchTerm) ||
                workflow.type.toLowerCase().includes(searchTerm) ||
                workflow.status.toLowerCase().includes(searchTerm);

            const statusMatch = statusFilter === 'all' || workflow.status === statusFilter;
            const typeMatch = typeFilter === 'all' || workflow.type === typeFilter;

            return searchMatch && statusMatch && typeMatch;
        });

        // 排序
        filtered.sort((a, b) => {
            switch (sortBy) {
                case 'progress':
                    return b.progress - a.progress;
                case 'status':
                    return a.status.localeCompare(b.status);
                case 'type':
                    return a.type.localeCompare(b.type);
                case 'name':
                default:
                    return a.name.localeCompare(b.name);
            }
        });

        return filtered;
    }

    function selectWorkflow(workflowName) {
        selectedWorkflow = workflowData.find(w => w.name === workflowName);
        renderWorkflowList();
        renderWorkflowDetails();
    }

    function renderWorkflowDetails() {
        const detailsContainer = document.getElementById('workflowDetails');
        const infoContainer = document.getElementById('workflowInfo');
        const progressContainer = document.getElementById('stageProgress');

        if (!selectedWorkflow) {
            detailsContainer.style.display = 'none';
            return;
        }

        detailsContainer.style.display = 'block';

        // 工作流程基本信息
        infoContainer.innerHTML = `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                <div><strong>名稱:</strong> ${selectedWorkflow.name}</div>
                <div><strong>狀態:</strong> <span class="workflow-status ${selectedWorkflow.status}">${getStatusText(selectedWorkflow.status)}</span></div>
                <div><strong>類型:</strong> ${getTypeText(selectedWorkflow.type)}</div>
                <div><strong>進度:</strong> ${selectedWorkflow.progress}%</div>
            </div>
        `;

        // 階段進度
        if (selectedWorkflow.stages && selectedWorkflow.stages.length > 0) {
            progressContainer.innerHTML = `
                <h5>階段進度</h5>
                <div class="stage-list">
                    ${selectedWorkflow.stages.map((stage, index) => {
                        const stageStatus = extractMetadataValue(stage.metadata, 'status') || 'pending';
                        const stageName = extractMetadataValue(stage.metadata, 'stage_name') || `階段 ${index + 1}`;
                        const requirements = extractMetadataValue(stage.metadata, 'required_node_types') || '[]';

                        return `
                            <div class="stage-item ${stageStatus}">
                                <div class="stage-icon ${stageStatus}">
                                    ${stageStatus === 'completed' ? '✓' :
                                      stageStatus === 'active' ? '●' :
                                      index + 1}
                                </div>
                                <div class="stage-name">${stageName}</div>
                                <div class="stage-requirements">
                                    需要: ${JSON.parse(requirements).join(', ') || '無特殊要求'}
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>

                <!-- 🔮 預留擴展區域 -->
                <div class="workflow-extension-panel">
                    <p>預留區域：未來可添加階段操作按鈕、驗證結果、自定義插件等</p>
                </div>
            `;
        } else {
            progressContainer.innerHTML = '<p>未找到相關階段信息</p>';
        }
    }

    function getStatusText(status) {
        const statusMap = {
            'not_started': '未開始',
            'in_progress': '進行中',
            'completed': '已完成',
            'paused': '已暫停',
            'unknown': '未知'
        };
        return statusMap[status] || status;
    }

    function getTypeText(type) {
        const typeMap = {
            'novel': '小說',
            'article': '文章',
            'script': '劇本',
            'custom': '自定義',
            'unknown': '未知'
        };
        return typeMap[type] || type;
    }

    function filterWorkflows() {
        renderWorkflowList();
    }

    function refreshWorkflows() {
        if (currentData) {
            updateWorkflowView(currentData);
            showToast('工作流程視圖已刷新', 'success');
        } else {
            showToast('請先選擇一個memory文件', 'error');
        }
    }

    function showCreateWorkflowModal() {
        // 🔮 預留功能：創建工作流程的模態框
        showToast('創建工作流程功能將在未來版本中實現', 'info');
    }

    // === 新增的增強功能 ===

    function toggleStatsPanel() {
        const panel = document.getElementById('workflowStatsPanel');
        statsVisible = !statsVisible;

        if (statsVisible) {
            panel.style.display = 'block';
            updateWorkflowStats();
            showToast('統計面板已顯示', 'success');
        } else {
            panel.style.display = 'none';
            showToast('統計面板已隱藏', 'info');
        }
    }

    function exportWorkflows() {
        if (workflowData.length === 0) {
            showToast('沒有工作流程可以導出', 'warning');
            return;
        }

        const exportData = {
            exportDate: new Date().toISOString(),
            totalWorkflows: workflowData.length,
            workflows: workflowData.map(workflow => ({
                name: workflow.name,
                type: workflow.type,
                status: workflow.status,
                progress: workflow.progress,
                currentStage: workflow.currentStage,
                totalStages: workflow.totalStages,
                stages: workflow.stages.map(stage => ({
                    name: extractMetadataValue(stage.metadata, 'stage_name'),
                    status: extractMetadataValue(stage.metadata, 'status'),
                    requirements: extractMetadataValue(stage.metadata, 'required_node_types')
                }))
            }))
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `workflows_export_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showToast('工作流程已導出', 'success');
    }

    function pauseWorkflow(workflowName) {
        const workflow = workflowData.find(w => w.name === workflowName);
        if (!workflow) return;

        const newStatus = workflow.status === 'paused' ? 'in_progress' : 'paused';
        workflow.status = newStatus;

        renderWorkflowList();
        updateWorkflowStats();

        const action = newStatus === 'paused' ? '暫停' : '恢復';
        showToast(`工作流程已${action}`, 'success');
    }

    function duplicateWorkflow(workflowName) {
        const workflow = workflowData.find(w => w.name === workflowName);
        if (!workflow) return;

        const duplicatedWorkflow = {
            ...workflow,
            name: `${workflow.name} (副本)`,
            status: 'not_started',
            progress: 0,
            currentStage: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        workflowData.push(duplicatedWorkflow);
        renderWorkflowList();
        updateWorkflowStats();
        updateStatsBadge();

        showToast('工作流程已複製', 'success');
    }

    function deleteWorkflow(workflowName) {
        if (!confirm(`確定要刪除工作流程 "${workflowName}" 嗎？此操作無法撤銷。`)) {
            return;
        }

        const index = workflowData.findIndex(w => w.name === workflowName);
        if (index !== -1) {
            workflowData.splice(index, 1);

            if (selectedWorkflow?.name === workflowName) {
                selectedWorkflow = null;
                document.getElementById('workflowDetails').style.display = 'none';
            }

            renderWorkflowList();
            updateWorkflowStats();
            updateStatsBadge();

            showToast('工作流程已刪除', 'success');
        }
    }

    // 自動刷新功能
    function startAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }

        autoRefreshInterval = setInterval(() => {
            if (currentData) {
                updateWorkflowView(currentData);
            }
        }, 30000); // 每30秒刷新一次
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
    }

    // 當切換到工作流程標籤時啟動自動刷新
    function onWorkflowTabActivated() {
        startAutoRefresh();
        if (currentData) {
            updateWorkflowView(currentData);
        }
    }

    // 當離開工作流程標籤時停止自動刷新
    function onWorkflowTabDeactivated() {
        stopAutoRefresh();
    }

    // --- Start the application ---
    initialize();
</script>
</body>
</html>