// tests/workflow/WorkflowStateManager.test.ts

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { WorkflowStateManager } from '../../src/core/workflow/WorkflowStateManager.js';
import { PredefinedTemplates } from '../../src/core/workflow/WorkflowTemplates.js';
import type { IExtendedStorage } from '../../src/infrastructure/storage/IExtendedStorage.js';

// 模擬存儲實現
class MockExtendedStorage implements IExtendedStorage {
  private workflows = new Map();
  private stages = new Map();
  private nodes = new Map();

  // 實現必需的方法
  async loadGraph() {
    return { nodes: Array.from(this.nodes.values()), edges: [] };
  }

  async saveGraph() {
    // Mock implementation
  }

  async loadEdgesByIds() {
    return [];
  }

  async saveWorkflowState(workflow: any) {
    this.workflows.set(workflow.workflowId, workflow);
  }

  async loadWorkflowState(workflowId: string) {
    return this.workflows.get(workflowId) || null;
  }

  async listWorkflows() {
    return Array.from(this.workflows.values());
  }

  async saveStageState(stage: any) {
    const stages = this.stages.get(stage.workflowId) || [];
    const existingIndex = stages.findIndex((s: any) => s.stageId === stage.stageId);
    if (existingIndex >= 0) {
      stages[existingIndex] = stage;
    } else {
      stages.push(stage);
    }
    this.stages.set(stage.workflowId, stages);
  }

  async loadStageStates(workflowId: string) {
    return this.stages.get(workflowId) || [];
  }

  async queryNodesByWorkflow(workflowId: string) {
    return Array.from(this.nodes.values()).filter((node: any) => 
      node.metadata.some((meta: string) => meta.includes(`workflow_id: ${workflowId}`))
    );
  }

  // 其他必需方法的空實現
  async updateNodesIncremental() {}
  async batchOperation() { return { success: true, results: [], errors: [] }; }
  async configureCaching() {}
  async invalidateCache() {}
  async configureIndexes() {}
  async queryNodesByType() { return []; }
  async registerStoragePlugin() {}
  async executeCustomQuery() { return null; }
  async migrateData() { return { success: true, migratedCount: 0, errors: [] }; }
  onStorageEvent() {}

  // 測試輔助方法
  addTestNode(node: any) {
    this.nodes.set(node.name, node);
  }

  clear() {
    this.workflows.clear();
    this.stages.clear();
    this.nodes.clear();
  }
}

describe('WorkflowStateManager', () => {
  let workflowStateManager: WorkflowStateManager;
  let mockStorage: MockExtendedStorage;

  beforeEach(() => {
    mockStorage = new MockExtendedStorage();
    workflowStateManager = new WorkflowStateManager(mockStorage);
  });

  describe('createWorkflow', () => {
    it('應該成功創建小說工作流程', async () => {
      const template = PredefinedTemplates.NOVEL_TEMPLATE;
      const workflowName = 'Test Novel';
      const customMetadata = { author: 'Test Author' };

      const result = await workflowStateManager.createWorkflow(
        template,
        workflowName,
        customMetadata
      );

      expect(result.workflowId).toBeDefined();
      expect(result.nodes).toHaveLength(5); // 1 workflow + 4 stages
      expect(result.edges).toHaveLength(7); // 3 next_stage + 4 contains_stage

      // 驗證工作流程節點
      const workflowNode = result.nodes.find(n => n.nodeType === 'workflow');
      expect(workflowNode).toBeDefined();
      expect(workflowNode.name).toBe(`workflow_${result.workflowId}`);

      // 驗證階段節點
      const stageNodes = result.nodes.filter(n => n.nodeType === 'stage');
      expect(stageNodes).toHaveLength(4);
      expect(stageNodes[0].name).toContain('planning');
    });

    it('應該正確設置工作流程狀態', async () => {
      const template = PredefinedTemplates.NOVEL_TEMPLATE;
      const result = await workflowStateManager.createWorkflow(template, 'Test');

      const savedWorkflow = await mockStorage.loadWorkflowState(result.workflowId);
      expect(savedWorkflow).toBeValidWorkflow();
      expect(savedWorkflow.status).toBe('not_started');
      expect(savedWorkflow.currentStage).toBe(0);
      expect(savedWorkflow.totalStages).toBe(4);
      expect(savedWorkflow.progress).toBe(0);
    });

    it('應該正確設置階段狀態', async () => {
      const template = PredefinedTemplates.NOVEL_TEMPLATE;
      const result = await workflowStateManager.createWorkflow(template, 'Test');

      const stages = await mockStorage.loadStageStates(result.workflowId);
      expect(stages).toHaveLength(4);
      
      // 第一個階段應該是活躍的
      expect(stages[0]).toBeValidStage();
      expect(stages[0].status).toBe('active');
      expect(stages[0].order).toBe(0);
      
      // 其他階段應該是待處理的
      for (let i = 1; i < stages.length; i++) {
        expect(stages[i].status).toBe('pending');
        expect(stages[i].order).toBe(i);
      }
    });
  });

  describe('advanceToNextStage', () => {
    let workflowId: string;

    beforeEach(async () => {
      const template = PredefinedTemplates.NOVEL_TEMPLATE;
      const result = await workflowStateManager.createWorkflow(template, 'Test');
      workflowId = result.workflowId;

      // 添加一些測試節點來滿足階段要求
      mockStorage.addTestNode({
        name: 'test-character',
        nodeType: 'character',
        metadata: [`workflow_id: ${workflowId}`, 'name: Test Character']
      });
      mockStorage.addTestNode({
        name: 'test-setting',
        nodeType: 'setting',
        metadata: [`workflow_id: ${workflowId}`, 'name: Test Setting']
      });
      mockStorage.addTestNode({
        name: 'test-theme',
        nodeType: 'theme',
        metadata: [`workflow_id: ${workflowId}`, 'name: Test Theme']
      });
    });

    it('應該成功推進到下一階段', async () => {
      const result = await workflowStateManager.advanceToNextStage(workflowId);

      expect(result.workflow.currentStage).toBe(1);
      expect(result.workflow.progress).toBe(25); // 1/4 * 100
      expect(result.nextStage).toBeDefined();
      expect(result.nextStage?.name).toBe('outline');

      // 驗證階段狀態變更
      const stages = await mockStorage.loadStageStates(workflowId);
      expect(stages[0].status).toBe('completed');
      expect(stages[1].status).toBe('active');
    });

    it('應該在最後階段完成工作流程', async () => {
      // 推進到最後階段
      await workflowStateManager.advanceToNextStage(workflowId, { force: true });
      await workflowStateManager.advanceToNextStage(workflowId, { force: true });
      await workflowStateManager.advanceToNextStage(workflowId, { force: true });

      const result = await workflowStateManager.advanceToNextStage(workflowId, { force: true });

      expect(result.workflow.status).toBe('completed');
      expect(result.workflow.progress).toBe(100);
      expect(result.nextStage).toBeUndefined();
    });

    it('應該在要求未滿足時拋出錯誤', async () => {
      // 清除測試節點
      mockStorage.clear();
      const template = PredefinedTemplates.NOVEL_TEMPLATE;
      const result = await workflowStateManager.createWorkflow(template, 'Test');
      
      await expect(
        workflowStateManager.advanceToNextStage(result.workflowId)
      ).rejects.toThrow('is not complete');
    });

    it('應該支持強制推進', async () => {
      // 清除測試節點
      mockStorage.clear();
      const template = PredefinedTemplates.NOVEL_TEMPLATE;
      const result = await workflowStateManager.createWorkflow(template, 'Test');
      
      // 強制推進應該成功
      const advanceResult = await workflowStateManager.advanceToNextStage(
        result.workflowId, 
        { force: true }
      );

      expect(advanceResult.workflow.currentStage).toBe(1);
    });
  });

  describe('validateWorkflow', () => {
    it('應該驗證有效的工作流程', async () => {
      const template = PredefinedTemplates.NOVEL_TEMPLATE;
      const result = await workflowStateManager.createWorkflow(template, 'Test');

      const validation = await workflowStateManager.validateWorkflow(result.workflowId);

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('應該檢測不存在的工作流程', async () => {
      const validation = await workflowStateManager.validateWorkflow('non-existent');

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Workflow not found: non-existent');
    });
  });

  describe('validateStage', () => {
    let workflowId: string;
    let stageId: string;

    beforeEach(async () => {
      const template = PredefinedTemplates.NOVEL_TEMPLATE;
      const result = await workflowStateManager.createWorkflow(template, 'Test');
      workflowId = result.workflowId;
      
      const stages = await mockStorage.loadStageStates(workflowId);
      stageId = stages[0].stageId;
    });

    it('應該驗證階段完成條件', async () => {
      // 添加必需的節點
      mockStorage.addTestNode({
        name: 'test-character',
        nodeType: 'character',
        metadata: [`workflow_id: ${workflowId}`, 'name: Test Character']
      });

      const validation = await workflowStateManager.validateStage(workflowId, stageId);

      expect(validation.stageId).toBe(stageId);
      expect(validation.requirements).toBeDefined();
      expect(validation.requirements.length).toBeGreaterThan(0);
    });

    it('應該檢測缺失的要求', async () => {
      const validation = await workflowStateManager.validateStage(workflowId, stageId);

      expect(validation.isComplete).toBe(false);
      expect(validation.missingRequirements.length).toBeGreaterThan(0);
    });
  });

  describe('事件發射', () => {
    it('應該在創建工作流程時發射事件', async () => {
      const eventSpy = jest.fn();
      workflowStateManager.on('workflow_created', eventSpy);

      const template = PredefinedTemplates.NOVEL_TEMPLATE;
      await workflowStateManager.createWorkflow(template, 'Test');

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          template: template.id,
          timestamp: expect.any(String)
        })
      );
    });

    it('應該在推進階段時發射事件', async () => {
      const stageEventSpy = jest.fn();
      const workflowEventSpy = jest.fn();
      
      workflowStateManager.on('stage_state_changed', stageEventSpy);
      workflowStateManager.on('workflow_completed', workflowEventSpy);

      const template = PredefinedTemplates.NOVEL_TEMPLATE;
      const result = await workflowStateManager.createWorkflow(template, 'Test');

      await workflowStateManager.advanceToNextStage(result.workflowId, { force: true });

      expect(stageEventSpy).toHaveBeenCalled();
    });
  });
});
