#!/usr/bin/env node
// scripts/run-tests.js
// 自動化測試腳本

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

class TestRunner {
    constructor() {
        this.results = {
            unit: null,
            integration: null,
            coverage: null,
            performance: null
        };
        this.startTime = Date.now();
    }

    async runCommand(command, args = [], options = {}) {
        return new Promise((resolve, reject) => {
            console.log(`🔧 執行: ${command} ${args.join(' ')}`);
            
            const process = spawn(command, args, {
                cwd: projectRoot,
                stdio: 'pipe',
                shell: true,
                ...options
            });

            let stdout = '';
            let stderr = '';

            process.stdout.on('data', (data) => {
                const output = data.toString();
                stdout += output;
                console.log(output.trim());
            });

            process.stderr.on('data', (data) => {
                const output = data.toString();
                stderr += output;
                console.error(output.trim());
            });

            process.on('close', (code) => {
                if (code === 0) {
                    resolve({ stdout, stderr, code });
                } else {
                    reject(new Error(`Command failed with code ${code}: ${stderr}`));
                }
            });

            process.on('error', (error) => {
                reject(error);
            });
        });
    }

    async checkPrerequisites() {
        console.log('📋 檢查測試前置條件...');
        
        // 檢查是否已構建
        const distPath = path.join(projectRoot, 'dist');
        if (!fs.existsSync(distPath)) {
            console.log('🔨 項目未構建，開始構建...');
            await this.runCommand('npm', ['run', 'build']);
        }

        // 檢查測試依賴
        const packageJsonPath = path.join(projectRoot, 'package.json');
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
        
        const requiredDeps = ['jest', 'ts-jest', '@types/jest'];
        const missingDeps = requiredDeps.filter(dep => 
            !packageJson.devDependencies?.[dep] && !packageJson.dependencies?.[dep]
        );

        if (missingDeps.length > 0) {
            console.log(`📦 安裝缺少的測試依賴: ${missingDeps.join(', ')}`);
            await this.runCommand('npm', ['install', '--save-dev', ...missingDeps]);
        }

        console.log('✅ 前置條件檢查完成');
    }

    async runUnitTests() {
        console.log('\n🧪 運行單元測試...');
        
        try {
            const result = await this.runCommand('npm', ['test', '--', '--testPathPattern=unit', '--verbose']);
            this.results.unit = {
                success: true,
                output: result.stdout,
                duration: this.extractDuration(result.stdout)
            };
            console.log('✅ 單元測試完成');
        } catch (error) {
            this.results.unit = {
                success: false,
                error: error.message,
                duration: 0
            };
            console.log('❌ 單元測試失敗');
        }
    }

    async runIntegrationTests() {
        console.log('\n🔗 運行集成測試...');
        
        try {
            const result = await this.runCommand('npm', ['test', '--', '--testPathPattern=integration', '--verbose']);
            this.results.integration = {
                success: true,
                output: result.stdout,
                duration: this.extractDuration(result.stdout)
            };
            console.log('✅ 集成測試完成');
        } catch (error) {
            this.results.integration = {
                success: false,
                error: error.message,
                duration: 0
            };
            console.log('❌ 集成測試失敗');
        }
    }

    async runCoverageTests() {
        console.log('\n📊 運行覆蓋率測試...');
        
        try {
            const result = await this.runCommand('npm', ['test', '--', '--coverage', '--coverageReporters=text', '--coverageReporters=json']);
            this.results.coverage = {
                success: true,
                output: result.stdout,
                coverage: this.extractCoverage(result.stdout)
            };
            console.log('✅ 覆蓋率測試完成');
        } catch (error) {
            this.results.coverage = {
                success: false,
                error: error.message,
                coverage: null
            };
            console.log('❌ 覆蓋率測試失敗');
        }
    }

    async runPerformanceTests() {
        console.log('\n⚡ 運行性能測試...');
        
        try {
            const result = await this.runCommand('npm', ['test', '--', '--testPathPattern=performance', '--verbose']);
            this.results.performance = {
                success: true,
                output: result.stdout,
                duration: this.extractDuration(result.stdout)
            };
            console.log('✅ 性能測試完成');
        } catch (error) {
            this.results.performance = {
                success: false,
                error: error.message,
                duration: 0
            };
            console.log('❌ 性能測試失敗');
        }
    }

    extractDuration(output) {
        const match = output.match(/Time:\s*(\d+\.?\d*)\s*s/);
        return match ? parseFloat(match[1]) : 0;
    }

    extractCoverage(output) {
        const lines = output.split('\n');
        const coverageLine = lines.find(line => line.includes('All files'));
        
        if (coverageLine) {
            const parts = coverageLine.split('|').map(p => p.trim());
            if (parts.length >= 5) {
                return {
                    statements: parseFloat(parts[1]) || 0,
                    branches: parseFloat(parts[2]) || 0,
                    functions: parseFloat(parts[3]) || 0,
                    lines: parseFloat(parts[4]) || 0
                };
            }
        }
        
        return null;
    }

    generateReport() {
        const totalDuration = Date.now() - this.startTime;
        
        console.log('\n' + '='.repeat(80));
        console.log('📋 測試執行報告');
        console.log('='.repeat(80));
        console.log(`總執行時間: ${(totalDuration / 1000).toFixed(2)}秒`);
        console.log(`執行時間: ${new Date().toLocaleString()}`);
        
        // 單元測試結果
        console.log('\n🧪 單元測試:');
        if (this.results.unit) {
            console.log(`  狀態: ${this.results.unit.success ? '✅ 通過' : '❌ 失敗'}`);
            if (this.results.unit.duration) {
                console.log(`  耗時: ${this.results.unit.duration}秒`);
            }
            if (!this.results.unit.success) {
                console.log(`  錯誤: ${this.results.unit.error}`);
            }
        } else {
            console.log('  狀態: ⏭️ 跳過');
        }

        // 集成測試結果
        console.log('\n🔗 集成測試:');
        if (this.results.integration) {
            console.log(`  狀態: ${this.results.integration.success ? '✅ 通過' : '❌ 失敗'}`);
            if (this.results.integration.duration) {
                console.log(`  耗時: ${this.results.integration.duration}秒`);
            }
            if (!this.results.integration.success) {
                console.log(`  錯誤: ${this.results.integration.error}`);
            }
        } else {
            console.log('  狀態: ⏭️ 跳過');
        }

        // 覆蓋率結果
        console.log('\n📊 代碼覆蓋率:');
        if (this.results.coverage) {
            console.log(`  狀態: ${this.results.coverage.success ? '✅ 通過' : '❌ 失敗'}`);
            if (this.results.coverage.coverage) {
                const cov = this.results.coverage.coverage;
                console.log(`  語句覆蓋率: ${cov.statements.toFixed(1)}%`);
                console.log(`  分支覆蓋率: ${cov.branches.toFixed(1)}%`);
                console.log(`  函數覆蓋率: ${cov.functions.toFixed(1)}%`);
                console.log(`  行覆蓋率: ${cov.lines.toFixed(1)}%`);
                
                // 檢查是否達到目標
                const target = 90;
                const avgCoverage = (cov.statements + cov.branches + cov.functions + cov.lines) / 4;
                console.log(`  平均覆蓋率: ${avgCoverage.toFixed(1)}% (目標: ${target}%)`);
                
                if (avgCoverage >= target) {
                    console.log('  🎯 達到覆蓋率目標！');
                } else {
                    console.log('  ⚠️ 未達到覆蓋率目標');
                }
            }
            if (!this.results.coverage.success) {
                console.log(`  錯誤: ${this.results.coverage.error}`);
            }
        } else {
            console.log('  狀態: ⏭️ 跳過');
        }

        // 性能測試結果
        console.log('\n⚡ 性能測試:');
        if (this.results.performance) {
            console.log(`  狀態: ${this.results.performance.success ? '✅ 通過' : '❌ 失敗'}`);
            if (this.results.performance.duration) {
                console.log(`  耗時: ${this.results.performance.duration}秒`);
            }
            if (!this.results.performance.success) {
                console.log(`  錯誤: ${this.results.performance.error}`);
            }
        } else {
            console.log('  狀態: ⏭️ 跳過');
        }

        // 總結
        const allTests = [this.results.unit, this.results.integration, this.results.coverage, this.results.performance];
        const runTests = allTests.filter(t => t !== null);
        const passedTests = runTests.filter(t => t.success);
        
        console.log('\n📈 總結:');
        console.log(`  執行測試: ${runTests.length}`);
        console.log(`  通過測試: ${passedTests.length}`);
        console.log(`  失敗測試: ${runTests.length - passedTests.length}`);
        console.log(`  成功率: ${runTests.length > 0 ? ((passedTests.length / runTests.length) * 100).toFixed(1) : 0}%`);
        
        if (passedTests.length === runTests.length && runTests.length > 0) {
            console.log('\n🎉 所有測試通過！');
            return 0;
        } else {
            console.log('\n❌ 部分測試失敗');
            return 1;
        }
    }

    async saveReport() {
        const reportData = {
            timestamp: new Date().toISOString(),
            duration: Date.now() - this.startTime,
            results: this.results
        };

        const reportPath = path.join(projectRoot, 'test-results.json');
        fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
        console.log(`📄 測試報告已保存到: ${reportPath}`);
    }
}

// 主函數
async function main() {
    const args = process.argv.slice(2);
    const runner = new TestRunner();

    try {
        await runner.checkPrerequisites();

        // 根據參數決定運行哪些測試
        if (args.includes('--unit') || args.length === 0) {
            await runner.runUnitTests();
        }

        if (args.includes('--integration') || args.length === 0) {
            await runner.runIntegrationTests();
        }

        if (args.includes('--coverage') || args.length === 0) {
            await runner.runCoverageTests();
        }

        if (args.includes('--performance') || args.length === 0) {
            await runner.runPerformanceTests();
        }

        const exitCode = runner.generateReport();
        await runner.saveReport();

        process.exit(exitCode);

    } catch (error) {
        console.error('❌ 測試執行失敗:', error.message);
        process.exit(1);
    }
}

// 如果直接運行此腳本
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export { TestRunner };
