# 🔄 MemoryMesh 工作流程管理使用指南

本文檔提供MemoryMesh工作流程管理功能的完整使用示例和最佳實踐指南。

## 📋 目錄

- [快速開始](#快速開始)
- [創作類型指南](#創作類型指南)
- [工具使用示例](#工具使用示例)
- [Prompt模板](#prompt模板)
- [常見問題](#常見問題)
- [最佳實踐](#最佳實踐)

---

## 🚀 快速開始

### 基本工作流程

1. **創建工作流程**
2. **查看狀態和階段**
3. **添加內容節點**
4. **推進階段**
5. **完成項目**

### 第一個工作流程示例

```bash
# 1. 創建小說工作流程
workflow_create {
  "name": "我的第一部小說",
  "type": "novel"
}

# 2. 查看工作流程狀態
workflow_status {
  "workflowId": "wf_xxx_xxx"
}

# 3. 添加角色節點
add_character {
  "character": {
    "name": "主角李明",
    "role": "protagonist",
    "status": "Active",
    "currentLocation": ["北京"],
    "description": "一個充滿理想的年輕程序員"
  }
}

# 4. 推進到下一階段
workflow_advance {
  "workflowId": "wf_xxx_xxx"
}
```

---

## 📖 創作類型指南

### 小說創作工作流程

#### 階段說明
1. **規劃階段** - 建立角色、設定、主題
2. **大綱階段** - 制定情節結構和時間線
3. **章節階段** - 規劃具體場景和對話
4. **生成階段** - 創作完整文本內容

#### 完整示例

```bash
# 創建小說項目
workflow_create {
  "name": "科幻小說：未來城市",
  "type": "novel",
  "metadata": {
    "genre": "科幻",
    "targetLength": "80000字",
    "targetAudience": "成人讀者"
  }
}

# 規劃階段 - 添加主要角色
add_character {
  "character": {
    "name": "艾莉絲",
    "role": "protagonist",
    "status": "Active",
    "currentLocation": ["新東京"],
    "description": "2087年的網絡安全專家",
    "background": "在虛擬現實技術發達的未來世界中工作",
    "motivation": "揭露大型科技公司的陰謀",
    "traits": ["聰明", "勇敢", "技術專家"],
    "abilities": ["黑客技能", "虛擬現實操作", "數據分析"]
  }
}

# 添加反派角色
add_character {
  "character": {
    "name": "維克多·陳",
    "role": "antagonist", 
    "status": "Active",
    "currentLocation": ["新東京", "企業總部"],
    "description": "大型科技公司的CEO",
    "motivation": "控制虛擬世界的所有數據",
    "traits": ["野心勃勃", "冷酷", "有魅力"],
    "weaknesses": ["過度自信", "忽視人性"]
  }
}

# 添加世界設定
add_setting {
  "setting": {
    "name": "新東京",
    "type": "Urban",
    "description": "2087年的超級都市，充滿全息投影和飛行汽車",
    "status": "Active",
    "significance": "Critical",
    "atmosphere": "未來主義與傳統文化的融合",
    "notableFeatures": ["全息廣告牌", "空中交通網", "地下數據中心"],
    "symbolicMeaning": "技術進步與人性的衝突"
  }
}

# 添加主題
add_theme {
  "theme": {
    "name": "技術與人性",
    "type": "Main Theme",
    "description": "探討先進技術對人類社會和個人身份的影響",
    "status": "Introduced",
    "importance": "Core",
    "currentDevelopment": "通過角色衝突展現技術的雙面性"
  }
}

# 驗證規劃階段完成
stage_validate {
  "workflowId": "wf_xxx_xxx"
}

# 推進到大綱階段
workflow_advance {
  "workflowId": "wf_xxx_xxx"
}

# 大綱階段 - 添加主要情節線
add_plotarc {
  "plotarc": {
    "name": "主線：揭露陰謀",
    "type": "Main Plot",
    "description": "艾莉絲發現並揭露維克多公司的數據操控陰謀",
    "status": "Planning",
    "importance": "Critical",
    "progressPercentage": "0",
    "mainCharacters": ["艾莉絲", "維克多·陳"],
    "incitingIncident": "艾莉絲發現異常的數據流量",
    "climax": "在虛擬世界中的最終對決",
    "resolution": "真相大白，系統重置"
  }
}

# 繼續推進工作流程...
```

### 文章創作工作流程

#### 階段說明
1. **研究階段** - 收集資料和參考文獻
2. **大綱階段** - 制定文章結構
3. **草稿階段** - 撰寫初稿
4. **審查階段** - 校對和完善

#### 示例：技術文章

```bash
# 創建技術文章項目
workflow_create {
  "name": "AI在醫療診斷中的應用",
  "type": "article",
  "metadata": {
    "category": "技術文章",
    "targetLength": "3000字",
    "deadline": "2024-02-15"
  }
}

# 研究階段 - 添加參考資料
add_nodes {
  "nodes": [
    {
      "name": "參考文獻1",
      "nodeType": "reference",
      "metadata": [
        "title: Deep Learning in Medical Imaging",
        "author: Smith et al.",
        "year: 2023",
        "source: Nature Medicine",
        "key_points: CNN在醫學影像分析中的突破"
      ]
    },
    {
      "name": "研究筆記1", 
      "nodeType": "research_note",
      "metadata": [
        "topic: AI診斷準確率",
        "content: 最新研究顯示AI在某些疾病診斷上超過人類醫生",
        "importance: high"
      ]
    }
  ]
}

# 大綱階段 - 制定文章結構
add_nodes {
  "nodes": [
    {
      "name": "文章大綱",
      "nodeType": "outline", 
      "metadata": [
        "structure: 引言-現狀-技術-案例-挑戰-未來",
        "main_points: AI技術發展,實際應用案例,面臨的挑戰",
        "target_audience: 醫療從業者和技術人員"
      ]
    }
  ]
}
```

### 劇本創作工作流程

#### 階段說明
1. **概念階段** - 確定故事概念和主題
2. **處理階段** - 制定詳細故事大綱
3. **劇本階段** - 撰寫完整劇本
4. **修訂階段** - 修改和完善

#### 示例：短片劇本

```bash
# 創建短片劇本項目
workflow_create {
  "name": "短片：最後一班地鐵",
  "type": "script",
  "metadata": {
    "format": "短片劇本",
    "duration": "15分鐘",
    "genre": "懸疑"
  }
}

# 概念階段
add_theme {
  "theme": {
    "name": "都市孤獨",
    "type": "Main Theme",
    "description": "現代都市中人與人之間的疏離感",
    "status": "Introduced",
    "importance": "Core"
  }
}

add_character {
  "character": {
    "name": "小雨",
    "role": "protagonist",
    "status": "Active", 
    "currentLocation": ["地鐵站"],
    "description": "深夜加班的上班族",
    "internalConflict": "對生活現狀的不滿和對改變的恐懼"
  }
}
```

---

## 🛠️ 工具使用示例

### 核心工具詳解

#### workflow_create - 創建工作流程
```bash
# 基本用法
workflow_create {
  "name": "項目名稱",
  "type": "novel|article|script|custom"
}

# 高級用法
workflow_create {
  "name": "高級小說項目",
  "type": "novel",
  "templateId": "novel_standard_v1",
  "metadata": {
    "author": "作者名",
    "genre": "奇幻",
    "targetWordCount": 100000,
    "deadline": "2024-06-01"
  }
}

# 自定義工作流程
workflow_create {
  "name": "自定義創作流程",
  "type": "custom",
  "customStages": ["靈感收集", "概念發展", "原型製作", "測試反饋", "最終完善"]
}
```

#### workflow_status - 查看狀態
```bash
# 基本狀態查詢
workflow_status {
  "workflowId": "wf_xxx_xxx"
}

# 詳細狀態查詢
workflow_status {
  "workflowId": "wf_xxx_xxx",
  "includeStages": true,
  "includeValidation": true
}
```

#### workflow_advance - 推進階段
```bash
# 正常推進（需滿足完成條件）
workflow_advance {
  "workflowId": "wf_xxx_xxx"
}

# 強制推進（跳過驗證）
workflow_advance {
  "workflowId": "wf_xxx_xxx",
  "force": true,
  "skipValidation": true
}
```

#### stage_validate - 驗證階段
```bash
# 驗證當前階段
stage_validate {
  "workflowId": "wf_xxx_xxx"
}

# 驗證特定階段
stage_validate {
  "workflowId": "wf_xxx_xxx", 
  "stageId": "wf_xxx_xxx_stage_planning",
  "detailed": true
}
```

#### workflow_list - 列出工作流程
```bash
# 列出所有工作流程
workflow_list {}

# 按條件過濾
workflow_list {
  "status": "in_progress",
  "type": "novel",
  "limit": 10,
  "sortBy": "updated_at",
  "sortOrder": "desc"
}
```

---

## 💬 Prompt模板

### 創建小說項目的完整Prompt

```
我想創建一個新的小說項目，請幫我設置完整的工作流程：

1. 創建名為"[小說名稱]"的小說工作流程
2. 添加主角：[角色描述]
3. 添加主要設定：[世界觀描述]  
4. 添加核心主題：[主題描述]
5. 檢查規劃階段是否完成
6. 如果完成，推進到大綱階段

請使用MemoryMesh的工作流程工具來執行這些步驟。
```

### 推進工作流程的Prompt

```
請幫我檢查工作流程"[工作流程ID]"的當前狀態：

1. 查看當前階段和進度
2. 驗證當前階段的完成條件
3. 如果條件滿足，推進到下一階段
4. 如果條件不滿足，告訴我還需要添加什麼內容

請提供詳細的狀態報告和建議。
```

### 內容創作的Prompt

```
基於我的工作流程"[工作流程ID]"，請幫我：

1. 查看當前階段需要的內容類型
2. 根據已有的角色和設定，創建[具體內容類型]
3. 將創建的內容添加到知識圖譜中
4. 檢查是否可以推進到下一階段

請確保新內容與現有設定保持一致。
```

---

## ❓ 常見問題

### Q: 如何恢復意外刪除的工作流程？
A: 目前版本不支持恢復已刪除的工作流程。建議：
- 定期備份memory.json文件
- 使用workflow_export導出重要項目（未來版本）
- 謹慎使用workflow_delete命令

### Q: 可以同時進行多個工作流程嗎？
A: 是的，MemoryMesh支持並行管理多個工作流程。使用workflow_list查看所有活躍項目。

### Q: 如何自定義階段要求？
A: 創建custom類型工作流程時可以自定義階段。未來版本將支持更靈活的階段配置。

### Q: 工作流程數據存儲在哪裡？
A: 工作流程數據存儲在memory.json文件中，與其他節點數據一起管理。

### Q: 如何與團隊共享工作流程？
A: 目前可以通過共享memory.json文件實現。未來版本將支持雲端同步和協作功能。

---

## 🎯 最佳實踐

### 項目組織建議

1. **命名規範**
   - 使用描述性的工作流程名稱
   - 包含項目類型和版本信息
   - 例如："科幻小說_星際旅行_v1"

2. **階段管理**
   - 不要跳過階段驗證
   - 每個階段都要有足夠的內容支撐
   - 定期檢查工作流程狀態

3. **內容組織**
   - 使用一致的命名約定
   - 添加詳細的metadata
   - 建立清晰的關係連接

### 效率提升技巧

1. **使用模板**
   - 為常用項目類型創建標準模板
   - 複用成功的工作流程結構

2. **批量操作**
   - 一次性添加多個相關節點
   - 使用add_nodes批量創建內容

3. **定期檢查**
   - 使用workflow_status監控進度
   - 及時解決階段驗證問題

### 質量保證

1. **內容完整性**
   - 確保每個階段都有必需的內容
   - 使用stage_validate驗證完成條件

2. **關係一致性**
   - 保持角色、設定、情節的邏輯一致
   - 定期檢查節點間的關係

3. **版本控制**
   - 重要節點添加版本信息
   - 記錄重大修改的原因和時間

---

## 🔗 相關資源

- [擴展接口文檔](docs/EXTENSION_INTERFACES.md)
- [MemoryViewer使用指南](MemoryViewer.html)
- [MCP協議文檔](https://modelcontextprotocol.io/)

---

**提示**: 這個工作流程管理系統是為了提升創作效率而設計的。根據你的具體需求調整使用方式，不要被工具限制了創造力！
