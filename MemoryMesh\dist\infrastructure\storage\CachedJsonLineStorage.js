// src/infrastructure/storage/CachedJsonLineStorage.ts
import { JsonLineStorage } from './JsonLineStorage.js';
import { EventEmitter } from 'events';
/**
 * 帶緩存的JSON Line存儲實現
 * 在原有JsonLineStorage基礎上添加性能優化和擴展功能
 */
export class CachedJsonLineStorage extends JsonLineStorage {
    nodeCache = new Map();
    workflowCache = new Map();
    stageCache = new Map();
    eventEmitter = new EventEmitter();
    // 索引
    nodeTypeIndex = new Map();
    workflowNodeIndex = new Map();
    // 🔧 性能監控和內存管理
    cacheTimestamps = new Map();
    cleanupInterval = null;
    memoryUsageThreshold = 100 * 1024 * 1024; // 100MB
    // 配置
    cacheConfig = {
        maxSize: 1000,
        ttl: 300000, // 5分鐘
        enablePersistence: true
    };
    indexConfig = {
        nodeNameIndex: true,
        nodeTypeIndex: true,
        workflowIndex: true,
        customIndexes: []
    };
    // 🔮 插件系統預留
    plugins = new Map();
    constructor() {
        super();
        this.initializeIndexes();
        this.startCleanupTimer();
        // 監聽進程退出事件以清理資源
        process.on('beforeExit', () => this.cleanup());
        process.on('SIGINT', () => this.cleanup());
        process.on('SIGTERM', () => this.cleanup());
    }
    // === 性能優化實現 ===
    /**
     * 增量更新節點實現
     */
    async updateNodesIncremental(updates) {
        try {
            const graph = await this.loadGraph();
            // 更新緩存和圖數據
            for (const update of updates) {
                if (!update.name)
                    continue;
                const existingNodeIndex = graph.nodes.findIndex(n => n.name === update.name);
                if (existingNodeIndex >= 0) {
                    // 更新現有節點
                    graph.nodes[existingNodeIndex] = { ...graph.nodes[existingNodeIndex], ...update };
                    // 更新緩存和時間戳
                    this.nodeCache.set(update.name, graph.nodes[existingNodeIndex]);
                    this.cacheTimestamps.set(update.name, Date.now());
                    // 更新索引
                    this.updateNodeIndexes(graph.nodes[existingNodeIndex]);
                }
            }
            // 保存更新後的圖
            await this.saveGraph(graph);
            // 發送事件
            this.emitStorageEvent('nodes_updated', { updates });
        }
        catch (error) {
            throw new Error(`Incremental update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * 批量操作實現
     */
    async batchOperation(operations) {
        const results = [];
        const errors = [];
        try {
            const graph = await this.loadGraph();
            for (const operation of operations) {
                try {
                    switch (operation.type) {
                        case 'create':
                            if (operation.target === 'node') {
                                graph.nodes.push(operation.data);
                                this.nodeCache.set(operation.data.name, operation.data);
                                this.cacheTimestamps.set(operation.data.name, Date.now());
                                this.updateNodeIndexes(operation.data);
                            }
                            results.push({ success: true, data: operation.data });
                            break;
                        case 'update':
                            if (operation.target === 'node') {
                                const nodeIndex = graph.nodes.findIndex(n => n.name === operation.data.name);
                                if (nodeIndex >= 0) {
                                    graph.nodes[nodeIndex] = { ...graph.nodes[nodeIndex], ...operation.data };
                                    this.nodeCache.set(operation.data.name, graph.nodes[nodeIndex]);
                                    this.updateNodeIndexes(graph.nodes[nodeIndex]);
                                }
                            }
                            results.push({ success: true, data: operation.data });
                            break;
                        case 'delete':
                            if (operation.target === 'node') {
                                graph.nodes = graph.nodes.filter(n => n.name !== operation.data.name);
                                this.nodeCache.delete(operation.data.name);
                                this.removeFromIndexes(operation.data.name);
                            }
                            results.push({ success: true, data: operation.data });
                            break;
                    }
                }
                catch (error) {
                    errors.push(error instanceof Error ? error : new Error('Unknown operation error'));
                }
            }
            // 保存批量更新
            await this.saveGraph(graph);
            return {
                success: errors.length === 0,
                results,
                errors
            };
        }
        catch (error) {
            return {
                success: false,
                results,
                errors: [error instanceof Error ? error : new Error('Batch operation failed')]
            };
        }
    }
    /**
     * 緩存配置
     */
    async configureCaching(config) {
        this.cacheConfig = { ...this.cacheConfig, ...config };
        // 如果緩存大小超限，清理舊數據
        if (this.nodeCache.size > config.maxSize) {
            const entries = Array.from(this.nodeCache.entries());
            const toKeep = entries.slice(-config.maxSize);
            this.nodeCache.clear();
            toKeep.forEach(([key, value]) => this.nodeCache.set(key, value));
        }
    }
    /**
     * 緩存失效
     */
    async invalidateCache(keys) {
        if (keys) {
            keys.forEach(key => this.nodeCache.delete(key));
        }
        else {
            this.nodeCache.clear();
            this.workflowCache.clear();
            this.stageCache.clear();
        }
    }
    // === 工作流程相關實現 ===
    async saveWorkflowState(workflow) {
        this.workflowCache.set(workflow.workflowId, workflow);
        // 這裡可以實現持久化邏輯
        this.emitStorageEvent('workflow_saved', { workflow });
    }
    async loadWorkflowState(workflowId) {
        return this.workflowCache.get(workflowId) || null;
    }
    async listWorkflows() {
        return Array.from(this.workflowCache.values());
    }
    async saveStageState(stage) {
        const stages = this.stageCache.get(stage.workflowId) || [];
        const existingIndex = stages.findIndex(s => s.stageId === stage.stageId);
        if (existingIndex >= 0) {
            stages[existingIndex] = stage;
        }
        else {
            stages.push(stage);
        }
        this.stageCache.set(stage.workflowId, stages);
        this.emitStorageEvent('stage_saved', { stage });
    }
    async loadStageStates(workflowId) {
        return this.stageCache.get(workflowId) || [];
    }
    // === 索引和查詢優化 ===
    async configureIndexes(config) {
        this.indexConfig = { ...this.indexConfig, ...config };
        await this.rebuildIndexes();
    }
    async queryNodesByType(nodeType, limit) {
        const nodeNames = this.nodeTypeIndex.get(nodeType) || new Set();
        const nodes = [];
        let count = 0;
        for (const nodeName of nodeNames) {
            if (limit && count >= limit)
                break;
            const node = this.nodeCache.get(nodeName);
            if (node) {
                nodes.push(node);
                count++;
            }
        }
        return nodes;
    }
    async queryNodesByWorkflow(workflowId) {
        const nodeNames = this.workflowNodeIndex.get(workflowId) || new Set();
        const nodes = [];
        for (const nodeName of nodeNames) {
            const node = this.nodeCache.get(nodeName);
            if (node) {
                nodes.push(node);
            }
        }
        return nodes;
    }
    // === 🔮 未來擴展接口實現 ===
    async registerStoragePlugin(plugin) {
        this.plugins.set(plugin.name, plugin);
        await plugin.initialize(this);
        this.emitStorageEvent('plugin_registered', { plugin: plugin.name });
    }
    async executeCustomQuery(query) {
        // 預留自定義查詢實現
        // 可以通過插件系統擴展
        throw new Error('Custom query not implemented yet');
    }
    async migrateData(fromVersion, toVersion) {
        // 預留數據遷移實現
        return {
            success: true,
            migratedCount: 0,
            errors: []
        };
    }
    onStorageEvent(event, handler) {
        this.eventEmitter.on(event, handler);
    }
    // === 私有輔助方法 ===
    initializeIndexes() {
        // 初始化索引結構
    }
    async rebuildIndexes() {
        const graph = await this.loadGraph();
        // 重建節點類型索引
        this.nodeTypeIndex.clear();
        graph.nodes.forEach(node => {
            this.updateNodeIndexes(node);
        });
    }
    updateNodeIndexes(node) {
        // 更新節點類型索引
        if (!this.nodeTypeIndex.has(node.nodeType)) {
            this.nodeTypeIndex.set(node.nodeType, new Set());
        }
        this.nodeTypeIndex.get(node.nodeType)?.add(node.name);
        // 更新工作流程索引（如果節點屬於某個工作流程）
        const workflowId = this.extractWorkflowId(node);
        if (workflowId) {
            if (!this.workflowNodeIndex.has(workflowId)) {
                this.workflowNodeIndex.set(workflowId, new Set());
            }
            this.workflowNodeIndex.get(workflowId)?.add(node.name);
        }
    }
    removeFromIndexes(nodeName) {
        // 從所有索引中移除節點
        this.nodeTypeIndex.forEach(nodeSet => nodeSet.delete(nodeName));
        this.workflowNodeIndex.forEach(nodeSet => nodeSet.delete(nodeName));
    }
    extractWorkflowId(node) {
        // 從節點metadata中提取工作流程ID
        const workflowMeta = node.metadata.find(meta => meta.startsWith('workflow_id:'));
        return workflowMeta ? workflowMeta.split(':')[1].trim() : null;
    }
    emitStorageEvent(type, data) {
        const event = {
            type,
            timestamp: new Date().toISOString(),
            data
        };
        this.eventEmitter.emit(type, event);
    }
    // === 🔧 性能優化和內存管理方法 ===
    /**
     * 啟動定期清理計時器
     */
    startCleanupTimer() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        // 每分鐘執行一次清理
        this.cleanupInterval = setInterval(() => {
            this.performCleanup();
        }, 60000);
    }
    /**
     * 執行緩存清理
     */
    performCleanup() {
        const now = Date.now();
        const ttl = this.cacheConfig.ttl;
        // 清理過期的節點緩存
        for (const [key, timestamp] of this.cacheTimestamps.entries()) {
            if (now - timestamp > ttl) {
                this.nodeCache.delete(key);
                this.cacheTimestamps.delete(key);
            }
        }
        // 檢查內存使用量
        const memoryUsage = process.memoryUsage();
        if (memoryUsage.heapUsed > this.memoryUsageThreshold) {
            this.performEmergencyCleanup();
        }
        // 限制緩存大小
        if (this.nodeCache.size > this.cacheConfig.maxSize) {
            this.evictOldestEntries();
        }
    }
    /**
     * 緊急清理（內存使用過高時）
     */
    performEmergencyCleanup() {
        console.warn('[CachedJsonLineStorage] High memory usage detected, performing emergency cleanup');
        // 清理一半的緩存
        const entries = Array.from(this.nodeCache.entries());
        const toRemove = Math.floor(entries.length / 2);
        for (let i = 0; i < toRemove; i++) {
            const [key] = entries[i];
            this.nodeCache.delete(key);
            this.cacheTimestamps.delete(key);
        }
        // 清理工作流程緩存
        if (this.workflowCache.size > 50) {
            const workflowEntries = Array.from(this.workflowCache.entries());
            const workflowsToRemove = Math.floor(workflowEntries.length / 2);
            for (let i = 0; i < workflowsToRemove; i++) {
                const [key] = workflowEntries[i];
                this.workflowCache.delete(key);
            }
        }
    }
    /**
     * 驅逐最舊的緩存條目
     */
    evictOldestEntries() {
        const sortedEntries = Array.from(this.cacheTimestamps.entries())
            .sort(([, a], [, b]) => a - b);
        const toRemove = this.nodeCache.size - this.cacheConfig.maxSize + 100; // 多清理一些
        for (let i = 0; i < toRemove && i < sortedEntries.length; i++) {
            const [key] = sortedEntries[i];
            this.nodeCache.delete(key);
            this.cacheTimestamps.delete(key);
        }
    }
    /**
     * 獲取緩存統計信息
     */
    getCacheStats() {
        return {
            nodeCache: this.nodeCache.size,
            workflowCache: this.workflowCache.size,
            stageCache: this.stageCache.size,
            memoryUsage: process.memoryUsage(),
            indexSizes: {
                nodeTypeIndex: this.nodeTypeIndex.size,
                workflowNodeIndex: this.workflowNodeIndex.size
            }
        };
    }
    /**
     * 清理所有資源
     */
    cleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        this.nodeCache.clear();
        this.workflowCache.clear();
        this.stageCache.clear();
        this.cacheTimestamps.clear();
        this.nodeTypeIndex.clear();
        this.workflowNodeIndex.clear();
        this.eventEmitter.removeAllListeners();
    }
}
//# sourceMappingURL=CachedJsonLineStorage.js.map