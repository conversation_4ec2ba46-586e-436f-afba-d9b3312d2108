// src/tools/handlers/SearchToolHandler.ts

import {BaseToolHandler} from './BaseToolHandler.js';
import {formatToolResponse, formatToolError} from '@shared/index.js';

export class SearchToolHandler extends BaseToolHandler {
    async handleTool(name: string, args: Record<string, any>): Promise<any> {
        try {
            this.validateArguments(args);

            switch (name) {
                case "read_graph":
                    const {
                        includeMetadata = true,
                        nodeTypeFilter = [],
                        limit = 0
                    } = args;
                    
                    let graph = await this.knowledgeGraphManager.readGraph();
                    
                    // Apply node type filtering
                    if (nodeTypeFilter.length > 0) {
                        graph = {
                            ...graph,
                            nodes: graph.nodes.filter(node => 
                                nodeTypeFilter.includes(node.nodeType)
                            )
                        };
                    }
                    
                    // Apply limit
                    if (limit > 0) {
                        graph = {
                            ...graph,
                            nodes: graph.nodes.slice(0, limit)
                        };
                    }
                    
                    // Filter metadata if requested
                    if (!includeMetadata) {
                        graph = {
                            ...graph,
                            nodes: graph.nodes.map(node => ({
                                ...node,
                                metadata: []
                            }))
                        };
                    }
                    
                    const actionDetails = [
                        `Read knowledge graph`,
                        nodeTypeFilter.length > 0 ? `filtered by types: ${nodeTypeFilter.join(', ')}` : 'all node types',
                        limit > 0 ? `limited to ${limit} nodes` : 'no limit',
                        includeMetadata ? 'with metadata' : 'without metadata'
                    ].join(', ');
                    
                    return formatToolResponse({
                        data: graph,
                        actionTaken: actionDetails,
                        message: `Found ${graph.nodes.length} nodes and ${graph.edges.length} edges`
                    });

                case "search_nodes":
                    const searchResults = await this.knowledgeGraphManager.searchNodes(args.query);
                    return formatToolResponse({
                        data: searchResults,
                        actionTaken: `Searched nodes with query: ${args.query}`
                    });

                case "open_nodes":
                    const nodes = await this.knowledgeGraphManager.openNodes(args.names);
                    return formatToolResponse({
                        data: nodes,
                        actionTaken: `Retrieved nodes: ${args.names.join(', ')}`
                    });

                default:
                    throw new Error(`Unknown search operation: ${name}`);
            }
        } catch (error) {
            return formatToolError({
                operation: name,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                context: {args},
                suggestions: [
                    "Check node names exist",
                    "Verify search query format"
                ],
                recoverySteps: [
                    "Try with different node names",
                    "Adjust search query parameters"
                ]
            });
        }
    }
}