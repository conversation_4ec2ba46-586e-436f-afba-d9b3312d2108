import type { ApplicationManager } from '../../../application/index.js';
import type { BaseToolHandler } from './BaseToolHandler.js';
export declare class ToolHandlerFactory {
    private static graphHandler;
    private static searchHandler;
    private static metadataHandler;
    private static dynamicHandler;
    private static workflowHandler;
    private static initialized;
    /**
     * Initializes all tool handlers
     */
    static initialize(knowledgeGraphManager: ApplicationManager): Promise<void>;
    /**
     * Gets the appropriate handler for a given tool name
     */
    static getHandler(toolName: string): BaseToolHandler;
    /**
     * Checks if factory is initialized
     */
    static isInitialized(): boolean;
}
