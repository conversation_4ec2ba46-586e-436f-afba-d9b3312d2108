// src/core/workflow/WorkflowTemplates.ts
/**
 * 工作流程節點生成器
 * 根據模板生成標準的工作流程節點
 */
export class WorkflowNodeGenerator {
    /**
     * 根據模板創建工作流程節點
     */
    static createWorkflowNode(template, workflowId, customMetadata) {
        const metadata = [
            `workflow_id: ${workflowId}`,
            `template_id: ${template.id}`,
            `template_version: ${template.version}`,
            `category: ${template.category}`,
            `status: not_started`,
            `current_stage: 0`,
            `total_stages: ${template.stages.length}`,
            `progress: 0`,
            `created_at: ${new Date().toISOString()}`,
            `updated_at: ${new Date().toISOString()}`,
            // 階段信息
            `stages: ${JSON.stringify(template.stages.map(s => ({
                id: s.id,
                name: s.name,
                order: s.order,
                status: s.order === 0 ? 'active' : 'pending'
            })))}`,
            // 🔮 預留自定義metadata
            ...Object.entries(customMetadata || {}).map(([key, value]) => `custom_${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`)
        ];
        return {
            type: 'node',
            name: `workflow_${workflowId}`,
            nodeType: 'workflow',
            metadata
        };
    }
    /**
     * 創建階段節點
     */
    static createStageNodes(template, workflowId) {
        return template.stages.map(stage => ({
            type: 'node',
            name: `stage_${workflowId}_${stage.id}`,
            nodeType: 'stage',
            metadata: [
                `workflow_id: ${workflowId}`,
                `stage_id: ${stage.id}`,
                `stage_name: ${stage.name}`,
                `stage_order: ${stage.order}`,
                `status: ${stage.order === 0 ? 'active' : 'pending'}`,
                `required_node_types: ${JSON.stringify(stage.requiredNodeTypes)}`,
                `optional_node_types: ${JSON.stringify(stage.optionalNodeTypes)}`,
                `completion_criteria: ${JSON.stringify(stage.completionCriteria)}`,
                `estimated_duration: ${stage.estimatedDuration || 'unknown'}`,
                `created_at: ${new Date().toISOString()}`,
                // 🔮 預留階段擴展字段
                ...Object.entries(stage.extensions || {}).map(([key, value]) => `ext_${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`)
            ]
        }));
    }
    /**
     * 創建階段依賴邊
     */
    static createStageDependencyEdges(template, workflowId) {
        const edges = [];
        // 創建順序依賴邊
        for (let i = 0; i < template.stages.length - 1; i++) {
            edges.push({
                from: `stage_${workflowId}_${template.stages[i].id}`,
                to: `stage_${workflowId}_${template.stages[i + 1].id}`,
                edgeType: 'next_stage',
                weight: 1
            });
        }
        // 創建工作流程到階段的歸屬邊
        template.stages.forEach(stage => {
            edges.push({
                from: `workflow_${workflowId}`,
                to: `stage_${workflowId}_${stage.id}`,
                edgeType: 'contains_stage',
                weight: 1
            });
        });
        return edges;
    }
}
/**
 * 預定義的工作流程模板
 */
export class PredefinedTemplates {
    /**
     * 小說創作工作流程模板
     */
    static NOVEL_TEMPLATE = {
        id: 'novel_standard_v1',
        name: '標準小說創作流程',
        description: '適用於長篇小說創作的標準化工作流程',
        category: 'novel',
        version: '1.0.0',
        stages: [
            {
                id: 'planning',
                name: '規劃階段',
                description: '建立小說的基本設定、角色和世界觀',
                order: 0,
                requiredNodeTypes: ['character', 'setting', 'theme'],
                optionalNodeTypes: ['timeline', 'worldbuilding'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['name', 'description', 'background'],
                    minimumQualityScore: 70,
                    qualityChecks: [
                        {
                            type: 'character_depth',
                            description: '角色需要有明確的動機、衝突和成長弧線',
                            validator: 'character_quality_validator',
                            weight: 0.4,
                            required: true
                        },
                        {
                            type: 'setting_richness',
                            description: '設定需要包含感官細節和文化背景',
                            validator: 'setting_quality_validator',
                            weight: 0.3,
                            required: true
                        },
                        {
                            type: 'theme_clarity',
                            description: '主題應該具體且能通過故事元素體現',
                            validator: 'theme_quality_validator',
                            weight: 0.3,
                            required: true
                        }
                    ],
                    contentGuidelines: {
                        character: {
                            description: '創建具有三維特質的角色：外在目標、內在需求、致命缺陷',
                            examples: [
                                '主角：年輕騎士，外在目標是拯救王國，內在需求是證明自己的價值，致命缺陷是過度自信',
                                '反派：墮落法師，外在目標是獲得永生，內在需求是被理解和接納，致命缺陷是無法信任他人'
                            ],
                            qualityTips: [
                                '每個角色都應該有獨特的說話方式和行為模式',
                                '角色的背景故事要與當前情節相關',
                                '確保角色有成長和變化的空間'
                            ],
                            commonMistakes: [
                                '創建完美無缺的角色',
                                '角色動機不明確或不合理',
                                '所有角色說話方式相同'
                            ]
                        },
                        setting: {
                            description: '描述不僅是地點，更是氛圍和情感的載體',
                            examples: [
                                '古老的圖書館：塵埃在陽光中飛舞，羊皮紙的味道混合著蠟燭的香氣，暗示著知識的神秘和時間的流逝',
                                '戰場：泥濘的土地，鐵鏽和血腥的味道，遠處的號角聲，體現戰爭的殘酷和英雄主義的複雜性'
                            ],
                            qualityTips: [
                                '使用五感描述來增強沉浸感',
                                '讓環境反映角色的內心狀態',
                                '考慮設定對情節發展的影響'
                            ],
                            commonMistakes: [
                                '過度描述無關緊要的細節',
                                '設定與故事氛圍不符',
                                '忽略環境對角色行為的影響'
                            ]
                        },
                        theme: {
                            description: '主題應該通過角色行動和情節發展自然呈現',
                            examples: [
                                '成長主題：通過主角面對挑戰、犯錯、學習的過程來體現',
                                '友誼主題：通過角色間的互助、衝突、和解來展現'
                            ],
                            qualityTips: [
                                '避免直接說教，讓主題通過故事自然流露',
                                '確保主題與角色弧線和情節發展一致',
                                '可以有多個相關的次主題'
                            ],
                            commonMistakes: [
                                '主題過於抽象或模糊',
                                '強行插入主題相關的對話',
                                '主題與故事內容脫節'
                            ]
                        }
                    }
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'outline',
                name: '大綱階段',
                description: '制定詳細的故事大綱和情節結構',
                order: 1,
                requiredNodeTypes: ['plotarc', 'timeline'],
                optionalNodeTypes: ['conflict', 'theme'],
                completionCriteria: {
                    minNodes: 1,
                    requiredFields: ['main_plot', 'key_events'],
                    minimumQualityScore: 75,
                    qualityChecks: [
                        {
                            type: 'plot_structure',
                            description: '情節結構需要有清晰的起承轉合',
                            validator: 'plot_structure_validator',
                            weight: 0.5,
                            required: true
                        },
                        {
                            type: 'pacing_balance',
                            description: '節奏安排需要有張弛有度的變化',
                            validator: 'pacing_validator',
                            weight: 0.3,
                            required: true
                        },
                        {
                            type: 'conflict_escalation',
                            description: '衝突需要有合理的升級和解決',
                            validator: 'conflict_validator',
                            weight: 0.2,
                            required: false
                        }
                    ],
                    contentGuidelines: {
                        plotarc: {
                            description: '建立引人入勝的情節弧線，包含起因、發展、高潮、結局',
                            examples: [
                                '三幕結構：建立(25%) → 對抗(50%) → 解決(25%)',
                                '英雄之旅：平凡世界 → 冒險召喚 → 拒絕召喚 → 遇見導師 → 跨越門檻...'
                            ],
                            qualityTips: [
                                '確保每個情節點都推動故事前進',
                                '高潮應該是情感和行動的雙重頂點',
                                '結局要回應開頭提出的問題'
                            ],
                            commonMistakes: [
                                '中段情節拖沓，缺乏推進力',
                                '高潮來得太突然或太平淡',
                                '結局過於匆忙或留下太多未解之謎'
                            ]
                        },
                        timeline: {
                            description: '建立清晰的時間線，確保事件邏輯合理',
                            examples: [
                                '線性時間線：按時間順序發展的故事',
                                '非線性時間線：使用倒敘、插敘等技巧'
                            ],
                            qualityTips: [
                                '重要事件之間要有合理的時間間隔',
                                '考慮季節、天氣對故事氛圍的影響',
                                '確保角色成長與時間推移相符'
                            ],
                            commonMistakes: [
                                '時間跳躍過於頻繁或突兀',
                                '忽略時間對角色和環境的影響',
                                '前後時間設定不一致'
                            ]
                        }
                    }
                },
                estimatedDuration: '1 week'
            },
            {
                id: 'chapter',
                name: '章節階段',
                description: '規劃具體的章節內容和場景',
                order: 2,
                requiredNodeTypes: ['scene', 'chapter'],
                optionalNodeTypes: ['dialogue', 'description'],
                completionCriteria: {
                    minNodes: 5,
                    requiredFields: ['scene_setting', 'characters_involved'],
                    minimumQualityScore: 80,
                    qualityChecks: [
                        {
                            type: 'scene_depth',
                            description: '場景需要有豐富的感官細節和情感層次',
                            validator: 'scene_depth_validator',
                            weight: 0.4,
                            required: true
                        },
                        {
                            type: 'dialogue_authenticity',
                            description: '對話需要符合角色性格且推動情節',
                            validator: 'dialogue_validator',
                            weight: 0.3,
                            required: true
                        },
                        {
                            type: 'pacing_control',
                            description: '章節節奏需要有適當的張弛變化',
                            validator: 'chapter_pacing_validator',
                            weight: 0.3,
                            required: true
                        }
                    ],
                    contentGuidelines: {
                        scene: {
                            description: '創建生動的場景，平衡行動、對話、描述和內心獨白',
                            examples: [
                                '行動場景：劍鬥 - 快節奏，短句，感官細節，情緒張力',
                                '對話場景：談判 - 角色動機衝突，潛台詞，肢體語言',
                                '反思場景：獨處 - 內心掙扎，回憶片段，情感深度'
                            ],
                            qualityTips: [
                                '每個場景都要有明確的目的和衝突',
                                '使用具體的感官細節增強沉浸感',
                                '確保場景推動情節或角色發展',
                                '注意場景間的過渡和連接'
                            ],
                            commonMistakes: [
                                '場景過長或過短，節奏失衡',
                                '缺乏具體的環境描述',
                                '場景目的不明確，流於表面',
                                '忽略角色在場景中的情感變化'
                            ]
                        },
                        chapter: {
                            description: '構建完整的章節，有開頭鉤子、發展和結尾懸念',
                            examples: [
                                '開頭鉤子：突發事件、謎團、衝突、有趣對話',
                                '章節發展：推進主線、深化角色、揭示信息',
                                '結尾懸念：未解問題、新的威脅、情感轉折'
                            ],
                            qualityTips: [
                                '每章都要推進整體故事進程',
                                '保持讀者的閱讀興趣和期待',
                                '平衡不同類型的內容（行動、對話、描述）',
                                '確保章節長度適中且一致'
                            ],
                            commonMistakes: [
                                '章節缺乏內在結構和節奏',
                                '結尾過於平淡，缺乏懸念',
                                '章節間缺乏連貫性',
                                '過度依賴對話或描述'
                            ]
                        }
                    }
                },
                estimatedDuration: '2-3 weeks'
            },
            {
                id: 'generation',
                name: '生成階段',
                description: '基於規劃內容生成完整的小說文本',
                order: 3,
                requiredNodeTypes: ['chapter'],
                optionalNodeTypes: ['revision_notes'],
                completionCriteria: {
                    minNodes: 1,
                    requiredFields: ['content', 'word_count'],
                    minimumQualityScore: 85,
                    qualityChecks: [
                        {
                            type: 'narrative_consistency',
                            description: '敘述風格和視角需要保持一致',
                            validator: 'narrative_consistency_validator',
                            weight: 0.3,
                            required: true
                        },
                        {
                            type: 'character_voice',
                            description: '角色聲音需要獨特且一致',
                            validator: 'character_voice_validator',
                            weight: 0.3,
                            required: true
                        },
                        {
                            type: 'thematic_integration',
                            description: '主題需要自然融入故事中',
                            validator: 'theme_integration_validator',
                            weight: 0.2,
                            required: true
                        },
                        {
                            type: 'prose_quality',
                            description: '文筆需要流暢且富有表現力',
                            validator: 'prose_quality_validator',
                            weight: 0.2,
                            required: false
                        }
                    ],
                    contentGuidelines: {
                        chapter: {
                            description: '完成高質量的章節內容，整合所有故事元素',
                            examples: [
                                '完整章節：開頭引人入勝，中段發展充實，結尾留有懸念',
                                '修訂版本：基於反饋改進的內容，提升可讀性和影響力'
                            ],
                            qualityTips: [
                                '確保每章都有明確的情節進展',
                                '保持敘述聲音的一致性',
                                '平衡描述、對話、行動和內心獨白',
                                '注意章節間的過渡和連貫性',
                                '最終校對語法、拼寫和格式'
                            ],
                            commonMistakes: [
                                '急於完成而忽略質量',
                                '前後章節風格不一致',
                                '忽略細節的打磨和完善',
                                '缺乏最終的整體審視和修訂'
                            ]
                        }
                    }
                },
                estimatedDuration: '4-6 weeks'
            }
        ]
    };
    /**
     * 文章創作工作流程模板
     */
    static ARTICLE_TEMPLATE = {
        id: 'article_standard_v1',
        name: '標準文章創作流程',
        description: '適用於專業文章和博客文章的創作流程',
        category: 'article',
        version: '1.0.0',
        stages: [
            {
                id: 'research',
                name: '研究階段',
                description: '收集資料和進行主題研究',
                order: 0,
                requiredNodeTypes: ['topic', 'source', 'research_note'],
                optionalNodeTypes: ['reference'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['topic', 'key_points']
                },
                estimatedDuration: '2-3 days'
            },
            {
                id: 'outline',
                name: '大綱階段',
                description: '制定文章結構和要點',
                order: 1,
                requiredNodeTypes: ['outline', 'section'],
                optionalNodeTypes: ['argument'],
                completionCriteria: {
                    minNodes: 1,
                    requiredFields: ['structure', 'main_points']
                },
                estimatedDuration: '1 day'
            },
            {
                id: 'draft',
                name: '草稿階段',
                description: '撰寫初稿內容',
                order: 2,
                requiredNodeTypes: ['draft'],
                optionalNodeTypes: ['paragraph', 'citation'],
                completionCriteria: {
                    minNodes: 1,
                    requiredFields: ['content', 'word_count']
                },
                estimatedDuration: '2-3 days'
            },
            {
                id: 'review',
                name: '審查階段',
                description: '校對和完善文章內容',
                order: 3,
                requiredNodeTypes: ['final_article'],
                optionalNodeTypes: ['revision_note', 'feedback'],
                completionCriteria: {
                    minNodes: 1,
                    requiredFields: ['final_content', 'review_status']
                },
                estimatedDuration: '1-2 days'
            }
        ]
    };
    /**
     * 劇本創作工作流程模板
     */
    static SCRIPT_TEMPLATE = {
        id: 'script_standard_v1',
        name: '標準劇本創作流程',
        description: '適用於電影、電視劇和舞台劇本的創作流程',
        category: 'script',
        version: '1.0.0',
        stages: [
            {
                id: 'concept',
                name: '概念階段',
                description: '建立劇本的核心概念、主題和基本設定',
                order: 0,
                requiredNodeTypes: ['concept', 'theme', 'genre'],
                optionalNodeTypes: ['inspiration', 'reference'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['core_concept', 'target_audience', 'genre']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'character_development',
                name: '角色發展階段',
                description: '創建和發展主要角色，建立角色關係',
                order: 1,
                requiredNodeTypes: ['character', 'relationship'],
                optionalNodeTypes: ['character_arc', 'backstory'],
                completionCriteria: {
                    minNodes: 5,
                    requiredFields: ['character_motivation', 'character_conflict', 'dialogue_style']
                },
                estimatedDuration: '2-3 weeks'
            },
            {
                id: 'structure_outline',
                name: '結構大綱階段',
                description: '建立三幕結構和主要情節點',
                order: 2,
                requiredNodeTypes: ['act', 'scene_outline', 'plot_point'],
                optionalNodeTypes: ['subplot', 'turning_point'],
                completionCriteria: {
                    minNodes: 8,
                    requiredFields: ['act_structure', 'scene_breakdown', 'pacing']
                },
                estimatedDuration: '2-3 weeks'
            },
            {
                id: 'scene_writing',
                name: '場景撰寫階段',
                description: '撰寫具體的場景和對話',
                order: 3,
                requiredNodeTypes: ['scene', 'dialogue'],
                optionalNodeTypes: ['action_line', 'stage_direction'],
                completionCriteria: {
                    minNodes: 15,
                    requiredFields: ['scene_content', 'character_dialogue', 'visual_elements']
                },
                estimatedDuration: '4-6 weeks'
            },
            {
                id: 'revision_polish',
                name: '修訂潤色階段',
                description: '修訂劇本結構、對話和格式',
                order: 4,
                requiredNodeTypes: ['revision'],
                optionalNodeTypes: ['feedback', 'format_check'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['final_script', 'format_compliance', 'readthrough_notes']
                },
                estimatedDuration: '2-3 weeks'
            }
        ]
    };
    /**
     * 學術論文工作流程模板
     */
    static ACADEMIC_TEMPLATE = {
        id: 'academic_standard_v1',
        name: '標準學術論文流程',
        description: '適用於學術研究論文和期刊文章的創作流程',
        category: 'academic',
        version: '1.0.0',
        stages: [
            {
                id: 'literature_review',
                name: '文獻回顧階段',
                description: '收集和分析相關學術文獻',
                order: 0,
                requiredNodeTypes: ['literature', 'citation', 'research_gap'],
                optionalNodeTypes: ['database_search', 'keyword'],
                completionCriteria: {
                    minNodes: 10,
                    requiredFields: ['literature_summary', 'research_gap', 'theoretical_framework']
                },
                estimatedDuration: '3-4 weeks'
            },
            {
                id: 'methodology',
                name: '研究方法階段',
                description: '設計研究方法和實驗設計',
                order: 1,
                requiredNodeTypes: ['methodology', 'research_design', 'data_collection'],
                optionalNodeTypes: ['ethics_approval', 'pilot_study'],
                completionCriteria: {
                    minNodes: 5,
                    requiredFields: ['research_method', 'data_analysis_plan', 'validity_measures']
                },
                estimatedDuration: '2-3 weeks'
            },
            {
                id: 'data_analysis',
                name: '數據分析階段',
                description: '收集和分析研究數據',
                order: 2,
                requiredNodeTypes: ['data', 'analysis', 'results'],
                optionalNodeTypes: ['statistical_test', 'visualization'],
                completionCriteria: {
                    minNodes: 8,
                    requiredFields: ['raw_data', 'analysis_results', 'statistical_significance']
                },
                estimatedDuration: '4-6 weeks'
            },
            {
                id: 'writing_drafting',
                name: '論文撰寫階段',
                description: '撰寫論文各個章節',
                order: 3,
                requiredNodeTypes: ['abstract', 'introduction', 'discussion', 'conclusion'],
                optionalNodeTypes: ['appendix', 'acknowledgments'],
                completionCriteria: {
                    minNodes: 6,
                    requiredFields: ['complete_draft', 'proper_citations', 'academic_style']
                },
                estimatedDuration: '3-4 weeks'
            },
            {
                id: 'peer_review',
                name: '同行評議階段',
                description: '獲取反饋並修訂論文',
                order: 4,
                requiredNodeTypes: ['peer_feedback', 'revision'],
                optionalNodeTypes: ['supervisor_review', 'language_check'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['final_paper', 'response_to_reviewers', 'submission_ready']
                },
                estimatedDuration: '2-3 weeks'
            }
        ]
    };
    /**
     * 技術文檔工作流程模板
     */
    static TECHNICAL_TEMPLATE = {
        id: 'technical_standard_v1',
        name: '標準技術文檔流程',
        description: '適用於API文檔、用戶手冊和技術規範的創作流程',
        category: 'technical',
        version: '1.0.0',
        stages: [
            {
                id: 'requirements_analysis',
                name: '需求分析階段',
                description: '分析文檔需求和目標受眾',
                order: 0,
                requiredNodeTypes: ['requirement', 'audience', 'scope'],
                optionalNodeTypes: ['stakeholder', 'use_case'],
                completionCriteria: {
                    minNodes: 4,
                    requiredFields: ['target_audience', 'document_scope', 'success_criteria']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'information_architecture',
                name: '信息架構階段',
                description: '設計文檔結構和信息組織',
                order: 1,
                requiredNodeTypes: ['structure', 'navigation', 'taxonomy'],
                optionalNodeTypes: ['wireframe', 'user_journey'],
                completionCriteria: {
                    minNodes: 5,
                    requiredFields: ['document_structure', 'navigation_design', 'content_hierarchy']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'content_creation',
                name: '內容創建階段',
                description: '撰寫技術內容和代碼示例',
                order: 2,
                requiredNodeTypes: ['content', 'code_example', 'diagram'],
                optionalNodeTypes: ['screenshot', 'video', 'interactive_demo'],
                completionCriteria: {
                    minNodes: 10,
                    requiredFields: ['technical_content', 'code_samples', 'visual_aids']
                },
                estimatedDuration: '3-4 weeks'
            },
            {
                id: 'review_testing',
                name: '審查測試階段',
                description: '技術審查和用戶測試',
                order: 3,
                requiredNodeTypes: ['technical_review', 'user_testing'],
                optionalNodeTypes: ['accessibility_check', 'seo_optimization'],
                completionCriteria: {
                    minNodes: 4,
                    requiredFields: ['review_feedback', 'test_results', 'accuracy_verification']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'publication_maintenance',
                name: '發布維護階段',
                description: '發布文檔並建立維護流程',
                order: 4,
                requiredNodeTypes: ['publication', 'maintenance_plan'],
                optionalNodeTypes: ['analytics', 'feedback_system'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['published_document', 'update_schedule', 'feedback_mechanism']
                },
                estimatedDuration: '1 week'
            }
        ]
    };
    /**
     * 創意寫作工作流程模板
     */
    static CREATIVE_TEMPLATE = {
        id: 'creative_standard_v1',
        name: '標準創意寫作流程',
        description: '適用於詩歌、散文、短篇小說等創意寫作的流程',
        category: 'creative',
        version: '1.0.0',
        stages: [
            {
                id: 'inspiration_gathering',
                name: '靈感收集階段',
                description: '收集創作靈感和素材',
                order: 0,
                requiredNodeTypes: ['inspiration', 'mood', 'image'],
                optionalNodeTypes: ['music', 'memory', 'observation'],
                completionCriteria: {
                    minNodes: 5,
                    requiredFields: ['core_inspiration', 'emotional_tone', 'sensory_details']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'experimentation',
                name: '實驗階段',
                description: '嘗試不同的寫作風格和技巧',
                order: 1,
                requiredNodeTypes: ['experiment', 'style_trial', 'voice'],
                optionalNodeTypes: ['form_exploration', 'technique'],
                completionCriteria: {
                    minNodes: 6,
                    requiredFields: ['writing_voice', 'chosen_style', 'experimental_pieces']
                },
                estimatedDuration: '2-3 weeks'
            },
            {
                id: 'drafting',
                name: '草稿階段',
                description: '創作初稿和多個版本',
                order: 2,
                requiredNodeTypes: ['draft', 'version'],
                optionalNodeTypes: ['fragment', 'alternative_ending'],
                completionCriteria: {
                    minNodes: 4,
                    requiredFields: ['complete_draft', 'alternative_versions', 'self_reflection']
                },
                estimatedDuration: '2-4 weeks'
            },
            {
                id: 'refinement',
                name: '精煉階段',
                description: '修訂和完善作品',
                order: 3,
                requiredNodeTypes: ['revision', 'polish'],
                optionalNodeTypes: ['peer_feedback', 'reading_aloud'],
                completionCriteria: {
                    minNodes: 3,
                    requiredFields: ['refined_work', 'artistic_vision', 'emotional_impact']
                },
                estimatedDuration: '1-2 weeks'
            },
            {
                id: 'sharing_reflection',
                name: '分享反思階段',
                description: '分享作品並反思創作過程',
                order: 4,
                requiredNodeTypes: ['final_work', 'reflection'],
                optionalNodeTypes: ['audience_feedback', 'publication'],
                completionCriteria: {
                    minNodes: 2,
                    requiredFields: ['completed_work', 'creative_reflection', 'next_steps']
                },
                estimatedDuration: '1 week'
            }
        ]
    };
    /**
     * 獲取所有預定義模板
     */
    static getAllTemplates() {
        return [
            this.NOVEL_TEMPLATE,
            this.ARTICLE_TEMPLATE,
            this.SCRIPT_TEMPLATE,
            this.ACADEMIC_TEMPLATE,
            this.TECHNICAL_TEMPLATE,
            this.CREATIVE_TEMPLATE
        ];
    }
    /**
     * 根據類別獲取模板
     */
    static getTemplatesByCategory(category) {
        return this.getAllTemplates().filter(template => template.category === category);
    }
    /**
     * 根據ID獲取特定模板
     */
    static getTemplateById(id) {
        return this.getAllTemplates().find(template => template.id === id) || null;
    }
    /**
     * 解析時長字符串為週數
     */
    static parseDuration(duration) {
        if (!duration)
            return 1;
        const match = duration.match(/(\d+)(?:-(\d+))?\s*(day|week|month)s?/);
        if (!match)
            return 1;
        const min = parseInt(match[1]);
        const max = match[2] ? parseInt(match[2]) : min;
        const avg = (min + max) / 2;
        const unit = match[3];
        switch (unit) {
            case 'day': return avg / 7;
            case 'week': return avg;
            case 'month': return avg * 4;
            default: return avg;
        }
    }
    /**
     * 獲取適用受眾
     */
    static getSuitableAudience(category) {
        const audiences = {
            'novel': ['小說作家', '創意寫作者', '文學愛好者'],
            'article': ['博客作者', '記者', '內容創作者', '營銷人員'],
            'script': ['編劇', '導演', '戲劇創作者', '影視工作者'],
            'academic': ['研究人員', '學者', '研究生', '博士生'],
            'technical': ['技術寫作者', '產品經理', '開發者', 'UX設計師'],
            'creative': ['詩人', '散文家', '創意寫作者', '藝術家']
        };
        return audiences[category] || ['創作者'];
    }
    /**
     * 獲取模板預覽信息
     */
    static getTemplatePreview(id) {
        const template = this.getTemplateById(id);
        if (!template)
            return null;
        const stageCount = template.stages.length;
        const complexity = stageCount <= 3 ? 'Simple' : stageCount <= 5 ? 'Moderate' : 'Complex';
        const totalWeeks = template.stages.reduce((total, stage) => {
            const duration = stage.estimatedDuration;
            const weeks = this.parseDuration(duration);
            return total + weeks;
        }, 0);
        const estimatedDuration = totalWeeks <= 4 ? `${totalWeeks} weeks` :
            totalWeeks <= 12 ? `${Math.ceil(totalWeeks / 4)} months` :
                `${Math.ceil(totalWeeks / 12)} quarters`;
        const suitableFor = this.getSuitableAudience(template.category);
        return {
            id: template.id,
            name: template.name,
            description: template.description,
            category: template.category,
            stageCount,
            estimatedDuration,
            complexity,
            suitableFor
        };
    }
    // 🔮 預留自定義模板註冊接口
    static customTemplates = new Map();
    /**
     * 註冊自定義模板
     */
    static registerCustomTemplate(template) {
        this.customTemplates.set(template.id, template);
    }
    /**
     * 獲取自定義模板
     */
    static getCustomTemplate(id) {
        return this.customTemplates.get(id) || null;
    }
    /**
     * 獲取所有模板（包括自定義）
     */
    static getAllTemplatesIncludingCustom() {
        return [
            ...this.getAllTemplates(),
            ...Array.from(this.customTemplates.values())
        ];
    }
    /**
     * 模板版本控制和升級機制
     */
    static getTemplateVersions() {
        return {
            'novel_standard': ['v1.0.0'],
            'article_standard': ['v1.0.0'],
            'script_standard': ['v1.0.0'],
            'academic_standard': ['v1.0.0'],
            'technical_standard': ['v1.0.0'],
            'creative_standard': ['v1.0.0']
        };
    }
    /**
     * 檢查模板是否需要升級
     */
    static checkForUpgrade(templateId, currentVersion) {
        const template = this.getTemplateById(templateId);
        if (!template) {
            return { needsUpgrade: false, latestVersion: currentVersion };
        }
        const latestVersion = template.version;
        const needsUpgrade = this.compareVersions(currentVersion, latestVersion) < 0;
        return {
            needsUpgrade,
            latestVersion,
            upgradeNotes: needsUpgrade ? this.getUpgradeNotes(templateId, currentVersion, latestVersion) : undefined
        };
    }
    /**
     * 比較版本號
     */
    static compareVersions(version1, version2) {
        const v1Parts = version1.split('.').map(Number);
        const v2Parts = version2.split('.').map(Number);
        for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
            const v1Part = v1Parts[i] || 0;
            const v2Part = v2Parts[i] || 0;
            if (v1Part < v2Part)
                return -1;
            if (v1Part > v2Part)
                return 1;
        }
        return 0;
    }
    /**
     * 獲取升級說明
     */
    static getUpgradeNotes(templateId, fromVersion, toVersion) {
        return `模板 ${templateId} 從版本 ${fromVersion} 升級到 ${toVersion}。建議檢查新的階段要求和完成標準。`;
    }
}
//# sourceMappingURL=WorkflowTemplates.js.map