// src/core/graph/GraphValidator.ts
import { EdgeWeightUtils } from './EdgeWeightUtils.js';
import { EntityNormalizer } from './EntityNormalizer.js';
import { SmartEntityNormalizer } from './SmartEntityNormalizer.js';
/**
 * Provides validation methods for graph operations
 */
export class GraphValidator {
    /**
     * 將節點類型映射到檢測上下文
     */
    static mapNodeTypeToContext(nodeType) {
        const characterTypes = ['character', 'person', 'protagonist', 'antagonist'];
        const settingTypes = ['setting', 'location', 'place', 'world'];
        if (characterTypes.includes(nodeType.toLowerCase())) {
            return 'character';
        }
        else if (settingTypes.includes(nodeType.toLowerCase())) {
            return 'setting';
        }
        else {
            return 'general';
        }
    }
    /**
     * Validates that a node with the given name exists in the graph.
     */
    static validateNodeExists(graph, nodeName) {
        if (!graph.nodes.some(node => node.name === nodeName)) {
            throw new Error(`Node not found: ${nodeName}`);
        }
    }
    /**
     * Validates that a node with the given name does not exist in the graph.
     * Enhanced with intelligent duplicate detection for Chinese text, punctuation, and spacing.
     */
    static validateNodeDoesNotExist(graph, nodeName) {
        // 原有的精確匹配檢查
        if (graph.nodes.some(node => node.name === nodeName)) {
            throw new Error(`Node already exists: ${nodeName}. Consider updating existing node.`);
        }
        // 新增智能重複檢測 - 多算法融合（改進版）
        const existingNames = graph.nodes.map(node => node.name);
        // 根據現有節點類型確定檢測參數（如果有的話）
        const existingNodeTypes = graph.nodes.map(n => n.nodeType).filter(Boolean);
        const mostCommonType = existingNodeTypes.length > 0 ?
            existingNodeTypes.reduce((a, b, i, arr) => arr.filter(v => v === a).length >= arr.filter(v => v === b).length ? a : b) : 'general';
        const contextType = this.mapNodeTypeToContext(mostCommonType);
        const smartCheck = SmartEntityNormalizer.isSmartDuplicate(existingNames, nodeName, {
            threshold: 0.75, // 提高基礎閾值減少誤報
            strictMode: false, // 非嚴格模式，允許合理的相似性
            contextType: contextType
        });
        if (smartCheck.isDuplicate) {
            const analysis = smartCheck.analysis;
            const confidence = smartCheck.confidence || 0;
            const recommendation = smartCheck.recommendation || '';
            throw new Error(`智能重複檢測: "${nodeName}" 與 "${smartCheck.matchedName}" 相似度為 ${(smartCheck.similarity * 100).toFixed(1)}% ` +
                `(置信度: ${(confidence * 100).toFixed(1)}%)。\n` +
                `詳細分析: Unicode=${(analysis.unicode * 100).toFixed(0)}%, 語音=${(analysis.phonetic * 100).toFixed(0)}%, ` +
                `結構=${(analysis.structural * 100).toFixed(0)}%, 語義=${(analysis.semantic * 100).toFixed(0)}%。\n` +
                `建議: ${recommendation}`);
        }
    }
    /**
     * Validates that an edge is unique in the graph.
     */
    static validateEdgeUniqueness(graph, edge) {
        if (graph.edges.some(existing => existing.from === edge.from &&
            existing.to === edge.to &&
            existing.edgeType === edge.edgeType)) {
            throw new Error(`Edge already exists: ${edge.from} -> ${edge.to} (${edge.edgeType})`);
        }
    }
    /**
     * Validates that a node has required properties.
     */
    static validateNodeProperties(node) {
        if (!node.name) {
            throw new Error("Node must have a 'name' property");
        }
        if (!node.nodeType) {
            throw new Error("Node must have a 'nodeType' property");
        }
        if (!Array.isArray(node.metadata)) {
            throw new Error("Node must have a 'metadata' array");
        }
    }
    /**
     * Validates that a partial node update has a name property.
     */
    static validateNodeNameProperty(node) {
        if (!node.name) {
            throw new Error("Node must have a 'name' property for updating");
        }
    }
    /**
     * Validates that the provided value is a valid array of node names.
     */
    static validateNodeNamesArray(nodeNames) {
        if (!Array.isArray(nodeNames)) {
            throw new Error("nodeNames must be an array");
        }
        if (nodeNames.some(name => typeof name !== 'string')) {
            throw new Error("All node names must be strings");
        }
    }
    /**
     * Validates edge properties.
     */
    static validateEdgeProperties(edge) {
        if (!edge.from) {
            throw new Error("Edge must have a 'from' property");
        }
        if (!edge.to) {
            throw new Error("Edge must have a 'to' property");
        }
        if (!edge.edgeType) {
            throw new Error("Edge must have an 'edgeType' property");
        }
        if (edge.weight !== undefined) {
            EdgeWeightUtils.validateWeight(edge.weight);
        }
    }
    /**
     * Validates that all referenced nodes in edges exist.
     */
    static validateEdgeReferences(graph, edges) {
        for (const edge of edges) {
            this.validateNodeExists(graph, edge.from);
            this.validateNodeExists(graph, edge.to);
        }
    }
    /**
     * Validates the entire graph structure.
     */
    static validateGraphStructure(graph) {
        if (!Array.isArray(graph.nodes)) {
            throw new Error("Graph must have a 'nodes' array");
        }
        if (!Array.isArray(graph.edges)) {
            throw new Error("Graph must have an 'edges' array");
        }
        // Validate all nodes
        graph.nodes.forEach(node => this.validateNodeProperties(node));
        // Validate all edges
        graph.edges.forEach(edge => {
            this.validateEdgeProperties(edge);
            this.validateNodeExists(graph, edge.from);
            this.validateNodeExists(graph, edge.to);
        });
    }
    /**
     * 檢測並報告圖中的重複實體
     */
    static detectDuplicateEntities(graph, threshold = 0.9) {
        const nodeNames = graph.nodes.map(node => node.name);
        const duplicateGroups = EntityNormalizer.findDuplicateGroups(nodeNames, threshold);
        const suggestedMerges = duplicateGroups.map(group => ({
            duplicates: group,
            suggestedName: EntityNormalizer.selectBestEntityName(group),
            similarityScores: group.slice(1).map(name => EntityNormalizer.calculateSimilarity(group[0], name))
        }));
        return {
            duplicateGroups,
            suggestedMerges
        };
    }
}
// Export convenience functions
export const { validateNodeExists, validateNodeDoesNotExist, validateEdgeUniqueness, validateNodeProperties, validateNodeNameProperty, validateNodeNamesArray, validateEdgeProperties, validateEdgeReferences, validateGraphStructure } = GraphValidator;
//# sourceMappingURL=GraphValidator.js.map