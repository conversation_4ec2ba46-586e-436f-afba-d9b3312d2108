// tests/mcp/ListWorkflowsTest.js
// 列出現有工作流程

console.log('📋 列出現有工作流程...');

async function listExistingWorkflows() {
    try {
        const handlerModule = await import('../../dist/integration/tools/handlers/WorkflowToolHandler.js');
        const storageModule = await import('../../dist/infrastructure/storage/CachedJsonLineStorage.js');
        const appManagerModule = await import('../../dist/application/managers/ApplicationManager.js');
        
        const storage = new storageModule.CachedJsonLineStorage();
        const appManager = new appManagerModule.ApplicationManager(storage);
        const handler = new handlerModule.WorkflowToolHandler(appManager, storage);

        console.log('✅ 模塊加載成功');

        // 列出所有工作流程
        const listResult = await handler.handleTool('workflow_list', {});
        console.log('📄 工作流程列表結果:', JSON.stringify(listResult, null, 2));

        // 清理
        if (storage && typeof storage.cleanup === 'function') {
            storage.cleanup();
        }

        return listResult;

    } catch (error) {
        console.error('❌ 錯誤:', error);
        return null;
    }
}

listExistingWorkflows();
