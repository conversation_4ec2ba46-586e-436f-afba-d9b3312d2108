// tests/integration/FeatureCompleteness.test.ts

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { WorkflowToolHandler } from '../../src/integration/tools/handlers/WorkflowToolHandler.js';
import { CachedJsonLineStorage } from '../../src/infrastructure/storage/CachedJsonLineStorage.js';
import { ApplicationManager } from '../../src/application/managers/ApplicationManager.js';
import { getAllWorkflowToolsIncludingCustom } from '../../src/integration/tools/registry/workflowTools.js';
import { PredefinedTemplates } from '../../src/core/workflow/WorkflowTemplates.js';

describe('Feature Completeness Validation', () => {
    let workflowHandler: WorkflowToolHandler;
    let storage: CachedJsonLineStorage;
    let appManager: ApplicationManager;

    beforeEach(() => {
        storage = new CachedJsonLineStorage();
        appManager = new ApplicationManager(storage);
        workflowHandler = new WorkflowToolHandler(appManager, storage);
    });

    afterEach(() => {
        storage.cleanup();
    });

    describe('承諾功能實現檢查', () => {
        it('應該實現所有承諾的工作流程工具', () => {
            const expectedTools = [
                'workflow_create',
                'workflow_status', 
                'workflow_advance',
                'workflow_list',
                'stage_validate',
                'stage_complete',
                'workflow_templates',
                'workflow_pause',
                'workflow_resume',
                'workflow_delete'
            ];

            const availableTools = getAllWorkflowToolsIncludingCustom();
            const toolNames = availableTools.map(tool => tool.name);

            expectedTools.forEach(expectedTool => {
                expect(toolNames).toContain(expectedTool);
            });
        });

        it('應該支持所有承諾的工作流程類型', () => {
            const expectedTypes = ['novel', 'article', 'script', 'custom'];
            const availableTemplates = PredefinedTemplates.getAllTemplates();
            const templateCategories = [...new Set(availableTemplates.map(t => t.category))];

            expectedTypes.slice(0, -1).forEach(type => { // 除了custom
                expect(templateCategories).toContain(type);
            });

            // 驗證custom類型支持
            expect(typeof PredefinedTemplates.registerCustomTemplate).toBe('function');
        });

        it('應該實現完整的工作流程生命週期管理', async () => {
            // 1. 創建工作流程
            const createResult = await workflowHandler.handleTool('workflow_create', {
                name: 'Completeness Test',
                type: 'novel'
            });
            expect(createResult.toolResult.isError).toBe(false);
            const workflowId = createResult.toolResult.data.workflowId;

            // 2. 查看狀態
            const statusResult = await workflowHandler.handleTool('workflow_status', {
                workflowId
            });
            expect(statusResult.toolResult.isError).toBe(false);

            // 3. 驗證階段
            const validateResult = await workflowHandler.handleTool('stage_validate', {
                workflowId
            });
            expect(validateResult.toolResult.isError).toBe(false);

            // 4. 推進階段
            const advanceResult = await workflowHandler.handleTool('workflow_advance', {
                workflowId,
                force: true
            });
            expect(advanceResult.toolResult.isError).toBe(false);

            // 5. 暫停工作流程
            const pauseResult = await workflowHandler.handleTool('workflow_pause', {
                workflowId,
                reason: 'Testing pause functionality'
            });
            expect(pauseResult.toolResult.isError).toBe(false);

            // 6. 恢復工作流程
            const resumeResult = await workflowHandler.handleTool('workflow_resume', {
                workflowId,
                notes: 'Testing resume functionality'
            });
            expect(resumeResult.toolResult.isError).toBe(false);
        });

        it('應該實現所有承諾的存儲功能', async () => {
            // 測試增量更新
            expect(typeof storage.updateNodesIncremental).toBe('function');
            
            // 測試批量操作
            const batchResult = await storage.batchOperation([]);
            expect(batchResult.success).toBe(true);

            // 測試緩存配置
            await storage.configureCaching({ maxSize: 100, ttl: 1000, enablePersistence: true });
            
            // 測試索引配置
            await storage.configureIndexes({ 
                nodeNameIndex: true, 
                nodeTypeIndex: true, 
                workflowIndex: true, 
                customIndexes: [] 
            });

            // 測試查詢功能
            const nodesByType = await storage.queryNodesByType('test');
            expect(Array.isArray(nodesByType)).toBe(true);
        });
    });

    describe('MemoryViewer功能檢查', () => {
        it('應該包含工作流程視圖標籤頁', async () => {
            // 讀取MemoryViewer.html文件內容
            const fs = await import('fs');
            const path = await import('path');
            
            const viewerPath = path.join(process.cwd(), 'MemoryViewer.html');
            const content = fs.readFileSync(viewerPath, 'utf-8');

            // 檢查是否包含工作流程視圖相關元素
            expect(content).toContain('workflow');
            expect(content).toContain('Workflow View');
            expect(content).toContain('workflowView');
            expect(content).toContain('workflow-container');
        });

        it('應該包含工作流程相關的CSS樣式', async () => {
            const fs = await import('fs');
            const path = await import('path');
            
            const viewerPath = path.join(process.cwd(), 'MemoryViewer.html');
            const content = fs.readFileSync(viewerPath, 'utf-8');

            // 檢查關鍵CSS類
            const expectedClasses = [
                'workflow-container',
                'workflow-header',
                'workflow-item',
                'workflow-status',
                'workflow-progress',
                'stage-list',
                'stage-item'
            ];

            expectedClasses.forEach(className => {
                expect(content).toContain(className);
            });
        });

        it('應該包含工作流程相關的JavaScript功能', async () => {
            const fs = await import('fs');
            const path = await import('path');
            
            const viewerPath = path.join(process.cwd(), 'MemoryViewer.html');
            const content = fs.readFileSync(viewerPath, 'utf-8');

            // 檢查關鍵JavaScript函數
            const expectedFunctions = [
                'updateWorkflowView',
                'renderWorkflowList',
                'selectWorkflow',
                'filterWorkflows',
                'refreshWorkflows'
            ];

            expectedFunctions.forEach(functionName => {
                expect(content).toContain(functionName);
            });
        });
    });

    describe('擴展接口實現檢查', () => {
        it('應該實現所有預留的存儲擴展接口', () => {
            // 檢查插件註冊
            expect(typeof storage.registerStoragePlugin).toBe('function');
            
            // 檢查自定義查詢
            expect(typeof storage.executeCustomQuery).toBe('function');
            
            // 檢查數據遷移
            expect(typeof storage.migrateData).toBe('function');
            
            // 檢查事件監聽
            expect(typeof storage.onStorageEvent).toBe('function');
        });

        it('應該支持工具插件註冊', () => {
            const { registerCustomWorkflowTool } = require('../../src/integration/tools/registry/workflowTools.js');
            expect(typeof registerCustomWorkflowTool).toBe('function');
        });

        it('應該支持自定義模板註冊', () => {
            expect(typeof PredefinedTemplates.registerCustomTemplate).toBe('function');
            expect(typeof PredefinedTemplates.getCustomTemplate).toBe('function');
        });
    });

    describe('錯誤處理完整性', () => {
        it('應該正確處理不存在的工作流程', async () => {
            const result = await workflowHandler.handleTool('workflow_status', {
                workflowId: 'non-existent-workflow'
            });
            
            expect(result.toolResult.isError).toBe(true);
            expect(result.toolResult.error).toContain('not found');
        });

        it('應該正確處理無效的工具參數', async () => {
            const result = await workflowHandler.handleTool('workflow_create', {
                // 缺少必需的name參數
                type: 'novel'
            });
            
            expect(result.toolResult.isError).toBe(true);
        });

        it('應該正確處理併發操作衝突', async () => {
            // 創建工作流程
            const createResult = await workflowHandler.handleTool('workflow_create', {
                name: 'Concurrency Test',
                type: 'novel'
            });
            const workflowId = createResult.toolResult.data.workflowId;

            // 嘗試併發推進階段
            const promises = Array(5).fill(null).map(() =>
                workflowHandler.handleTool('workflow_advance', {
                    workflowId,
                    force: true
                }).catch(error => ({ error: error.message }))
            );

            const results = await Promise.all(promises);
            
            // 至少應該有一些操作成功，其他的應該有適當的錯誤處理
            const successful = results.filter(r => !r.error).length;
            expect(successful).toBeGreaterThan(0);
        });
    });

    describe('性能要求驗證', () => {
        it('應該在合理時間內完成工作流程創建', async () => {
            const startTime = Date.now();
            
            await workflowHandler.handleTool('workflow_create', {
                name: 'Performance Test',
                type: 'novel'
            });
            
            const duration = Date.now() - startTime;
            expect(duration).toBeLessThan(1000); // 應該在1秒內完成
        });

        it('應該能夠處理合理數量的工作流程列表', async () => {
            // 創建多個工作流程
            for (let i = 0; i < 10; i++) {
                await workflowHandler.handleTool('workflow_create', {
                    name: `List Test ${i}`,
                    type: 'novel'
                });
            }

            const startTime = Date.now();
            const result = await workflowHandler.handleTool('workflow_list', {});
            const duration = Date.now() - startTime;

            expect(result.toolResult.isError).toBe(false);
            expect(result.toolResult.data.workflows.length).toBeGreaterThanOrEqual(10);
            expect(duration).toBeLessThan(2000); // 應該在2秒內完成
        });
    });

    describe('文檔一致性檢查', () => {
        it('應該有對應的擴展接口文檔', async () => {
            const fs = await import('fs');
            const path = await import('path');
            
            const docPath = path.join(process.cwd(), 'docs', 'EXTENSION_INTERFACES.md');
            expect(fs.existsSync(docPath)).toBe(true);
            
            const content = fs.readFileSync(docPath, 'utf-8');
            
            // 檢查文檔是否包含關鍵章節
            expect(content).toContain('存儲層擴展接口');
            expect(content).toContain('工具系統擴展接口');
            expect(content).toContain('工作流程模板擴展');
            expect(content).toContain('UI擴展接口');
        });

        it('應該有完整的測試配置', async () => {
            const fs = await import('fs');
            const path = await import('path');
            
            // 檢查Jest配置
            const jestConfigPath = path.join(process.cwd(), 'jest.config.js');
            expect(fs.existsSync(jestConfigPath)).toBe(true);
            
            // 檢查測試設置文件
            const setupPath = path.join(process.cwd(), 'tests', 'setup.ts');
            expect(fs.existsSync(setupPath)).toBe(true);
        });
    });
});
