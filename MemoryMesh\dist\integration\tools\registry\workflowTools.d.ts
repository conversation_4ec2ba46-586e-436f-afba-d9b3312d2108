import type { Tool } from '../../../shared/index.js';
/**
 * 工作流程相關的MCP工具定義
 * 遵循開放封閉原則，支持未來功能擴展
 */
export declare const workflowTools: Tool[];
/**
 * 工作流程工具類別標識
 */
export declare const WORKFLOW_TOOL_CATEGORY = "workflow";
/**
 * 獲取所有工作流程工具
 */
export declare function getAllWorkflowTools(): Tool[];
/**
 * 根據名稱獲取工作流程工具
 */
export declare function getWorkflowTool(name: string): Tool | undefined;
/**
 * 檢查是否為工作流程工具
 */
export declare function isWorkflowTool(toolName: string): boolean;
export declare function registerCustomWorkflowTool(tool: Tool): void;
export declare function getCustomWorkflowTools(): Tool[];
export declare function getAllWorkflowToolsIncludingCustom(): Tool[];
