// src/tools/handlers/ToolHandlerFactory.ts

import {GraphToolHandler} from './GraphToolHandler.js';
import {SearchToolHandler} from './SearchToolHandler.js';
import {MetadataToolHandler} from './MetadataToolHandler.js';
import {DynamicToolHandler} from './DynamicToolHandler.js';
import {toolsRegistry} from '@integration/index.js';
import type {ApplicationManager} from '@application/index.js';
import type {BaseToolHandler} from './BaseToolHandler.js';

// 延遲導入以避免循環依賴
let WorkflowToolHandler: any;
let CachedJsonLineStorage: any;

export class ToolHandlerFactory {
    private static graphHandler: GraphToolHandler;
    private static searchHandler: SearchToolHandler;
    private static metadataHandler: MetadataToolHandler;
    private static dynamicHandler: DynamicToolHandler;
    private static workflowHandler: any;
    private static initialized = false;

    /**
     * Initializes all tool handlers
     */
    static async initialize(knowledgeGraphManager: ApplicationManager): Promise<void> {
        if (this.initialized) {
            return;
        }

        this.graphHandler = new GraphToolHandler(knowledgeGraphManager);
        this.searchHandler = new SearchToolHandler(knowledgeGraphManager);
        this.metadataHandler = new MetadataToolHandler(knowledgeGraphManager);
        this.dynamicHandler = new DynamicToolHandler(knowledgeGraphManager);

        // 動態導入工作流程相關模塊以避免循環依賴
        try {
            const { WorkflowToolHandler: WTH } = await import('./WorkflowToolHandler.js');
            const { CachedJsonLineStorage: CJLS } = await import('../../../infrastructure/storage/CachedJsonLineStorage.js');

            WorkflowToolHandler = WTH;
            CachedJsonLineStorage = CJLS;

            // 初始化工作流程處理器（使用擴展存儲）
            const extendedStorage = new CachedJsonLineStorage();
            this.workflowHandler = new WorkflowToolHandler(knowledgeGraphManager, extendedStorage);
        } catch (error) {
            console.warn('[ToolHandlerFactory] Failed to initialize workflow handler:', error);
            // 工作流程處理器初始化失敗不應該阻止其他處理器的工作
        }

        this.initialized = true;
    }

    /**
     * Gets the appropriate handler for a given tool name
     */
    static getHandler(toolName: string): BaseToolHandler {
        if (!this.initialized) {
            throw new Error('ToolHandlerFactory not initialized');
        }

        // First check workflow tools (新增)
        if (toolName.match(/^workflow_/) || toolName.match(/^stage_/)) {
            if (!this.workflowHandler) {
                throw new Error('Workflow handler not initialized. Please ensure ToolHandlerFactory.initialize() was called.');
            }
            return this.workflowHandler;
        }

        // Then check static tools
        if (toolName.match(/^(add|update|delete)_(nodes|edges)$/)) {
            return this.graphHandler;
        }
        if (toolName.match(/^(read_graph|search_nodes|open_nodes)$/)) {
            return this.searchHandler;
        }
        if (toolName.match(/^(add|delete)_metadata$/)) {
            return this.metadataHandler;
        }

        // Then check dynamic tools
        if (toolsRegistry.hasTool(toolName) && toolName.match(/^(add|update|delete)_/)) {
            return this.dynamicHandler;
        }

        throw new Error(`No handler found for tool: ${toolName}`);
    }

    /**
     * Checks if factory is initialized
     */
    static isInitialized(): boolean {
        return this.initialized;
    }
}