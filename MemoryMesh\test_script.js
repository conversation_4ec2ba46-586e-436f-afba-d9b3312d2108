// MemoryMesh v0.3.1 功能測試腳本
import('./dist/integration/tools/handlers/WorkflowToolHandler.js').then(async (handlerModule) => {
  const storageModule = await import('./dist/infrastructure/storage/CachedJsonLineStorage.js');
  const appManagerModule = await import('./dist/application/managers/ApplicationManager.js');
  const dynamicToolsModule = await import('./dist/integration/tools/handlers/DynamicToolHandler.js');

  const storage = new storageModule.CachedJsonLineStorage();
  const appManager = new appManagerModule.ApplicationManager(storage);
  const workflowHandler = new handlerModule.WorkflowToolHandler(appManager, storage);
  const dynamicHandler = new dynamicToolsModule.DynamicToolHandler(appManager);
  
  const workflowId = 'wf_1752590577823_zzcaihzjz';
  
  console.log('🧪 第2階段：規劃階段測試');
  console.log('📚 創建《大奉打更人》主要角色');
  
  // 2.1 創建主角許七安
  console.log('\n👤 2.1 創建主角許七安');
  const xuQianResult = await dynamicHandler.handleTool('add_character', {
    character: {
      name: '許七安',
      role: 'protagonist',
      status: 'Active',
      currentLocation: ['京城', '打更人衙門'],
      description: '現代刑警穿越到大奉王朝，成為打更人，擁有現代偵探思維和古代修行體系',
      traits: ['機智', '正義感', '適應力強', '有時過於自信'],
      background: '21世紀刑警，因意外穿越到大奉王朝，利用現代知識在古代世界生存發展',
      motivation: '在新世界中生存並發揮自己的價值，維護正義',
      internalConflict: '現代思維與古代環境的衝突，身份認同的困惑',
      characterArc: '從迷茫的穿越者成長為真正的守護者',
      age: '25歲（穿越前）',
      gender: '男',
      importance: 'Protagonist',
      moralAlignment: 'Good',
      abilities: ['現代偵探技能', '古代修行', '邏輯推理', '格鬥技巧'],
      strengths: ['邏輯思維', '正義感', '學習能力'],
      weaknesses: ['有時過於理性', '對古代禮儀不熟悉'],
      fears: ['無法回到現代', '失去重要的人'],
      secret: '穿越者身份',
      reputation: '能幹的打更人，破案高手',
      socialStatus: '打更人（低級官員）'
    }
  });
  
  console.log('許七安創建結果:', xuQianResult.isError ? '❌ 失敗' : '✅ 成功');
  if (xuQianResult.isError) {
    console.log('錯誤詳情:', JSON.stringify(xuQianResult, null, 2));
  }
  
  // 2.2 創建重要配角
  console.log('\n👥 2.2 創建重要配角');
  const supportCharacters = [
    {
      name: '魏淵',
      role: 'mentor',
      description: '打更人組織的首領，許七安的上司和引路人',
      traits: ['智慧', '深謀遠慮', '愛護下屬'],
      background: '打更人組織的創建者和領導者，對許七安有特殊關照',
      motivation: '維護大奉王朝的穩定，培養許七安',
      importance: 'Main Character',
      moralAlignment: 'Good'
    },
    {
      name: '懷慶公主',
      role: 'ally',
      description: '大奉王朝的公主，聰明睿智，與許七安合作破案',
      traits: ['聰慧', '堅強', '有責任感'],
      background: '皇室成員，關心民生，支持許七安的工作',
      motivation: '為民除害，維護王朝正義',
      importance: 'Main Character',
      moralAlignment: 'Good'
    }
  ];
  
  for (const char of supportCharacters) {
    const result = await dynamicHandler.handleTool('add_character', { character: char });
    console.log(`${char.name}創建結果:`, result.isError ? '❌ 失敗' : '✅ 成功');
    if (result.isError) {
      console.log(`${char.name}錯誤詳情:`, JSON.stringify(result, null, 2));
    }
  }
  
  // 2.3 創建設定
  console.log('\n🏛️ 2.3 創建大奉王朝設定');
  const settingResult = await dynamicHandler.handleTool('add_setting', {
    setting: {
      name: '大奉王朝',
      type: 'Urban',
      description: '古代封建王朝，有著複雜的政治體系和修行文化，許七安工作和生活的主要背景',
      status: 'Active',
      significance: 'Critical',
      atmosphere: '古樸莊嚴中透著暗流涌動的政治氣息',
      sensoryDetails: '青石板路面，紅牆黃瓦的宮殿，街市的叫賣聲，香火繚繞的寺廟',
      culturalContext: '儒家文化為主，佛道並存，修行者地位崇高',
      notableFeatures: ['皇宮', '打更人衙門', '各大寺廟', '市井街道'],
      relatedCharacters: ['許七安', '魏淵', '懷慶公主']
    }
  });

  console.log('大奉王朝設定創建結果:', settingResult.isError ? '❌ 失敗' : '✅ 成功');
  if (settingResult.isError) {
    console.log('錯誤詳情:', JSON.stringify(settingResult, null, 2));
  }

  // 2.4 創建主題
  console.log('\n🎭 2.4 創建故事主題');
  const themeResult = await dynamicHandler.handleTool('add_theme', {
    theme: {
      name: '正義與成長',
      type: 'Main Theme',
      description: '通過許七安在大奉王朝的探案經歷，探討正義的真諦和個人成長的意義',
      status: 'Developing',
      importance: 'Core',
      currentDevelopment: '主角正在通過各種案件逐步理解古代社會的正義觀念',
      symbolism: ['天平（公正）', '明鏡（真相）', '劍（執行正義的工具）'],
      relatedCharacters: ['許七安', '魏淵', '懷慶公主']
    }
  });

  console.log('正義與成長主題創建結果:', themeResult.isError ? '❌ 失敗' : '✅ 成功');
  if (themeResult.isError) {
    console.log('錯誤詳情:', JSON.stringify(themeResult, null, 2));
  }

  // 2.5 檢查工作流程狀態
  console.log('\n📊 2.5 檢查工作流程狀態');
  const statusResult = await workflowHandler.handleTool('workflow_status', {
    workflowId,
    includeStages: true,
    includeValidation: true
  });

  console.log('工作流程狀態查詢結果:', statusResult.isError ? '❌ 失敗' : '✅ 成功');

  if (!statusResult.isError) {
    const statusData = JSON.parse(statusResult.content[1].text.split('Data: ')[1]);
    console.log('當前階段:', statusData.workflow.currentStage);
    console.log('總進度:', statusData.workflow.progress + '%');

    // 2.6 測試增強的階段驗證
    console.log('\\n🔍 2.6 測試v0.3.1增強的階段驗證');
    const validationResult = await workflowHandler.handleTool('stage_validate', {
      workflowId
    });

    console.log('階段驗證結果:', validationResult.isError ? '❌ 失敗' : '✅ 成功');

    if (!validationResult.isError) {
      const data = JSON.parse(validationResult.content[1].text.split('Data: ')[1]);

      console.log('\\n📊 驗證詳情:');
      console.log('  完成狀態:', data.isComplete ? '✅ 完成' : '❌ 未完成');
      console.log('  質量分數:', data.qualityAssessment?.overallScore || 'N/A', '/100');

      if (data.nextStepGuidance) {
        console.log('\\n🎯 下一步指導:');
        data.nextStepGuidance.immediateActions?.forEach(action => {
          console.log('  •', action);
        });

        console.log('\\n💡 質量提升建議:');
        data.nextStepGuidance.qualityTips?.forEach(tip => {
          console.log('  •', tip);
        });
      }

      if (data.progressInsights) {
        console.log('\\n🔍 進度洞察:');
        console.log('  當前重點:', data.progressInsights.currentFocus);

        if (data.progressInsights.strengthAreas?.length > 0) {
          console.log('  優勢領域:');
          data.progressInsights.strengthAreas.forEach(area => {
            console.log('    ✅', area);
          });
        }

        if (data.progressInsights.improvementAreas?.length > 0) {
          console.log('  改進領域:');
          data.progressInsights.improvementAreas.forEach(area => {
            console.log('    🔧', area);
          });
        }
      }
    } else {
      console.log('驗證錯誤詳情:', JSON.stringify(validationResult, null, 2));
    }
  } else {
    console.log('狀態查詢錯誤詳情:', JSON.stringify(statusResult, null, 2));
  }



  return workflowId;
}).catch(console.error);
