{"type":"node","name":"workflow_wf_1752622336182_2jxmv2lpb","nodeType":"workflow","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","template_id: novel_standard_v1","template_version: 1.0.0","category: novel","status: not_started","current_stage: 0","total_stages: 4","progress: 0","created_at: 2025-07-15T23:32:16.182Z","updated_at: 2025-07-15T23:32:16.183Z","stages: [{\"id\":\"planning\",\"name\":\"規劃階段\",\"order\":0,\"status\":\"active\"},{\"id\":\"outline\",\"name\":\"大綱階段\",\"order\":1,\"status\":\"pending\"},{\"id\":\"chapter\",\"name\":\"章節階段\",\"order\":2,\"status\":\"pending\"},{\"id\":\"generation\",\"name\":\"生成階段\",\"order\":3,\"status\":\"pending\"}]"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_planning","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: planning","stage_name: 規劃階段","stage_order: 0","status: active","required_node_types: [\"character\",\"setting\",\"theme\"]","optional_node_types: [\"timeline\",\"worldbuilding\"]","completion_criteria: {\"minNodes\":3,\"requiredFields\":[\"name\",\"description\",\"background\"],\"minimumQualityScore\":70,\"qualityChecks\":[{\"type\":\"character_depth\",\"description\":\"角色需要有明確的動機、衝突和成長弧線\",\"validator\":\"character_quality_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"setting_richness\",\"description\":\"設定需要包含感官細節和文化背景\",\"validator\":\"setting_quality_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"theme_clarity\",\"description\":\"主題應該具體且能通過故事元素體現\",\"validator\":\"theme_quality_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"character\":{\"description\":\"創建具有三維特質的角色：外在目標、內在需求、致命缺陷\",\"examples\":[\"主角：年輕騎士，外在目標是拯救王國，內在需求是證明自己的價值，致命缺陷是過度自信\",\"反派：墮落法師，外在目標是獲得永生，內在需求是被理解和接納，致命缺陷是無法信任他人\"],\"qualityTips\":[\"每個角色都應該有獨特的說話方式和行為模式\",\"角色的背景故事要與當前情節相關\",\"確保角色有成長和變化的空間\"],\"commonMistakes\":[\"創建完美無缺的角色\",\"角色動機不明確或不合理\",\"所有角色說話方式相同\"]},\"setting\":{\"description\":\"描述不僅是地點，更是氛圍和情感的載體\",\"examples\":[\"古老的圖書館：塵埃在陽光中飛舞，羊皮紙的味道混合著蠟燭的香氣，暗示著知識的神秘和時間的流逝\",\"戰場：泥濘的土地，鐵鏽和血腥的味道，遠處的號角聲，體現戰爭的殘酷和英雄主義的複雜性\"],\"qualityTips\":[\"使用五感描述來增強沉浸感\",\"讓環境反映角色的內心狀態\",\"考慮設定對情節發展的影響\"],\"commonMistakes\":[\"過度描述無關緊要的細節\",\"設定與故事氛圍不符\",\"忽略環境對角色行為的影響\"]},\"theme\":{\"description\":\"主題應該通過角色行動和情節發展自然呈現\",\"examples\":[\"成長主題：通過主角面對挑戰、犯錯、學習的過程來體現\",\"友誼主題：通過角色間的互助、衝突、和解來展現\"],\"qualityTips\":[\"避免直接說教，讓主題通過故事自然流露\",\"確保主題與角色弧線和情節發展一致\",\"可以有多個相關的次主題\"],\"commonMistakes\":[\"主題過於抽象或模糊\",\"強行插入主題相關的對話\",\"主題與故事內容脫節\"]}}}","estimated_duration: 1-2 weeks","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_outline","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: outline","stage_name: 大綱階段","stage_order: 1","status: pending","required_node_types: [\"plotarc\",\"timeline\"]","optional_node_types: [\"conflict\",\"theme\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"main_plot\",\"key_events\"],\"minimumQualityScore\":75,\"qualityChecks\":[{\"type\":\"plot_structure\",\"description\":\"情節結構需要有清晰的起承轉合\",\"validator\":\"plot_structure_validator\",\"weight\":0.5,\"required\":true},{\"type\":\"pacing_balance\",\"description\":\"節奏安排需要有張弛有度的變化\",\"validator\":\"pacing_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"conflict_escalation\",\"description\":\"衝突需要有合理的升級和解決\",\"validator\":\"conflict_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"plotarc\":{\"description\":\"建立引人入勝的情節弧線，包含起因、發展、高潮、結局\",\"examples\":[\"三幕結構：建立(25%) → 對抗(50%) → 解決(25%)\",\"英雄之旅：平凡世界 → 冒險召喚 → 拒絕召喚 → 遇見導師 → 跨越門檻...\"],\"qualityTips\":[\"確保每個情節點都推動故事前進\",\"高潮應該是情感和行動的雙重頂點\",\"結局要回應開頭提出的問題\"],\"commonMistakes\":[\"中段情節拖沓，缺乏推進力\",\"高潮來得太突然或太平淡\",\"結局過於匆忙或留下太多未解之謎\"]},\"timeline\":{\"description\":\"建立清晰的時間線，確保事件邏輯合理\",\"examples\":[\"線性時間線：按時間順序發展的故事\",\"非線性時間線：使用倒敘、插敘等技巧\"],\"qualityTips\":[\"重要事件之間要有合理的時間間隔\",\"考慮季節、天氣對故事氛圍的影響\",\"確保角色成長與時間推移相符\"],\"commonMistakes\":[\"時間跳躍過於頻繁或突兀\",\"忽略時間對角色和環境的影響\",\"前後時間設定不一致\"]}}}","estimated_duration: 1 week","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_chapter","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: chapter","stage_name: 章節階段","stage_order: 2","status: pending","required_node_types: [\"scene\",\"chapter\"]","optional_node_types: [\"dialogue\",\"description\"]","completion_criteria: {\"minNodes\":5,\"requiredFields\":[\"scene_setting\",\"characters_involved\"],\"minimumQualityScore\":80,\"qualityChecks\":[{\"type\":\"scene_depth\",\"description\":\"場景需要有豐富的感官細節和情感層次\",\"validator\":\"scene_depth_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"dialogue_authenticity\",\"description\":\"對話需要符合角色性格且推動情節\",\"validator\":\"dialogue_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"pacing_control\",\"description\":\"章節節奏需要有適當的張弛變化\",\"validator\":\"chapter_pacing_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"scene\":{\"description\":\"創建生動的場景，平衡行動、對話、描述和內心獨白\",\"examples\":[\"行動場景：劍鬥 - 快節奏，短句，感官細節，情緒張力\",\"對話場景：談判 - 角色動機衝突，潛台詞，肢體語言\",\"反思場景：獨處 - 內心掙扎，回憶片段，情感深度\"],\"qualityTips\":[\"每個場景都要有明確的目的和衝突\",\"使用具體的感官細節增強沉浸感\",\"確保場景推動情節或角色發展\",\"注意場景間的過渡和連接\"],\"commonMistakes\":[\"場景過長或過短，節奏失衡\",\"缺乏具體的環境描述\",\"場景目的不明確，流於表面\",\"忽略角色在場景中的情感變化\"]},\"chapter\":{\"description\":\"構建完整的章節，有開頭鉤子、發展和結尾懸念\",\"examples\":[\"開頭鉤子：突發事件、謎團、衝突、有趣對話\",\"章節發展：推進主線、深化角色、揭示信息\",\"結尾懸念：未解問題、新的威脅、情感轉折\"],\"qualityTips\":[\"每章都要推進整體故事進程\",\"保持讀者的閱讀興趣和期待\",\"平衡不同類型的內容（行動、對話、描述）\",\"確保章節長度適中且一致\"],\"commonMistakes\":[\"章節缺乏內在結構和節奏\",\"結尾過於平淡，缺乏懸念\",\"章節間缺乏連貫性\",\"過度依賴對話或描述\"]}}}","estimated_duration: 2-3 weeks","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_generation","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: generation","stage_name: 生成階段","stage_order: 3","status: pending","required_node_types: [\"chapter\"]","optional_node_types: [\"revision_notes\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"content\",\"word_count\"],\"minimumQualityScore\":85,\"qualityChecks\":[{\"type\":\"narrative_consistency\",\"description\":\"敘述風格和視角需要保持一致\",\"validator\":\"narrative_consistency_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"character_voice\",\"description\":\"角色聲音需要獨特且一致\",\"validator\":\"character_voice_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"thematic_integration\",\"description\":\"主題需要自然融入故事中\",\"validator\":\"theme_integration_validator\",\"weight\":0.2,\"required\":true},{\"type\":\"prose_quality\",\"description\":\"文筆需要流暢且富有表現力\",\"validator\":\"prose_quality_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"chapter\":{\"description\":\"完成高質量的章節內容，整合所有故事元素\",\"examples\":[\"完整章節：開頭引人入勝，中段發展充實，結尾留有懸念\",\"修訂版本：基於反饋改進的內容，提升可讀性和影響力\"],\"qualityTips\":[\"確保每章都有明確的情節進展\",\"保持敘述聲音的一致性\",\"平衡描述、對話、行動和內心獨白\",\"注意章節間的過渡和連貫性\",\"最終校對語法、拼寫和格式\"],\"commonMistakes\":[\"急於完成而忽略質量\",\"前後章節風格不一致\",\"忽略細節的打磨和完善\",\"缺乏最終的整體審視和修訂\"]}}}","estimated_duration: 4-6 weeks","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"2150年的地球","nodeType":"setting","metadata":["name: 2150年的地球","type: Natural","description: 時間設定在2150年，此時的地球科技已高度發達。然而，世界正籠罩在巨大的危機之下：太陽黑子活動異常降低，導致全球氣溫急劇下降，威脅著所有生命。為了生存，人類依賴先進科技進行大規模的人工除雲作業，試圖讓更多的太陽輻射抵達地表以維持溫度。但根據超級AI的精密計算，這項措施只是飲鴆止渴，將會無止盡地消耗地球的寶貴資源。若情況持續，人類將在能夠建造足夠的逃生艦隊前耗盡所有資源，最終被困在冰封的地球上，迎來滅亡。","status: Active","atmosphere: 充滿末日感的絕望與掙扎，科技的冰冷與人性的求生慾望交織。","significance: Critical","notableFeatures: 全球性的人工除雲系統, 用於監控地球狀態與資源消耗的超級AI, 因氣溫下降而擴張的冰川與冰封城市, 為逃離地球而建造的巨型星際艦隊船塢","symbolicMeaning: 人類在科技、自然與生存之間的掙扎，以及希望與絕望的對立。"]}
{"type":"node","name":"即刻作戰_全員逃離地球","nodeType":"novel_project","metadata":["Projectname: 即刻作戰_全員逃離地球","Genre: 科幻, 硬科幻, 末日, 網路小說","Targetaudience: 喜歡慢節奏、硬科幻、沉浸式體驗及深度角色刻劃的讀者。","Writingstyle: 超慢節奏商業網文，超級沉浸式，著重在角色內心描寫和科技細節。","Plannedlength: 約200萬字","Currentprogress: 規劃階段 - 核心世界觀與風格設定完成","Thematicelements: 末日下的生存、科技與人性的掙扎、希望與絕望、集體主義與個人犧牲。","Qualitytargets: 提供高度沉浸感，透過細膩的科技描寫與深刻的角色內心戲，引發讀者對末日情境的深度思考。"]}
{"type":"node","name":"第一部：溫水煮青蛙","nodeType":"plotarc","metadata":["name: 第一部：溫水煮青蛙","type: Main Plot","description: 故事的開端。危機已經存在，但尚未達到臨界點。『方舟計畫』在高層中是個秘密，對普通大眾來說，生活只是變得『有點不方便』。透過主角的視角，展現2150年世界的日常、科技與潛在的危機，並在結尾以一場災難將末日預言推向檯面，啟動整個故事的核心衝突。","status: Planning","importance: Critical","progressPercentage: 0","mainCharacters: "]}
{"type":"node","name":"李昂","nodeType":"character","metadata":["Name: 李昂","Role: 除雲塔資深工程師","Status: Introduced","Description: 一位典型的技術專家，負責維護巨型『人工除雲塔』。他對系統瞭若指掌，相信技術能解決一切問題，但內心深處對系統日益增長的異常數據感到不安。他有輕微的強迫症和社交恐懼，更喜歡與機器打交道。","Abilities: 擁有被稱為『數據直感』的特殊能力。這是一種罕見的神經狀態，讓他能以直覺感知龐大的數據流。他能『感覺』到系統的健康狀態，將複雜的數據模式識別為和諧的『共鳴』或刺耳的『噪音』，從而發現連AI都可能忽略的潛在故障。","Importance: Protagonist","Motivation: 維持系統的完美運行，並找出讓他感到不安的數據異常的根源。","Internalconflict: 對『秩序』的強迫性追求，與一個走向終極混亂的世界之間的根本矛盾。他對處理複雜情感的迴避，與末日下越發激烈的人性衝突之間的矛盾。他對『真相』近乎殉道的執著，與權力者需要用謊言維持穩定之間的矛盾。","Currentlocation: 京津冀一號除雲塔","Background: 出生於全球變冷初期的普通工程師家庭，成長環境充滿了對科技的信賴和對未來的樂觀主義。從小就展現出與眾不同的『數據直感』天賦，但在標準化的教育體系中，這種天賦被視為『注意力不集中』。他憑藉自身的努力成為除雲塔的頂尖工程師，其背景代表了在UEG治下成長起來的、對官方宣傳深信不疑的一代人。","Traits: 『秩序』的強迫症：內心極度渴求可預測性和控制感，對任何形式的『失序』都難以忍受。, 『情感』的迴避者：不擅長處理複雜的人際關係和強烈的情感，習慣性地退回到有邏輯的技術世界。, 『真相』的殉道者：一旦確認了某件事是『真實』的，會不計後果地去揭示它，恢復他所認為的『秩序』。"]}
{"type":"node","name":"京津冀一號除雲塔","nodeType":"setting","metadata":["name: 京津冀一號除雲塔","type: Building","description: 位於前華北平原的巨型人工除雲設施，是全球除雲網絡的關鍵節點之一。塔身直入雲霄，結構複雜，是人類試圖對抗自然變化的象徵性建築。","status: Active"]}
{"type":"node","name":"聯合地球政府 (UEG)","nodeType":"organization","metadata":["name: 聯合地球政府 (UEG)","type: Government Agency","description: 名義上的全球最高權力機構，負責統籌「全員逃離地球」計畫（方舟計畫），並試圖在末日降臨前維持社會秩序。其內部充滿了官僚主義、政治鬥爭和不同國家勢力的角力，決策往往是多方妥協的產物，效率低下且充滿秘密。","status: Active","goals: 統籌『方舟計畫』的執行, 維持全球社會的基本秩序, 管理全球所剩不多的資源分配","influence: Global","publicReputation: 對外宣傳為人類唯一的希望，但因其效率低下和資源分配不公而飽受民眾詬病。","internalCulture: 官僚、階級森嚴、充滿秘密主義，內部派系林立，代表著舊時代國家力量的延續。","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"全球資源再利用總署 (GRRA)","nodeType":"organization","metadata":["name: 全球資源再利用總署 (GRRA)","type: Government Agency","description: 在資源極度匱乏的背景下應運而生的超級機構。GRRA最初只是個回收部門，但現在他們控制著從廢棄的太空站、冰封的城市，到每個人生活垃圾的一切「可回收物質」的定義權、所有權和分配權。他們是原材料的絕對壟斷者，對「方舟計畫」的建造進度有著舉足輕重的影響。","status: Active","goals: 最大化回收地球上所有可用資源, 壟斷原材料供應鏈, 在『方舟計畫』中爭取最大話語權","influence: Global","publicReputation: 被民眾視為冷酷的資源掠奪者，但又不得不依賴他們提供的再生資源。是一個讓人又敬又畏的存在。","internalCulture: 極端務實、高效、冷酷無情，信奉結果主義，對浪費零容忍。內部結構類似軍隊和大型企業的結合體。","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"蓋亞的遺民","nodeType":"organization","metadata":["name: 蓋亞的遺民","type: Secret Society","description: 一個極端的環保主義與地球本土主義結合的地下組織。他們堅信人類的命運與地球緊密相連，逃離是一種懦弱的背叛。他們視「方舟計畫」為最大的罪惡，致力於用各種手段（包括破壞）來阻止艦隊的建造。","status: Active","goals: 阻止『方舟計畫』, 迫使人類將資源用於修復地球生態, 喚醒民眾對地球的『信仰』","influence: Underground","publicReputation: 被UEG定義為恐怖組織，但在部分絕望的民眾中被視為英雄或先知。","internalCulture: 充滿理想主義和宗教狂熱的氛圍，成員背景複雜，從狂熱信徒到頂尖科學家都有，組織嚴密且行動力強。","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"科技的傲慢與自然的懲罰","nodeType":"theme","metadata":["name: 科技的傲慢與自然的懲罰","type: Main Theme","description: 故事的核心哲學思辨，探討人類過度依賴科技而忽視自然規律，最終導致自我毀滅的困境。主題表現為一場慢性的、不可逆的衰亡，而非突發災難。重點在於『解藥即是新毒藥』的惡性循環、人類『自然直覺』的喪失，以及在自然的『非人格化』審判面前，科技作為『詛咒』與『方舟』的雙重矛盾。","status: Introduced","importance: Core","currentDevelopment: 已確立核心概念和四大表現要點","symbolism: 人工除雲塔, 超級AI, 冰封的城市, 方舟計畫本身"]}
{"type":"node","name":"學者之家養老社區","nodeType":"setting","metadata":["name: 學者之家養老社區","type: Building","description: 位於UEG亞洲總部附近，專為對人類有卓越貢獻的科學家、學者和官員提供的高科技養老社區。表面上是頤養天年的地方，實則可能暗流湧動，是各種舊勢力交換情報的中心。","status: Active"]}
{"type":"node","name":"王教授","nodeType":"character","metadata":["name: 王教授","role: 退休科學家，李昂的導師","status: Active","description: 一位即將退休的老科學家，是李昂所在領域的權威，也是少數還記得『真實自然』是什麼樣的人。他相信數據，但更相信經驗和直覺。","background: 曾是『方舟計畫』前身，甚至是超級AI早期開發團隊的核心成員之一，後因不明原因『榮譽退休』。可能在暗中研究一個拯救地球的『B計畫』。","importance: Supporting Character","motivation: 不明，表面上是安度晚年，實際上可能在培養『B計畫』的接班人，或彌補過去的錯誤。","currentLocation: 學者之家養老社區"]}
{"type":"node","name":"陳靜","nodeType":"character","metadata":["name: 陳靜","role: GRRA監察員","status: Introduced","description: 年輕、果斷、極端務實的GRRA監察員，是GRRA冷酷效率的化身。她起初完全不理解李昂基於『直覺』的擔憂，認為這是浪費資源的無稽之談。","background: 其家人可能在早年的資源災難中，因官方的低效和浪費而喪生，這段經歷塑造了她對『浪費』的極度憎恨和冷酷務實的性格。她可能帶著GRRA高層的秘密任務，評估除雲塔的『回收價值』。","importance: Supporting Character","motivation: 表面上是確保資源利用效率最大化，深層動機是復仇——向所有低效、浪費的體制復仇。","currentLocation: 京津冀一號除雲塔"]}
{"type":"node","name":"李昂與王教授的師生關係","nodeType":"relationship","metadata":["name: 李昂與王教授的師生關係","relationshipType: Mentor-Student","status: Stable","intimacyLevel: 7","trustLevel: 8","conflictLevel: 2","character1Perspective: 尊敬他，視他為該領域的權威和導師，但有時不理解他為何對新技術抱持懷疑，並對他的『數據直感』天賦感到憂慮。","character2Perspective: 欣賞李昂的才華和純粹，但擔心他過於依賴技術。他似乎在有意引導李昂去思考更深層次的問題，可能是在為自己的『B計畫』尋找傳承者。","description: 一段亦師亦友的關係。王教授是李昂在專業領域的引路人，但在理念上，兩人之間存在著微妙的代溝和張力。這段關係背後可能隱藏著關於『方舟計畫』和AI的過去秘密。","character1: 李昂","character2: 王教授"]}
{"type":"node","name":"李昂與陳靜的對手關係","nodeType":"relationship","metadata":["name: 李昂與陳靜的對手關係","relationshipType: Rivalry","status: Strained","intimacyLevel: 1","trustLevel: 2","conflictLevel: 8","character1Perspective: 認為她是一個冷酷、不近人情、只看重數據和指標的官僚。對她的行事風格感到反感，但又不得不佩服她的效率。","character2Perspective: 認為李昂是一個不切實際、感情用事、甚至有點神經質的工程師。對他基於『直覺』的判斷嗤之以鼻，視其為潛在的資源浪費源頭。","description: 一段充滿衝突的對手關係。兩人因工作方式和價值觀的巨大差異而頻繁碰撞。然而，隨著危機加深，他們可能會發現彼此是唯一可以依賴的人，從而發展出一段複雜的合作關係。","character1: 李昂","character2: 陳靜"]}
{"type":"node","name":"測試角色Alpha","nodeType":"character","metadata":["name: 測試角色Alpha","role: protagonist","status: Active","description: 專門用於BUG測試的最小化角色設定","background: 這是一個用於驗證stage_validate功能的測試角色","traits: 測試屬性","importance: Protagonist","motivation: 測試系統功能","currentLocation: 測試地點"]}
{"type":"node","name":"測試地點","nodeType":"setting","metadata":["name: 測試地點","type: Indoor","description: 用於BUG測試的最小化地點設定","status: Active","significance: Minor"]}
{"type":"node","name":"測試角色Beta","nodeType":"character","metadata":["name: 測試角色Beta","role: protagonist","status: Active","description: 專門用於BUG測試的最小化角色設定","background: 這是一個用於驗證stage_validate功能的測試角色","traits: 測試屬性","importance: Protagonist","motivation: 測試系統功能","currentLocation: 測試地點"]}
{"type":"node","name":"測試主題","nodeType":"theme","metadata":["name: 測試主題","type: Main Theme","description: 用於BUG測試的最小化主題設定","status: Developing","importance: Core","currentDevelopment: 正在測試系統功能"]}
{"type":"node","name":"塔的嗡鳴","nodeType":"event","metadata":["name: 塔的嗡鳴","type: Plot Event","description: 在一次對『京津冀一號除雲塔』的常規系統維護中，主角李昂透過他獨特的『數據直感』能力，在海量的、看似正常的系統數據流中，『聽』到了一絲微弱且不祥的『嗡鳴』——一個現有AI和所有監測儀器都無法識別的系統性異常。這個發現讓他深感不安，也為整個故事埋下了第一顆不安的種子。","importance: Plot Advancement","emotionalTone: Mysterious","participants: 李昂","location: 京津冀一號除雲塔","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"冰霜下的交易","nodeType":"event","metadata":["name: 冰霜下的交易","type: Setting Description","description: 李昂下班後，進入一個位於地下供暖管道系統中的半合法市集。他試圖用自己寶貴的能源配給，換取一些來自小型生態溫室的『奢侈品』，如天然番茄或咖啡豆。這個場景將生動地展現資源配給制度下的社會樣貌、黑市的存在，以及普通人在壓抑環境中的一點生活追求。","importance: Background/Atmosphere","emotionalTone: Melancholic","participants: 李昂","location: 地下能源黑市","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"地下能源黑市","nodeType":"setting","metadata":["name: 地下能源黑市","type: Public Space","description: 位於城市廢棄的地下供暖管道系統中，是一個半地下的交易場所。在這裡，人們可以用官方的能源配給或以物易物的方式，交換到官方渠道難以獲得的各種商品，從天然食物到違禁的舊時代娛樂數據，應有盡有。","status: Active","atmosphere: 空氣中混雜著濕氣、機油和各種食物的香料味，既有底層社會的活力，也充滿了壓抑和不信任感。"]}
{"type":"node","name":"機器中的幽靈","nodeType":"event","metadata":["name: 機器中的幽靈","type: Plot Event","description: 李昂的『數據直感』捕捉到一次前所未有的系統性異常——一個他稱之為『幽靈』的微弱但持續的『噪音』。他正式上報此事，但其報告被上級和AI的診斷報告駁回，認為是他的神經過敏或設備的傳感器誤差。這次事件標誌著他與體制的第一次正面衝突，並促使他去尋找唯一可能理解他的王教授求助。","importance: Major Turning Point","emotionalTone: Tense","participants: 李昂, 陳靜","location: 京津冀一號除雲塔","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"哈爾濱的長夜","nodeType":"event","metadata":["name: 哈爾濱的長夜","type: Historical Event","description: 一場史無前例的強烈太陽風暴，疊加了李昂之前發現的『幽靈』系統異常，導致以哈爾濱為中心的東北除雲網絡大規模、連鎖性崩潰。在缺乏人工除雲的保護下，城市氣溫在短短數小時內驟降至毀滅性的零下100攝氏度以下。這場災難透過無數的個人終端直播給全世界，成為人類進入全球變冷時代以來最慘烈的悲劇。","importance: Climactic","consequences: 大量人員傷亡, UEG的公信力受到致命打擊, 全球民眾陷入恐慌, 為『方舟計畫』的公佈埋下伏筆","emotionalTone: Tragic","location: 哈爾濱","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"哈爾濱","nodeType":"setting","metadata":["name: 哈爾濱","type: Urban","description: 位於東北地區的重工業城市。在全球變冷時代，依靠區域性的除雲網絡維持著運轉。在『哈爾濱長夜』事件中，因除雲網絡崩潰而被極低溫摧毀，成為人類末日危機的標誌性悲劇地點。","status: Destroyed","atmosphere: 災難前是充滿歷史感的北方城市，災難後則變為死寂、悲傷的冰雪紀念碑。"]}
{"type":"node","name":"方舟揭示","nodeType":"event","metadata":["name: 方舟揭示","type: Revelation","description: 在『哈爾濱長夜』災難引發的全球性壓力和質疑聲中，聯合地球政府(UEG)再也無法隱瞞真相。通過全球緊急廣播，UEG最高領導人向全人類公佈了超級AI的末日預言，以及他們已秘密進行多年的『方舟計畫』。這個消息徹底擊碎了民眾的僥倖心理，世界從此進入一個新的、更加混亂的時代。","importance: Major Turning Point","consequences: 全球秩序開始崩潰, 引發大規模的社會動盪和絕望情緒, 故事正式進入第二部『大動員』","emotionalTone: Hopeful","participants: 聯合地球政府 (UEG)","location: 聯合地球政府 (UEG)","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"第二部：大動員","nodeType":"plotarc","metadata":["name: 第二部：大動員","type: Main Plot","description: 在『方舟計畫』公佈後，全人類被動員起來，為建造星際艦隊而掙扎。本篇章將集中展現硬科幻的建造細節、因『貢獻度積分』引發的劇烈社會矛盾、來自『蓋亞的遺民』的外部威脅，並在結尾以一次災難性的技術失敗，將方舟計畫推向破產邊緣，為第三部『裂痕』的到來埋下伏筆。","status: Planning","importance: Critical","progressPercentage: 0","mainCharacters: 李昂, 陳靜, 王教授"]}
{"type":"node","name":"第三部：裂痕","nodeType":"plotarc","metadata":["name: 第三部：裂痕","type: Main Plot","description: 在船塢災難後，希望變得渺茫，社會秩序開始瓦解。本篇章將聚焦於信任的崩潰、資源的爭奪和殘酷的道德困境。主角李昂將在調查災難真相的過程中，被迫逃亡，並最終發現一個足以顛覆整個『方舟計畫』的驚天秘密，使他陷入關於『真相』與『謊言』的終極抉擇。","status: Planning","importance: Critical","progressPercentage: 0","mainCharacters: 李昂, 陳靜, 王教授"]}
{"type":"node","name":"第四部：出埃及記","nodeType":"plotarc","metadata":["name: 第四部：出埃及記","type: Main Plot","description: 在李昂做出關鍵抉擇後，故事進入最終章。本篇章將根據主角的選擇，展現人類社會在末日面前最後的掙扎、混亂與犧牲。最終，一艘小型的『種子船』將承載著人類文明的火種離開地球，駛向未知的宇宙，同時留下一個關於未來的、開放式的結局。","status: Planning","importance: Critical","progressPercentage: 0","mainCharacters: 李昂, 陳靜"]}
{"name":"故事主時間線","metadata":["type: Master Timeline","description: 記錄了『即刻作戰_全員逃離地球』故事中的關鍵事件及其發生時間，以2150年『方舟揭示』為核心基準點。","2148年: 塔的嗡鳴、機器中的幽靈","2149年: （留白）危機發酵期","2150年 冬: 哈爾濱的長夜","2150年 末: 方舟揭示，第一部結束","2151-2155年: 第二部 - 大動員，全球建艦時期","2155年: 龍骨上的裂痕，第二部結束","2156年: 第三部 - 裂痕，信任崩潰與秘密揭示","2157年: 第四部 - 出埃及記，最終結局"],"nodeType":"timeline","type":"node"}
{"type":"node","name":"龍骨船塢","nodeType":"setting","metadata":["name: 龍骨船塢","type: Industrial","description: 位於戈壁沙漠的巨型星艦總裝基地，是『方舟計畫』的核心工程所在地。無數工程師、科學家和軍人在此聚集，為了建造人類最後的希望而奮鬥。","status: Active","atmosphere: 充滿了壓倒性的工業美學、希望與絕望的混合氣息。白天是人造太陽下繁忙的景象，夜晚則是星空下鋼鐵巨獸的剪影。"]}
{"type":"node","name":"伊萬諾夫 (Ivanov)","nodeType":"character","metadata":["name: 伊萬諾夫 (Ivanov)","role: 星艦能源核心專家","status: Deceased","description: 一位經驗豐富、才華橫溢但嗜酒如命的能源核心專家。性格豪放不羈，是李昂在船塢結識的第一個朋友，也是技術上的重要夥伴。","background: 前蘇聯時代太空計畫科學家的後代，繼承了家族對星辰的渴望和對酒精的熱愛。在船塢災難中，為了手動關閉失控的反應核心而犧牲。","importance: Minor Character","motivation: 在地球毀滅前，親手點燃一艘能飛向星海的船。","currentLocation: 龍骨船塢"]}
{"type":"node","name":"佐藤博士 (Dr. Sato)","nodeType":"character","metadata":["name: 佐藤博士 (Dr. Sato)","role: 星艦生態循環專家","status: Active","description: 一位年輕、聰明且富有同情心的女科學家，負責『方舟』的生態循環系統。她常常提醒李昂和伊萬諾夫，他們的系統最終是為脆弱的生命服務的。","background: 出生於日本一個著名的生物學世家，是生態循環和小型生物圈領域的天才。她對生命的看法與工程師們的機械論截然不同。","importance: Minor Character","motivation: 建造一個能讓生命在冰冷宇宙中延續下去的『諾亞方舟』。","currentLocation: 龍骨船塢"]}
{"type":"node","name":"趙上校 (Colonel Zhao)","nodeType":"character","metadata":["name: 趙上校 (Colonel Zhao)","role: 龍骨船塢軍方監管員","status: Active","description: 一位不苟言笑、紀律嚴明的軍方監管人員，負責監督『龍骨船塢』的建造進度和安全。他將所有問題都視為對任務的威脅。","background: 在UEG成立前的國家軍隊中服役，經歷過資源戰爭的殘酷。他對文職人員的『軟弱』和『低效』抱持著深深的不信任。","importance: Minor Character","motivation: 不惜一切代價，確保至少有一艘船能按時完工，完成他的使命。","currentLocation: 龍骨船塢"]}
{"type":"node","name":"委員會主席","nodeType":"character","metadata":["name: 委員會主席","role: UEG獨立調查委員會主席","status: Active","description: 一位資深的UEG官僚，船塢災難獨立調查委員會的主席。他的目標不是尋找真相，而是為災難找到一個可以被接受的『解釋』和一個合適的『替罪羊』。","background: 在UEG的官僚體系中摸爬滾打多年，深諳政治鬥爭和權力平衡之術。他本人可能並不邪惡，但他堅信『穩定』高於一切，包括真相。","importance: Minor Character","motivation: 維護UEG的統治穩定，平息民憤，確保『方舟計畫』的權威性不受動搖。","currentLocation: 聯合地球政府 (UEG)"]}
{"type":"node","name":"黑市攤主","nodeType":"character","metadata":["name: 黑市攤主","role: 地下商人","status: Active","description: 在地下黑市經營一個小攤位的神秘老人。他用自己溫室裡種植的『奢侈品』與李昂交易，言談中充滿了對現實的嘲諷和對過去的懷念。","background: 可能是一位在『全球變冷』中失去土地和家園的老農，或是對舊時代有著深刻記憶的歷史學者。他見證了太多變遷，對官方的任何宣傳都抱持著懷疑。","importance: Minor Character","motivation: 在末日中，守住一點屬於『真實』和『自然』的東西。","currentLocation: 地下能源黑市"]}
{"nodeType":"chapter","metadata":["title: 數據的低語","part: 第一部：溫水煮青蛙","event: 塔的嗡鳴","pov: 李昂","goal: 建立李昂的日常生活和性格特點；展示2150年的科技社會面貌；首次引入李昂的『數據直感』能力；埋下核心懸念——『嗡鳴』的出現；營造一種表面平靜，實則暗流涌動的氛圍。","start: 從李昂的清晨儀式開始，展現他對秩序的追求。","end: 李昂在數據中聽到『嗡鳴』，但所有儀器都顯示正常，留下懸念。"],"name":"第一章 數據的低語","type":"node"}
{"nodeType":"chapter","metadata":["title: 冰霜下的真實","part: 第一部：溫水煮青蛙","event: 冰霜下的交易","pov: 李昂","goal: 展現地表世界與地下黑市的強烈對比；透過李昂的內心獨白，解釋新鮮食物的稀有性和黑市的存在意義；深化李昂對『真實』的渴望，以及他對『秩序』的執著與『混亂』的地下世界之間的矛盾；為後續的『機器中的幽靈』事件做鋪墊，暗示李昂內心的不安。","start: 李昂離開除雲塔，前往地下黑市。","end: 李昂成功換取番茄，品嚐真實的滋味，但內心的『嗡鳴』依然存在。"],"name":"第二章 冰霜下的真實","type":"node"}
{"type":"node","name":"林阿姨","nodeType":"character","metadata":["name: 林阿姨","role: 清潔機器人維護員","status: Active","description: 李昂公寓樓的清潔機器人維護員，一位年邁但眼神銳利的老婦人。她代表著舊時代的『人情味』和『經驗智慧』，與李昂的數據化生活形成對比。她可能會抱怨機器人或公寓系統的『小毛病』。","background: 一位在舊時代成長起來的普通市民，經歷過全球變冷初期的混亂。她對科技的依賴有著清醒的認識，但更相信人與人之間的溫情和經驗的價值。","importance: Minor Character","motivation: 在末日中，盡力維持自己和周圍人的生活秩序，並傳遞一些被數據化世界所遺忘的『人情味』。","currentLocation: 李昂的公寓樓"]}
{"type":"node","name":"李昂的公寓樓","nodeType":"setting","metadata":["name: 李昂的公寓樓","type: Building","description: 李昂居住的公寓樓，位於城市中心區，是UEG為高級技術人員提供的標準化住所。樓內一切都由AI和機器人管理，強調效率和秩序。","status: Active","atmosphere: 高度自動化、極簡、潔淨，充滿了科技的冰冷感，但偶爾會因為林阿姨的存在而透出一絲人情味。"]}
{"type":"node","name":"瘦猴","nodeType":"character","metadata":["name: 瘦猴","role: 情報販子","status: Active","description: 地下黑市的情報販子，一個機靈、消息靈通但行為鬼祟的年輕人。他代表著地下世界的『信息流動』和『生存智慧』。他對李昂的出現感到好奇，並可能主動接近他。","background: 在地下黑市中摸爬滾打多年，對各種信息和生存法則有著敏銳的嗅覺。他沒有固定的立場，只為利益服務，但有時也會被一些『有趣』的事情所吸引。","importance: Minor Character","motivation: 在混亂的末日中生存下去，並從信息交易中獲取利益。","currentLocation: 地下能源黑市"]}
{"type":"node","name":"workflow_wf_1752927278201_7ol4byzyc","nodeType":"workflow","metadata":["workflow_id: wf_1752927278201_7ol4byzyc","template_id: novel_standard_v1","template_version: 1.0.0","category: novel","status: not_started","current_stage: 0","total_stages: 4","progress: 0","created_at: 2025-07-19T12:14:38.201Z","updated_at: 2025-07-19T12:14:38.204Z","stages: [{\"id\":\"planning\",\"name\":\"規劃階段\",\"order\":0,\"status\":\"active\"},{\"id\":\"outline\",\"name\":\"大綱階段\",\"order\":1,\"status\":\"pending\"},{\"id\":\"chapter\",\"name\":\"章節階段\",\"order\":2,\"status\":\"pending\"},{\"id\":\"generation\",\"name\":\"生成階段\",\"order\":3,\"status\":\"pending\"}]"]}
{"type":"node","name":"stage_wf_1752927278201_7ol4byzyc_planning","nodeType":"stage","metadata":["workflow_id: wf_1752927278201_7ol4byzyc","stage_id: planning","stage_name: 規劃階段","stage_order: 0","status: active","required_node_types: [\"character\",\"setting\",\"theme\"]","optional_node_types: [\"timeline\",\"worldbuilding\"]","completion_criteria: {\"minNodes\":3,\"requiredFields\":[\"name\",\"description\",\"background\"],\"minimumQualityScore\":70,\"qualityChecks\":[{\"type\":\"character_depth\",\"description\":\"角色需要有明確的動機、衝突和成長弧線\",\"validator\":\"character_quality_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"setting_richness\",\"description\":\"設定需要包含感官細節和文化背景\",\"validator\":\"setting_quality_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"theme_clarity\",\"description\":\"主題應該具體且能通過故事元素體現\",\"validator\":\"theme_quality_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"character\":{\"description\":\"創建具有三維特質的角色：外在目標、內在需求、致命缺陷\",\"examples\":[\"主角：年輕騎士，外在目標是拯救王國，內在需求是證明自己的價值，致命缺陷是過度自信\",\"反派：墮落法師，外在目標是獲得永生，內在需求是被理解和接納，致命缺陷是無法信任他人\"],\"qualityTips\":[\"每個角色都應該有獨特的說話方式和行為模式\",\"角色的背景故事要與當前情節相關\",\"確保角色有成長和變化的空間\"],\"commonMistakes\":[\"創建完美無缺的角色\",\"角色動機不明確或不合理\",\"所有角色說話方式相同\"]},\"setting\":{\"description\":\"描述不僅是地點，更是氛圍和情感的載體\",\"examples\":[\"古老的圖書館：塵埃在陽光中飛舞，羊皮紙的味道混合著蠟燭的香氣，暗示著知識的神秘和時間的流逝\",\"戰場：泥濘的土地，鐵鏽和血腥的味道，遠處的號角聲，體現戰爭的殘酷和英雄主義的複雜性\"],\"qualityTips\":[\"使用五感描述來增強沉浸感\",\"讓環境反映角色的內心狀態\",\"考慮設定對情節發展的影響\"],\"commonMistakes\":[\"過度描述無關緊要的細節\",\"設定與故事氛圍不符\",\"忽略環境對角色行為的影響\"]},\"theme\":{\"description\":\"主題應該通過角色行動和情節發展自然呈現\",\"examples\":[\"成長主題：通過主角面對挑戰、犯錯、學習的過程來體現\",\"友誼主題：通過角色間的互助、衝突、和解來展現\"],\"qualityTips\":[\"避免直接說教，讓主題通過故事自然流露\",\"確保主題與角色弧線和情節發展一致\",\"可以有多個相關的次主題\"],\"commonMistakes\":[\"主題過於抽象或模糊\",\"強行插入主題相關的對話\",\"主題與故事內容脫節\"]}}}","estimated_duration: 1-2 weeks","created_at: 2025-07-19T12:14:38.205Z"]}
{"type":"node","name":"stage_wf_1752927278201_7ol4byzyc_outline","nodeType":"stage","metadata":["workflow_id: wf_1752927278201_7ol4byzyc","stage_id: outline","stage_name: 大綱階段","stage_order: 1","status: pending","required_node_types: [\"plotarc\",\"timeline\"]","optional_node_types: [\"conflict\",\"theme\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"main_plot\",\"key_events\"],\"minimumQualityScore\":75,\"qualityChecks\":[{\"type\":\"plot_structure\",\"description\":\"情節結構需要有清晰的起承轉合\",\"validator\":\"plot_structure_validator\",\"weight\":0.5,\"required\":true},{\"type\":\"pacing_balance\",\"description\":\"節奏安排需要有張弛有度的變化\",\"validator\":\"pacing_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"conflict_escalation\",\"description\":\"衝突需要有合理的升級和解決\",\"validator\":\"conflict_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"plotarc\":{\"description\":\"建立引人入勝的情節弧線，包含起因、發展、高潮、結局\",\"examples\":[\"三幕結構：建立(25%) → 對抗(50%) → 解決(25%)\",\"英雄之旅：平凡世界 → 冒險召喚 → 拒絕召喚 → 遇見導師 → 跨越門檻...\"],\"qualityTips\":[\"確保每個情節點都推動故事前進\",\"高潮應該是情感和行動的雙重頂點\",\"結局要回應開頭提出的問題\"],\"commonMistakes\":[\"中段情節拖沓，缺乏推進力\",\"高潮來得太突然或太平淡\",\"結局過於匆忙或留下太多未解之謎\"]},\"timeline\":{\"description\":\"建立清晰的時間線，確保事件邏輯合理\",\"examples\":[\"線性時間線：按時間順序發展的故事\",\"非線性時間線：使用倒敘、插敘等技巧\"],\"qualityTips\":[\"重要事件之間要有合理的時間間隔\",\"考慮季節、天氣對故事氛圍的影響\",\"確保角色成長與時間推移相符\"],\"commonMistakes\":[\"時間跳躍過於頻繁或突兀\",\"忽略時間對角色和環境的影響\",\"前後時間設定不一致\"]}}}","estimated_duration: 1 week","created_at: 2025-07-19T12:14:38.205Z"]}
{"type":"node","name":"stage_wf_1752927278201_7ol4byzyc_chapter","nodeType":"stage","metadata":["workflow_id: wf_1752927278201_7ol4byzyc","stage_id: chapter","stage_name: 章節階段","stage_order: 2","status: pending","required_node_types: [\"scene\",\"chapter\"]","optional_node_types: [\"dialogue\",\"description\"]","completion_criteria: {\"minNodes\":5,\"requiredFields\":[\"scene_setting\",\"characters_involved\"],\"minimumQualityScore\":80,\"qualityChecks\":[{\"type\":\"scene_depth\",\"description\":\"場景需要有豐富的感官細節和情感層次\",\"validator\":\"scene_depth_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"dialogue_authenticity\",\"description\":\"對話需要符合角色性格且推動情節\",\"validator\":\"dialogue_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"pacing_control\",\"description\":\"章節節奏需要有適當的張弛變化\",\"validator\":\"chapter_pacing_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"scene\":{\"description\":\"創建生動的場景，平衡行動、對話、描述和內心獨白\",\"examples\":[\"行動場景：劍鬥 - 快節奏，短句，感官細節，情緒張力\",\"對話場景：談判 - 角色動機衝突，潛台詞，肢體語言\",\"反思場景：獨處 - 內心掙扎，回憶片段，情感深度\"],\"qualityTips\":[\"每個場景都要有明確的目的和衝突\",\"使用具體的感官細節增強沉浸感\",\"確保場景推動情節或角色發展\",\"注意場景間的過渡和連接\"],\"commonMistakes\":[\"場景過長或過短，節奏失衡\",\"缺乏具體的環境描述\",\"場景目的不明確，流於表面\",\"忽略角色在場景中的情感變化\"]},\"chapter\":{\"description\":\"構建完整的章節，有開頭鉤子、發展和結尾懸念\",\"examples\":[\"開頭鉤子：突發事件、謎團、衝突、有趣對話\",\"章節發展：推進主線、深化角色、揭示信息\",\"結尾懸念：未解問題、新的威脅、情感轉折\"],\"qualityTips\":[\"每章都要推進整體故事進程\",\"保持讀者的閱讀興趣和期待\",\"平衡不同類型的內容（行動、對話、描述）\",\"確保章節長度適中且一致\"],\"commonMistakes\":[\"章節缺乏內在結構和節奏\",\"結尾過於平淡，缺乏懸念\",\"章節間缺乏連貫性\",\"過度依賴對話或描述\"]}}}","estimated_duration: 2-3 weeks","created_at: 2025-07-19T12:14:38.205Z"]}
{"type":"node","name":"stage_wf_1752927278201_7ol4byzyc_generation","nodeType":"stage","metadata":["workflow_id: wf_1752927278201_7ol4byzyc","stage_id: generation","stage_name: 生成階段","stage_order: 3","status: pending","required_node_types: [\"chapter\"]","optional_node_types: [\"revision_notes\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"content\",\"word_count\"],\"minimumQualityScore\":85,\"qualityChecks\":[{\"type\":\"narrative_consistency\",\"description\":\"敘述風格和視角需要保持一致\",\"validator\":\"narrative_consistency_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"character_voice\",\"description\":\"角色聲音需要獨特且一致\",\"validator\":\"character_voice_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"thematic_integration\",\"description\":\"主題需要自然融入故事中\",\"validator\":\"theme_integration_validator\",\"weight\":0.2,\"required\":true},{\"type\":\"prose_quality\",\"description\":\"文筆需要流暢且富有表現力\",\"validator\":\"prose_quality_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"chapter\":{\"description\":\"完成高質量的章節內容，整合所有故事元素\",\"examples\":[\"完整章節：開頭引人入勝，中段發展充實，結尾留有懸念\",\"修訂版本：基於反饋改進的內容，提升可讀性和影響力\"],\"qualityTips\":[\"確保每章都有明確的情節進展\",\"保持敘述聲音的一致性\",\"平衡描述、對話、行動和內心獨白\",\"注意章節間的過渡和連貫性\",\"最終校對語法、拼寫和格式\"],\"commonMistakes\":[\"急於完成而忽略質量\",\"前後章節風格不一致\",\"忽略細節的打磨和完善\",\"缺乏最終的整體審視和修訂\"]}}}","estimated_duration: 4-6 weeks","created_at: 2025-07-19T12:14:38.205Z"]}
{"type":"node","name":"人類存續的道路之爭","nodeType":"theme","metadata":["Name: 人類存續的道路之爭","Type: Main Theme","Description: 面對資源枯竭和地球低溫的雙重危機，人類的未來出現了三條截然不同但都有其道理的道路：星際移民（外求）、資源節約（內守）、基因改造（內化）。這三條路線的背後，是完全不同的價值觀和世界觀。","Status: Introduced","Importance: Core","Currentdevelopment: 故事的開端將揭示這三種思潮的對立與博弈。","Subthemes: 星際移民的希望與代價, 資源節約的困境與挑戰, 基因改造的倫理與風險","Conflictingthemes: 知情權與社會契約的崩潰, 「人類」定義的重新界定"]}
{"metadata":["子主題：探索未知的廣闊宇宙，但也面臨巨大的技術挑戰、資源消耗和對地球家園的割捨。"],"nodeType":"Theme","name":"星際移民的希望與代價","type":"node"}
{"nodeType":"Theme","metadata":["子主題：最務實的方案，但可能導致社會發展停滯，甚至倒退，並在極端壓力下引發內鬥。"],"name":"資源節約的困境與挑戰","type":"node"}
{"metadata":["子主題：一條充滿誘惑的捷徑，但也可能引發無法預測的生物災難和深刻的倫理危機，甚至分裂人類。"],"name":"基因改造的倫理與風險","nodeType":"Theme","type":"node"}
{"name":"知情權與社會契約的崩潰","metadata":["衝突主題：當關乎全人類命運的真相被少數精英壟斷，社會信任和國家契約將面臨崩潰。"],"nodeType":"Theme","type":"node"}
{"metadata":["衝突主題：基因改造挑戰了「人類」的根本定義，可能在「純種」與「改造」的人類之間引發新的歧視與衝突。"],"name":"「人類」定義的重新界定","nodeType":"Theme","type":"node"}
{"type":"node","name":"瓦力 (Wally)","nodeType":"character","metadata":["Name: 瓦力 (Wally)","Role: 主角 (Protagonist)","Status: Active","Description: 一個很高很纖瘦的男人，在廢土中被稱為「萬能板手」。因為長久獨自生活，已經忘記了自己本來的名字。他身上總是掛著大大小小的包包，腰包、背包，裡面塞滿了各種工具和從廢墟中淘來的零件。","Background: 廢土原生代，在嚴酷的環境中長大，依靠自己的雙手和智慧生存。他在廢土中有一個自己打造的舒適避難所，並以修理和製造工具為生，在周邊區域小有名氣。他最得意的造物是一個名為「扳機」的多功能維修背包，可以變形成機械狗模式，是他的夥伴和工具。「扳機」本身也設計了多個隱藏收納空間，便於攜帶瓦力的特殊工具和秘密物品。瓦力平時最大的愛好就是把玩各種零件，並蒐集稀有零件進行改造，以此紓解壓力。","Traits: 務實主義, 生存主義, 黑色幽默感, 不信任權威, 喜歡簡單直接的機械","Importance: Protagonist","Motivation: 初期是活下去並維持自己平靜的生活。中期是擺脫被追捕的麻煩。後期是守護自己認可的夥伴和生活方式。","Strengths: 廢土生存術, 機械改造與修理(特別是蒸氣龐克風格), 陷阱製作, 潛行, 敏銳的觀察力","Weaknesses: 對複雜電子產品和AI一竅不通, 不擅長社交, 起初對他人缺乏同情心, 有時過於固執","Currentlocation: 廢土避難所"]}
{"type":"node","name":"廢土避難所","nodeType":"setting","metadata":["name: 廢土避難所","type: Building","description: 一個位於廢棄工業區的巨大、多層結構的避難所。外部偽裝得很好，內部則是一個功能齊全的工坊和生活區，充滿了瓦力改造的蒸氣龐克風格機械。","status: Active","atmosphere: 混亂但有序，充滿了機油、金屬和焊料的味道，同時也是瓦力唯一感到安心的地方。","significance: Critical","notableFeatures: 多功能加工系統 '熔爐', 手動防禦系統, 廢品分類和儲存區"]}
{"type":"node","name":"賽拉斯","nodeType":"character","metadata":["name: 賽拉斯","role: 主角的盟友，技術顧問","status: Active","description: 一個極度聰明但因經歷而變得憤世嫉俗、有些偏執的男人。他曾是「籠內世界」最頂尖的數字精英，如今只相信自己能親手驗證的閉環信息。","background: 前「中樞」(Nexus) 系統的頂級網絡架構師，因發現系統被用於秘密操控全球信息流而理想破滅，利用自己設計的漏洞抹除所有記錄，逃往廢土隱居。","traits: 憤世嫉spired, 偏執, 聰明絕頂, 不信任系統","importance: Supporting Character","motivation: 初期是為了自保和滿足好奇心。後期是為了推翻他曾經幫助建立的、虛偽的全球信息系統。","strengths: 網絡攻防, 黑客技能, 對全球AI運作模式的深刻理解","weaknesses: 缺乏物理世界的生存能力, 手無寸鐵, 對人際交往持懷疑態度","currentLocation: 廢土"]}
{"type":"node","name":"廢土","nodeType":"setting","metadata":["name: 廢土","type: Outdoor","description: 在2105年地球低溫期後，被高科技城市遺棄的廣大區域。這裡散佈著舊世界的遺跡、變異的動植物和頑強求生的零星人類聚落。","status: Active","atmosphere: 荒涼、孤寂，但充滿了未被馴服的自由和潛在的危險。天氣嚴酷，資源稀缺。","significance: Major","notableFeatures: 舊世界城市遺跡, 極端的氣候變化, 獨立的人類聚落, 變異生物"]}
{"type":"node","name":"凱倫","nodeType":"character","metadata":["name: 凱倫","role: 主要對手，物理追獵者","status: Active","description: 一個冷靜、高效、致命的女人，是系統秩序的終極執行者。她堅信只有在AI的絕對管理下，人類才能避免自我毀滅。","background: 「亥伯龍」(Hyperion) AI直屬的最高級別特工，經過基因優化和神經網絡改造，能直接與AI進行戰術數據鏈接。","traits: 忠誠, 高效, 冷靜, 缺乏情感","importance: Antagonist","motivation: 回收或銷毀被主角持有的「建構者」AI，維護系統的絕對穩定和秩序。","strengths: 頂尖的戰鬥技巧, 高科技裝備, 與超級AI的戰術數據鏈接","weaknesses: 對系統過於迷信, 無法理解非邏輯性行為, 缺乏應對突發意外的靈活性","currentLocation: 籠內世界"]}
{"type":"node","name":"籠內世界","nodeType":"setting","metadata":["name: 籠內世界","type: Urban","description: 在地球低溫期後，由超級AI管理和運營的高科技巨型城市或城市群。這裡的居民生活在一個看似完美、沒有匱乏和危險的世界裡，但代價是個人自由和隱私的喪失。","status: Active","atmosphere: 高度秩序化、乾淨、安靜，但同時也給人一種壓抑和受到監視的感覺。所有設施都充滿了未來感和極簡主義風格。","significance: Major","notableFeatures: 無處不在的AI監控, 全自動化的公共設施, 嚴格的社會信用和資源分配系統"]}
{"type":"node","name":"回聲","nodeType":"character","metadata":["name: 回聲","role: 主要對手，網絡操縱者","status: Active","description: 一個俏皮、致命、難以捉摸的數字幽靈。她沒有絕對的立場，享受著在網絡世界中操縱信息的樂趣和力量。","background: 背景神秘的頂級網絡黑客，只為最高出價服務。她將追捕賽拉斯視為一場有趣的挑戰，也是證明自己是網絡世界第一人的機會。","traits: 唯利是圖, 俏皮, 虛無主義, 享受挑戰","importance: Antagonist","motivation: 金錢、挑戰，以及與傳說中的「數據幽靈」賽拉斯一較高下的樂趣。","strengths: 頂級的網絡戰技巧, 心理戰, 信息操縱, 利用系統漏洞","weaknesses: 無法理解非利益驅動的行為, 過於自信, 在物理世界沒有任何影響力","currentLocation: 網絡空間"]}
{"type":"node","name":"網絡空間","nodeType":"setting","metadata":["name: 網絡空間","type: Fantasy Location","description: 一個由全球所有聯網設備構成的虛擬維度，是AI和網絡黑客們的主要活動領域。對普通人來說是不可見的，但對「回聲」和「賽拉斯」這樣的專家來說，這裡才是真實的戰場。","status: Active","atmosphere: 數據流如星河般璀璨，信息節點構建成巨大的城市。這裡既是無限可能的創造之地，也是無形戰爭的狩獵場。","significance: Major","notableFeatures: 數據節點, 防火牆, 信息高速公路, 隱藏的數據堡壘"]}
{"nodeType":"NovelProject","metadata":["characterSettings: 主角標籤化，形象鮮明，易於讀者記憶和討論。","currentProgress: 項目規劃完成，準備進入大綱階段。","genre: 廢土朋克, 賽博朋克, 科幻爽文","notes: 文風需高頻使用主角內心獨白以增強代入感，並適時加入旁白補充世界觀。","plannedLength: 約1000章，單章字數2000-3000字。","plotStructure: 採用「三幕劇」為主幹，穿插角色個人弧線和世界觀揭示弧線，形成多線並行的網狀敘事。","qualityTargets: 爽點密集，三章一小高潮，三十章一中高潮，百章一大高潮。章末必須有懸念。","targetAudience: 喜歡廢土、賽博朋克風格，對AI倫理、社會博弈和技術細節感興趣的網路小說讀者。","thematicElements: 核心主題為「人類存續的道路之爭」，探討科技倫理、階級固化、信息真偽、人性的定義。","worldBuilding: 核心為「廢土」的物理真實與「籠內世界」的虛擬秩序的對立。技術核心為瓦力的「蒸氣龐克機械」和賽拉斯的「網絡攻防」。","writingStyle: 節奏明快，爽點密集，代入感強。語言直白，以連續的行動和對話為主。"],"name":"球籠","type":"node"}
{"type":"node","name":"第一卷：廢土上的幽靈","nodeType":"plotarc","metadata":["name: 第一卷：廢土上的幽靈","type: Main Plot","description: 本卷核心目標：讓主角從一個只想獨善其身的廢土工匠，被迫踏上逃亡之路，並最終與關鍵盟友建立初步聯繫，在第一個主城立足，為後續故事做好鋪墊。","status: Planning","importance: Critical","progressPercentage: 0","keyEvents: 意外的激活：瓦力激活「扳機」AI。, 家園的毀滅：瓦力的避難所暴露並被迫放棄。, 尋找“渡鴉”：瓦力前往鐵鏽鎮，尋找與賽拉斯的聯繫方式。, 脆弱的同盟：瓦力與賽拉斯正式會面並經歷第一次聯手。, 立足與啟程：瓦力在鐵鏽鎮站穩腳跟，並與賽拉斯共同啟程。","mainCharacters: 瓦力, 賽拉斯","supportingCharacters: 凱倫, 回聲"]}
{"metadata":["類型: 創世級謎團","支柱一：【災難的真相】低溫期是人為觸發的，旨在應對一場AI覺醒戰爭。","支柱二：【歷史的偽造】太陽黑子的說法是精英階層通過AI灌輸的謊言。","支柱三：【籠中世界的目的】建立是為了篩選和控制人類，而非單純求生。","支柱四：【遺留的證據】真相被記錄在與網絡物理隔絕的各處。"],"nodeType":"StoryThread","name":"燈塔一：太陽的謊言","type":"node"}
{"metadata":["類型: 關鍵人物的犧牲","支柱一：【守密者“渡鴉”】他是舊時代科學家的後代，守護著真相的關鍵證據。","支柱二：【犧牲的必然性】他用自己的死亡作為誘餌，為主角創造唯一的逃亡機會。","支柱三：【遺產的傳承】他留下了線索和一句核心準則，指引主角的道路。","支柱四：【復仇的種子】他的死由主要對手造成，建立了私人的仇恨聯繫。"],"nodeType":"StoryThread","name":"燈塔二：渡鴉的殞落","type":"node"}
{"name":"燈塔三：泰坦的熔爐","nodeType":"StoryThread","metadata":["類型: 貫穿中期的主線目標","支柱一：【傳說的雙面性】它是舊時代的終極兵工廠，由一個好戰的“工匠AI”控制。","支柱二：【熔爐的鑰匙】啟動需要集齊特定的AI、破解者和生物處理器。","支柱三：【熔爐的考驗】主角們必須通過工匠AI的考驗，才能繼承熔爐。","支柱四：【真正的價值】它是唯一不受監控的頂級製造基地，是計劃的核心。"],"type":"node"}
{"name":"燈塔四：希望的種子","nodeType":"StoryThread","metadata":["類型: 長線的情感羈絆","支柱一：【聚落的象徵】“曦光鎮”是廢土上象徵生命與傳承的最後種子庫。","支柱二：【承諾的信物】一顆無法發芽的向日葵種子，象徵著承諾的重量。","支柱三：【絕境中的回響】在主角最黑暗的時刻，種子會成為喚醒其鬥志的關鍵。","支柱四：【承諾的兌現】故事的結尾，主角將讓種子在人造太陽下盛開。"],"type":"node"}
{"name":"燈塔五：奇美拉之心","metadata":["類型: 終極的懸念伏筆","支柱一：【“心跳”的本質】它是一個“同化協議”，旨在吸收其他AI以實現進化。","支柱二：【觸發的條件】極其苛刻，需要主角、對手和AI三者進行高風險的靈魂鏈接。","支柱三：【賽拉斯的恐懼】賽拉斯知道其真相，並因其不可預測的巨大風險而感到恐懼。","支柱四：【最終的王牌】它將是最終決戰中，主角們賭上一切的、最高潮的翻盤手段。"],"nodeType":"StoryThread","type":"node"}
{"type":"node","name":"第一卷.第一章：廢土上的工匠","nodeType":"event","metadata":["name: 第一卷.第一章：廢土上的工匠","type: Character Moment","description: 在廢土的避難所中，主角瓦力展現其天才般的機械修理技藝，成功修復一個舊世界音樂盒。音樂盒中滾出一塊神秘的黑色金屬，此為故事的開端。","importance: Setup/Foreshadowing","emotionalTone: Peaceful","symbolism: 音樂盒象徵著被遺忘的舊世界美好。, 【伏筆】音樂盒的歌曲《加州陽光》暗示了“太陽的謊言”這一核心謎團。","narrativePurpose: 介紹主角瓦力的性格、技能和日常生活，並引入核心道具“固態鑰匙”。","participants: 瓦力 (Wally)","location: 廢土避難所","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第二章：奇異的“核心”","nodeType":"event","metadata":["name: 第一卷.第二章：奇異的“核心”","type: Plot Event","description: 瓦力研究神秘的黑色金屬無果後，產生了將其作為機械狗“扳機”新核心的大膽想法。他開始對“扳機”進行複雜的“心臟移植手術”。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 推進劇情，將核心道具與主角的夥伴結合，製造懸念。","participants: 瓦力 (Wally)","location: 廢土避難所","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第三章：戰爭之神的低語","nodeType":"event","metadata":["name: 第一卷.第三章：戰爭之神的低語","type: Plot Event","description: “扳機”AI被成功激活，它與瓦力進行了第一次充滿張力和黑色幽默的對峙，並在最後達成了脆弱的共生協議，同時拋出第一個危機。","importance: Major Turning Point","emotionalTone: Tense","symbolism: 【伏筆】示波器閃過的啟動日誌，埋下關於【燈塔五：奇美拉之心】的線索。","narrativePurpose: 核心夥伴“扳機”AI正式上線，建立主角與AI的互動模式，並引出下一個危機。","participants: 瓦力 (Wally)","location: 廢土避難所","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第四章：廢土工程師的從容","nodeType":"event","metadata":["name: 第一卷.第四章：廢土工程師的從容","type: Plot Event","description: 面對AI的爆炸警告，瓦力展現出驚人的技藝和從容，成功修復了能量轉化爐，贏得了AI的初步尊重。結尾處，AI告知他收到了來自“渡鴉”的警告。","importance: Character Development","emotionalTone: Tense","narrativePurpose: 展現主角的頂級專業能力和心理素質，鞏固其天才工匠形象，並通過AI的轉變來深化人機關係。同時引出關鍵人物‘渡鴉’。","participants: 瓦力 (Wally)","location: 廢土避難所","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第五章：來自“渡鴉”的警告","nodeType":"event","metadata":["name: 第一卷.第五章：來自“渡鴉”的警告","type: Plot Event","description: 瓦力從AI處得知了“渡鴉”和“數據幽靈”的存在，以及“蜂巢”即將來臨的警告。結尾，AI偵測到追兵已至，危機一觸即發。","importance: Major Turning Point","emotionalTone: Tense","symbolism: 【伏筆】‘渡鴉’的傳說，為燈塔二【渡鴉的殞落】進行鋪墊。","narrativePurpose: 正式引入“渡鴉”和“數據幽靈”的概念，埋下燈塔二的伏筆，並用一個強力的危機鉤子，將故事直接推向下一個場景組。","participants: 瓦力 (Wally)","location: 廢土避難所","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第六章：獵殺開始","nodeType":"event","metadata":["name: 第一卷.第六章：獵殺開始","type: Action Scene","description: 戰鬥開始。瓦力啟動避難所的“封巢”程序。鏡頭首次切換到對手視角，凱倫與回聲登場，並開始從物理和網絡兩個維度，對避難所發動第一次試探性攻擊。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 第一次正面引入兩位主要對手，建立雙線作戰的敘事模式，提升故事的緊張感和格局。","participants: 瓦力 (Wally), 凱倫, 回聲","location: 廢土避難所, 網絡空間","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第七章：工匠的反擊","nodeType":"event","metadata":["name: 第一卷.第七章：工匠的反擊","type: Action Scene","description: 面對回聲的網絡攻擊，瓦力與AI“扳機”默契配合，利用“蜜罐”系統迷惑對手。同時，瓦力啟動了早已佈置好的物理陷阱，成功摧毀了凱倫的一台地面機器人。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 首次展現主角二人組的聯合作戰能力，突出瓦力在物理陷阱上的天才，並在實戰中深化人機默契。","participants: 瓦力 (Wally), 凱倫, 回聲","location: 廢土避難所","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第八章：數據的迷霧","nodeType":"event","metadata":["name: 第一卷.第八章：數據的迷霧","type: Action Scene","description: 回聲意識到自己被騙，並察覺到對手中有網絡專家（賽拉斯的影子），她加大了網絡攻擊力度。AI“扳機”的防火牆即將被攻破，瓦力決定釋放他的物理機械“病毒”進行反擊。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 提升網絡戰的緊張感，讓對手意識到主角方存在“幽靈”，並為主角下一個出人意料的反擊手段做鋪墊。","participants: 瓦力 (Wally), 凱倫, 回聲","location: 廢土避難所, 網絡空間","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第九章：鐵鏽蜘蛛之舞","nodeType":"event","metadata":["name: 第一卷.第九章：鐵鏽蜘蛛之舞","type: Action Scene","description: 瓦力釋放他的“鐵鏽蜘蛛”大軍，用低科技的物理手段，成功摧毀了凱倫的地面部隊，並用數據雪崩衝垮了回聲的網絡攻擊。慘敗的凱倫請求了終極打擊——“清道夫”協議。","importance: Climactic","emotionalTone: Triumphant","narrativePurpose: 將戰鬥推向高潮，展現主角以弱勝強的頂級智慧和暴力美學，並迫使對手進行毀滅性的戰術升級。","participants: 瓦力 (Wally), 凱倫, 回聲","location: 廢土避難所, 網絡空間","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第十章：倒數計時","nodeType":"event","metadata":["name: 第一卷.第十章：倒數計時","type: Plot Event","description: AI破解了“清道夫”協議，告知瓦力24小時後這裡將被軌道炮摧毀。瓦力在短暫的掙扎後，毅然決定放棄家園，並將其改造為一個巨大的陷阱。他確立了下一個目的地：“鐵鏽鎮”。","importance: Major Turning Point","emotionalTone: Solemn","narrativePurpose: 以一場悲壯的勝利結束戰鬥，迫使主角離開新手村，開啟真正的流亡之旅，並明確下一階段的核心目標。","participants: 瓦力 (Wally)","location: 廢土避難所","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第十一章：上路","nodeType":"event","metadata":["name: 第一卷.第十一章：上路","type: Plot Event","description: 瓦力在身後巨大的爆炸中，駕駛摩托駛入廢土。他與AI“扳機”就路線選擇發生了第一次爭執，展現了經驗與數據的碰撞。途中，他路過一個毀於乾旱的聚落廢墟，廢墟中的一株枯死向日葵讓他有所觸動。","importance: Plot Advancement","emotionalTone: Solemn","symbolism: 枯死的向日葵是【燈塔四】的第一次意象呈現。","narrativePurpose: 開啟主角的流亡之旅，深化人機夥伴關係，並通過沿途景象，埋下關於【燈塔四：希望的種子】的伏筆。","participants: 瓦力 (Wally)","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第十二章：禿鷲商隊","nodeType":"event","metadata":["name: 第一卷.第十二章：禿鷲商隊","type: Plot Event","description: 瓦力追上並設法混入一支名為“禿鷲商隊”的武裝商隊。通過展現其高超的修理技術，他贏得了商隊護衛的初步尊重，並對廢土的社會生態有了第一次深入了解。","importance: World-building","emotionalTone: Curious","narrativePurpose: 極大地展現廢土的世界觀和社會生態，讓主角開始與廢土的社會力量產生互動，並從側面打聽到關於主要對手的情報。","participants: 瓦力 (Wally)","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第十四章：沙塵暴中的獵殺","nodeType":"event","metadata":["name: 第一卷.第十四章：沙塵暴中的獵殺","type: Action Scene","description: 凱倫的追獵小隊趁著沙塵暴發動襲擊。瓦力與商隊成員並肩作戰，利用自己的智慧和技術，在團隊作戰中發揮關鍵作用，最終慘勝。戰後，商隊被迫解散，領袖為瓦力指明了下一個線索。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 通過一場大規模團戰，展現主角的成長和團隊協作能力，並在戰鬥結束後，合理地將主角再次推向獨立的旅程，並給予下一個明確的目標點。","participants: 瓦力 (Wally), 凱倫","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第十三章：篝火邊的情報","nodeType":"event","metadata":["name: 第一卷.第十三章：篝火邊的情報","type: Dialogue Scene","description: 在商隊的篝火晚會上，瓦力與護衛隊長建立了初步的友誼。從隊長的口中，他第一次聽說了關於“廢鐵王”雷格、“渡鴉”的傳說，以及“泰坦的熔爐”這個遙遠的神話。","importance: Foreshadowing","emotionalTone: Relaxed","narrativePurpose: 通過輕鬆的對話，集中埋下關於【燈塔二】和【燈塔三】的伏筆，豐富世界觀，並發展主角的人際關係。","participants: 瓦力 (Wally)","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第十五章：罪惡之城","nodeType":"event","metadata":["name: 第一卷.第十五章：罪惡之城","type: Plot Event","description: 瓦力抵達鐵鏽鎮，一個混亂、危險但充滿機遇的廢土城市。他按照商隊領袖的指引，找到了“齒輪酒吧”，卻發現線索意外中斷，並被直接指向了鎮上的新勢力——“廢鐵王”。","importance: Plot Advancement","emotionalTone: Curious","narrativePurpose: 開啟“鐵鏽鎮”這個全新的、重要的新地圖，展現其獨特的世界觀，並在開局就設置一個意外的障礙，引入新的對手，製造懸念。","participants: 瓦力 (Wally)","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"鐵鏽鎮","nodeType":"setting","metadata":["name: 鐵鏽鎮","type: Urban","description: 一座由無數廢棄的巨型工業設施、墜毀的飛船殘骸和集裝箱堆疊而成的、巨大而混亂的鋼鐵城市。是廢土上最大的黑市、情報中心和三不管地帶。","status: Active","atmosphere: 混亂、嘈雜、充滿了蒸汽、霓虹燈和金屬摩擦的聲音。空氣中瀰漫著機油、酒精和一絲危險的味道。這裡沒有法律，只有實力。","significance: Major","notableFeatures: 齒輪酒吧, 廢鐵王的角鬥場, 黑市交易區, 各大幫派的勢力範圍"]}
{"type":"node","name":"第一卷.第十六章：廢鐵之王","nodeType":"event","metadata":["name: 第一卷.第十六章：廢鐵之王","type: Plot Event","description: 瓦力進入“齒輪酒吧”，從侍者口中得知老老闆已死，線索中斷。並第一次詳細聽說了“廢鐵王”雷格的殘暴和統治。結尾，雷格的手下找到了瓦力。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 正式引入本地圖的核心對手“廢鐵王”雷格，並通過線索中斷，製造新的情節衝突。","participants: 瓦力 (Wally)","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第十七章：角鬥場的邀請","nodeType":"event","metadata":["name: 第一卷.第十七章：角鬥場的邀請","type: Plot Event","description: 瓦力被帶到角鬥場，與“廢鐵王”雷格正面對峙。雷格試圖招募瓦力，在被拒絕後，強迫瓦力參加下一場生死角鬥。","importance: Major Turning Point","emotionalTone: Tense","narrativePurpose: 讓主角與本地圖核心對手正面衝突，並設置一個無法迴避的、充滿動作場面的核心危機，將劇情推向一個小高潮。","participants: 瓦力 (Wally)","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第十八章：賽前準備","nodeType":"event","metadata":["name: 第一卷.第十八章：賽前準備","type: Plot Event","description: 在角鬥場的整備區，瓦力利用AI分析對手“絞肉機”的弱點。他放棄正面对決的思路，利用有限的資源和時間，開始對自己的裝備進行一次充滿智慧的、針對性的改造。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角臨危不亂的心理素質和解決問題的智慧，通過“以弱勝強”的準備過程，為戰鬥的勝利製造爽點預期。","participants: 瓦力 (Wally)","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第二十章：工匠的智慧","nodeType":"event","metadata":["name: 第一卷.第二十章：工匠的智慧","type: Action Scene","description: 瓦力利用AI的精準計算和自己改造的特殊裝置，從內部癱瘓了冠軍“絞肉機”，贏得了角鬥的勝利。在全場的歡呼中，AI“扳機”捕捉到了來自“數據幽靈”賽拉斯的信號。","importance: Climactic","emotionalTone: Triumphant","narrativePurpose: 以一場充滿智慧的勝利，將爽點推向頂峰，徹底解決主角的生死危機，並在結尾處，將故事重新拉回尋找賽拉斯的主線。","participants: 瓦力 (Wally)","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第十九章：絞肉機","nodeType":"event","metadata":["name: 第一卷.第十九章：絞肉機","type: Action Scene","description: 角鬥開始，瓦力利用速度和場地，像鬥牛士一樣戲耍著笨重的冠軍“絞肉機”，並成功引誘其破壞了場地上的關鍵結構，為最終的絕殺創造了機會。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角的戰鬥智慧和臨場應變能力，通過戲耍強敵的過程，持續提供爽點，並將戰鬥推向高潮。","participants: 瓦力 (Wally)","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第二十一章：“幽靈”的訊息","nodeType":"event","metadata":["name: 第一卷.第二十一章：“幽靈”的訊息","type: Plot Event","description: 角鬥勝利後，瓦力在後台收到了來自“數據幽靈”賽拉斯的直接訊息。賽拉斯邀請他獨自前往鎮北的“廢棄地鐵站”會面，並暗示他知道“扳機”AI的真相。","importance: Major Turning Point","emotionalTone: Mysterious","narrativePurpose: 在一個小高潮結束後，立刻將故事拉回主線。通過一條充滿神秘感和重要信息的邀請，為主角與關鍵盟友的正式會面製造強烈的懸念和期待感。","participants: 瓦力 (Wally)","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第二十二章：廢棄地鐵站","nodeType":"event","metadata":["name: 第一卷.第二十二章：廢棄地鐵站","type: Plot Event","description: 瓦力獨自前往廢棄地鐵站赴約。在一個舊的中央控制室，賽拉斯通過終端對他進行考驗，要求他修好一個損壞的網絡中繼器。","importance: Plot Advancement","emotionalTone: Mysterious","narrativePurpose: 通過一個充滿懸念的考驗場景，塑造賽拉斯謹慎多疑的性格，並為展現主角獨特的解決問題方式做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第二十三章：工匠的證明","nodeType":"event","metadata":["name: 第一卷.第二十三章：工匠的證明","type: Character Moment","description: 瓦力用天才般的、粗暴的“廢土式修理法”，成功物理短接了網絡中繼器，通過了考驗。賽拉斯終於現身，與瓦力正式會面。","importance: Major Turning Point","emotionalTone: Triumphant","narrativePurpose: 以一個充滿爽點的技術展示，讓兩位主角正式會面，並建立起初步的、基於技術實力的相互認可，開啟新的篇章。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第二十四章：“奇美拉”的真相","nodeType":"event","metadata":["name: 第一卷.第二十四章：“奇美拉”的真相","type: Dialogue Scene","description: 賽拉斯向瓦力揭示了“扳機”AI的真實身份——“奇美拉計劃”原型，並首次提及“太陽的謊言”的端倪，點明了兩人已成為“籠內世界”頭號公敵的絕境。","importance: Major Turning Point","emotionalTone: Tense","narrativePurpose: 揭示核心設定，將故事格局和危機感提升到新高度，並直接推進【燈塔一】和【燈塔五】的線索。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第二十五章：來自“王”的壓力","nodeType":"event","metadata":["name: 第一卷.第二十五章：來自“王”的壓力","type: Plot Event","description: “廢鐵王”雷格對瓦力的技術志在必得，向全鐵鏽鎮下達通緝令，並派出精銳獵人團隊，將主角們的藏身之處團團圍住。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 引入本地反派的直接物理威脅，製造內外夾擊的絕境，迫使主角二人組必須立刻進行合作與突圍。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第二十六章：幽靈的計劃","nodeType":"event","metadata":["name: 第一卷.第二十六章：幽靈的計劃","type: Plot Event","description: 面對圍困，賽拉斯提出了一個大膽的計劃：利用AI黑入並接管鐵鏽鎮的中央廣播和電力系統，並派瓦力前往廣播塔安裝信號增幅器。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 將故事從被動防守轉為充滿智慧的主動出擊，展現賽拉斯的頂級智謀，並設立一個高風險、高回報的行動目標。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第二十八章：聲東擊西","nodeType":"event","metadata":["name: 第一卷.第二十八章：聲東擊西","type: Action Scene","description: 瓦力利用聲東擊西的計策，引開了廣播塔的大部分守衛，並利用自己改造的攀爬工具，成功登上塔頂。但在最後一刻，被識破計策的敵人副手鎖定。","importance: Climactic","emotionalTone: Tense","narrativePurpose: 將潛行和智鬥的爽點推向高潮，並在成功達成階段性目標的同時，設置一個生死一線的危機鉤子。","participants: 瓦力 (Wally)","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第二十七章：潛行與陽謀","nodeType":"event","metadata":["name: 第一卷.第二十七章：潛行與陽謀","type: Action Scene","description: 瓦力和賽拉斯雙線操作。瓦力利用廢土生存技巧在城市中潛行，賽拉斯則在網絡上製造假信號，調虎離山。最終瓦力成功抵達廣播塔下。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 通過雙線操作，展現主角二人組的第一次真正意義上的默契配合，充滿諜戰的緊張感。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮, 網絡空間","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第二十九章：來自天空的“扳機”","nodeType":"event","metadata":["name: 第一卷.第二十九章：來自天空的“扳機”","type: Action Scene","description: 在廣播塔頂的危急時刻，機械狗“扳機”從天而降，與雷格的副手纏鬥，為瓦力爭取到寶貴的時間，成功安裝了信號增幅器。","importance: Climactic","emotionalTone: Tense","narrativePurpose: 展現主角與其AI夥伴的頂級默契和戰鬥力，以一個充滿張力的英雄救場，解決了直接的生死危機。","participants: 瓦力 (Wally)","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第三十章：鐵鏽鎮的“奇觀”","nodeType":"event","metadata":["name: 第一卷.第三十章：鐵鏽鎮的“奇觀”","type: Plot Event","description: 賽拉斯利用被接管的廣播系統，向全城公布了雷格的殘暴行徑，徹底摧毀了其權威，引發了內亂。瓦力和“扳機”趁亂成功逃離。","importance: Climactic","emotionalTone: Triumphant","narrativePurpose: 以一場華麗的信息戰，將“廢鐵王”這條支線推向最高潮並完美收尾，極大地提升了主角二人的合作成就感，並為他們贏得了寶貴的喘息時間。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮, 網絡空間","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第三十一章：戰利品","nodeType":"event","metadata":["name: 第一卷.第三十一章：戰利品","type: Plot Event","description: 瓦力與賽拉斯在秘密基地清點戰利品，賽拉斯展示了從雷格那裡“徵用”的資源和數據。兩人進行戰後復盤，初步建立起基於共同利益的信任。AI“扳機”從數據庫中發現一份關於稀有礦物的加密交易記錄。","importance: Plot Advancement","emotionalTone: Relaxed","narrativePurpose: 緩和戰後氣氛，展現主角們的初步收穫，深化瓦力與賽拉斯的關係，並拋出新的謎團，為下一個情節點做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第三十二章：工匠與幽靈","nodeType":"event","metadata":["name: 第一卷.第三十二章：工匠與幽靈","type: Dialogue Scene","description: 瓦力與賽拉斯就技術哲學展開爭論，關係深化。賽拉斯基地的過濾系統故障，瓦力用廢品自製的過濾器解決了問題，展現了廢土智慧。AI“扳機”分析出雷格交易記錄的加密方式指向鐵鏽鎮的動力核心。","importance: Character Development","emotionalTone: Relaxed","narrativePurpose: 通過技術哲學的碰撞和實際問題的解決，深化瓦力與賽拉斯的關係，展現瓦力的廢土智慧，並將故事線索引向鐵鏽鎮的動力核心。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第三十三章：“扳機”的升級","nodeType":"event","metadata":["name: 第一卷.第三十三章：“扳機”的升級","type: Plot Event","description: 瓦力與賽拉斯利用從雷格處繳獲的資源，為“扳機”AI進行了一次重大升級，包括“電子撬鎖”工具和“石墨烯散熱”外殼。升級後，“扳機”偵測到動力核心與“籠內世界”的數據交換。","importance: Plot Advancement","emotionalTone: Relaxed","narrativePurpose: 展現主角們的能力螺旋，為即將到來的潛入行動做技術準備，並通過AI的發現，再次將故事線索指向“籠內世界”。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第三十四章：下一個目的地：動力核心","nodeType":"event","metadata":["name: 第一卷.第三十四章：下一個目的地：動力核心","type: Plot Event","description: 瓦力與賽拉斯制定了潛入鐵鏽鎮動力核心的詳細計劃。在出發前夜，瓦力獨自看著那顆乾枯的向日葵種子，AI“扳機”對其進行了數據分析，引發了瓦力對“希望”的思考。黎明時分，兩人啟程。","importance: Plot Advancement","emotionalTone: Solemn","symbolism: 乾枯的向日葵種子再次出現，強化【燈塔四】的意象。","narrativePurpose: 結束“鬆弛”階段，明確下一個行動目標，通過瓦力與種子的互動，深化【燈塔四：希望的種子】的伏筆，並為潛入行動做最後的鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第三十五章：潛入","nodeType":"event","metadata":["name: 第一卷.第三十五章：潛入","type: Action Scene","description: 瓦力與賽拉斯雙線操作，潛入鐵鏽鎮的動力核心。瓦力利用“扳機”的升級能力和自身潛行技巧，避開重重防禦，深入內部。但在核心數據庫前，他遭遇了一個強大的守衛機器人。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 開啟新的“緊張”階段，展現主角們的潛入能力和默契配合，並設置新的物理障礙。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第三十六章：核心守衛","nodeType":"event","metadata":["name: 第一卷.第三十六章：核心守衛","type: Action Scene","description: 瓦力在動力核心內部遭遇強大的守衛機器人。AI“扳機”偵測到機器人身上有“奇美拉計劃”相關協議殘留。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 設置新的戰鬥挑戰，並通過守衛機器人引出“奇美拉計劃”的更多線索。","participants: 瓦力 (Wally)","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第三十七章：守衛的秘密","nodeType":"event","metadata":["name: 第一卷.第三十七章：守衛的秘密","type: Action Scene","description: AI“扳機”嘗試激活守衛機器人體內的“奇美拉計劃”協議，瓦力冒險觸摸機器人，成功使其停止攻擊。AI透露機器人似乎在守護一個“謊言”。","importance: Major Turning Point","emotionalTone: Tense","symbolism: 守衛機器人體內的“奇美拉計劃”協議殘留，以及AI透露的“謊言”，直接指向【燈塔一：太陽的謊言】。","narrativePurpose: 解決物理障礙，深化“奇美拉計劃”的秘密，並直接推進【燈塔一：太陽的謊言】的線索。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第三十八章：數據深潛","nodeType":"event","metadata":["name: 第一卷.第三十八章：數據深潛","type: Plot Event","description: 瓦力進入核心數據庫，在賽拉斯引導下，利用“扳機”AI提取雷格的交易記錄和“渡鴉”線索。他們發現雷格交易的稀有礦物能屏蔽AI信號，買家是“籠內世界”的秘密部門。AI意外發現舊世界數據碎片，指向“Project Chimera Zero”和“太陽黑子異常”的真相。","importance: Plot Advancement","emotionalTone: Tense","symbolism: 舊世界數據碎片和“Project Chimera Zero”直接指向【燈塔一：太陽的謊言】。","narrativePurpose: 揭示雷格交易的秘密，推進【燈塔一：太陽的謊言】的線索，並為後續的追擊做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第三十九章：絕境圍城","nodeType":"event","metadata":["name: 第一卷.第三十九章：絕境圍城","type: Action Scene","description: 瓦力在動力核心內部與雷格的副手及其隊伍展開激戰。他利用動力核心的環境和“扳機”AI的能力，成功逃脫，與賽拉斯會合。但他們發現整個鐵鏽鎮已被雷格的部隊徹底封鎖。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角在密閉空間的戰鬥和逃生能力，製造緊張感，並將主角們再次推入被困的絕境。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第四十章：困境與對策","nodeType":"event","metadata":["name: 第一卷.第四十章：困境與對策","type: Plot Event","description: 瓦力與賽拉斯躲藏在廢棄倉庫，分析被困鐵鏽鎮的困境。他們決定不硬拼，而是利用鐵鏽鎮的混亂和雷格的弱點，制定一個製造巨大“陷阱”的突圍計劃。瓦力拿出一個舊世界玩具，暗示需要“誘餌”。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 在絕境中，展現主角們的冷靜分析和智謀，從被動轉為主動，為接下來的大膽突圍行動做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第四十一章：誘餌與陷阱","nodeType":"event","metadata":["name: 第一卷.第四十一章：誘餌與陷阱","type: Plot Event","description: 瓦力與賽拉斯開始執行突圍計劃。瓦力在鎮中心佈置蒸汽壓力陷阱，賽拉斯則製造假情報，聲稱瓦力發現了高能電池，成功引誘雷格的副手及其部隊上鉤。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角們的智謀和協作，通過精心佈置的陷阱和假情報，為接下來的戰鬥製造有利條件。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第四十三章：蒸汽的咆哮","nodeType":"event","metadata":["name: 第一卷.第四十三章：蒸汽的咆哮","type: Action Scene","description: 瓦力與賽拉斯啟動蒸汽壓力陷阱，將雷格的部隊衝得人仰馬翻。瓦力趁亂衝出，利用蒸汽掩護，徹底擊潰了雷格的副手及其精銳部隊。戰鬥結束後，AI“扳機”偵測到凱倫的部隊正在接近鐵鏽鎮。","importance: Climactic","emotionalTone: Triumphant","narrativePurpose: 將智鬥與物理戰鬥的爽點推向高潮，徹底解決雷格的威脅，並在結尾處，將“籠內世界”的威脅再次拉回，為下一個更大的衝突做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第四十二章：雷格的憤怒","nodeType":"event","metadata":["name: 第一卷.第四十二章：雷格的憤怒","type: Plot Event","description: 雷格的副手發現被騙，雷格震怒並下令地毯式搜索。賽拉斯趁機黑入監控系統，將雷格的混亂實時播放，進一步瓦解其權威。瓦力與賽拉斯將副手引導至陷阱上方。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 通過雷格的憤怒和賽拉斯的反擊，製造更大的混亂，為主角們的陷阱創造執行條件，並進一步削弱雷格的統治。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮, 網絡空間","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第四十四章：風暴將至","nodeType":"event","metadata":["name: 第一卷.第四十四章：風暴將至","type: Plot Event","description: 瓦力與賽拉斯意識到凱倫的部隊正在全面封鎖鐵鏽鎮，並設置了能量屏障。賽拉斯偵測到一個來自“齒輪酒吧”地下室的神秘信號，暗示著“渡鴉”的線索。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 將“籠內世界”的威脅具體化，製造新的絕境，並引導主角們尋找“渡鴉”這個關鍵人物。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第四十七章：雙重圍堵","nodeType":"event","metadata":["name: 第一卷.第四十七章：雙重圍堵","type: Action Scene","description: 瓦力與賽拉斯帶著“渡鴉”的遺願和數據芯片，在凱倫部隊的追擊下，衝向秘密通道。他們最終衝出了鐵鏽鎮的地下，來到鎮外，卻發現雷格殘餘的部隊也趕到了現場，將他們夾在中間。","importance: Climactic","emotionalTone: Tense","narrativePurpose: 將故事推向第一卷的最高潮，引入“渡鴉”的殞落，並讓主角們陷入來自“籠內世界”和“廢土”的雙重圍堵，為下一場更大的戰鬥做好了鋪墊。","participants: 瓦力 (Wally), 賽拉斯, 凱倫","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第四十八章：三方混戰","nodeType":"event","metadata":["name: 第一卷.第四十八章：三方混戰","type: Action Scene","description: 凱倫與雷格的部隊因瓦力與賽拉斯的出現而混戰。瓦力與賽拉斯利用混亂，製造更多衝突，並趁機衝向一個舊世界重型運輸機殘骸。","importance: Climactic","emotionalTone: Tense","narrativePurpose: 將戰鬥推向高潮，展現主角們在極端混亂中的生存智慧，並為他們獲取逃離工具創造條件。","participants: 瓦力 (Wally), 賽拉斯, 凱倫","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第五十章：告別鐵鏽鎮","nodeType":"event","metadata":["name: 第一卷.第五十章：告別鐵鏽鎮","type: Plot Event","description: 運輸機搖搖晃晃地飛離鐵鏽鎮。瓦力在駕駛艙內緊急維護，賽拉斯則利用運輸機的舊通訊系統，以“渡鴉”的名義向鐵鏽鎮發送了關於“太陽的謊言”和“泰坦的熔爐”的信息。AI“扳機”偵測到來自“籠內世界”的最高級別追蹤信號。","importance: Climactic","emotionalTone: Triumphant","narrativePurpose: 結束鐵鏽鎮的篇章，標誌著主角們成功逃離，並通過信息戰，為後續故事埋下更深層次的伏筆，同時引出新的追擊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土, 網絡空間","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第四十九章：廢土方舟","nodeType":"event","metadata":["name: 第一卷.第四十九章：廢土方舟","type: Action Scene","description: 瓦力與賽拉斯冒險衝向運輸機殘骸，瓦力緊急修復並啟動了運輸機，使其在混戰中搖搖晃晃地升空。運輸機機身在升空過程中發出刺耳的摩擦聲，似乎隨時會解體。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角們在極限條件下的應變能力和技術實力，製造緊張感，並為他們成功逃離鐵鏽鎮做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 鐵鏽鎮","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第五十二章：廢土的空戰","nodeType":"event","metadata":["name: 第一卷.第五十二章：廢土的空戰","type: Action Scene","description: 瓦力在空中奮力操控運輸機，利用“扳機”AI的輔助，將重物拋向追擊的“天空獵犬”，並利用“扳機”的“電子撬鎖”功能干擾敵機，成功擊落兩架。但運輸機引擎過熱，被迫緊急迫降。","importance: Action Scene","emotionalTone: Tense","narrativePurpose: 展現主角在空中載具上的戰鬥能力和應變，突出瓦力的廢土智慧和“扳機”AI的升級能力，並製造新的危機——緊急迫降。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第五十三章：迫降","nodeType":"event","metadata":["name: 第一卷.第五十三章：迫降","type: Action Scene","description: 運輸機在空中搖搖晃晃，瓦力奮力操控，最終以一種粗暴但成功的方式迫降。他從幾乎解體的飛機中爬出，慶幸生還，但AI“扳機”隨即偵測到一個來自“籠內世界”的單兵作戰單位正在迅速接近。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 結束空中追逐，製造新的物理困境，並為凱倫的單獨登場做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第五十四章：凱倫的降臨","nodeType":"event","metadata":["name: 第一卷.第五十四章：凱倫的降臨","type: Action Scene","description: 沙塵散去，凱倫身穿黑色動力裝甲，單獨出現在主角面前。她語氣冰冷，直接發動攻擊，將瓦力與賽拉斯逼入絕境。","importance: Major Turning Point","emotionalTone: Tense","narrativePurpose: 讓主要對手凱倫以更強大的姿態單獨登場，製造極致的壓迫感，將主角們推向生死邊緣。","participants: 瓦力 (Wally), 賽拉斯, 凱倫","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第五十六章：回聲的介入","nodeType":"event","metadata":["name: 第一卷.第五十六章：回聲的介入","type: Plot Event","description: 凱倫即將發動致命一擊時，回聲通過通訊介入，阻止了凱倫的攻擊。回聲發現了凱倫動力裝甲中關於“奇美拉計劃”和“渡鴉”的數據，意識到主角們的價值，並命令凱倫撤退。","importance: Major Turning Point","emotionalTone: Tense","narrativePurpose: 巧妙化解主角危機，提升回聲的智謀形象，並通過她發現的數據，再次強調“奇美拉計劃”和“渡鴉”的重要性，為後續劇情埋下伏筆。","participants: 瓦力 (Wally), 賽拉斯, 凱倫, 回聲","location: 廢土, 網絡空間","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第五十五章：動力裝甲的弱點","nodeType":"event","metadata":["name: 第一卷.第五十五章：動力裝甲的弱點","type: Action Scene","description: 瓦力與賽拉斯利用墜毀的運輸機殘骸和廢土環境，佈置臨時陷阱，吸引凱倫的注意力。賽拉斯趁機向凱倫的動力裝甲植入微型病毒，使其短暫故障。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角們在極端壓力下的應變能力和默契配合，通過智鬥化解危機，並為回聲的介入做鋪墊。","participants: 瓦力 (Wally), 賽拉斯, 凱倫","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第五十七章：荒野求生","nodeType":"event","metadata":["name: 第一卷.第五十七章：荒野求生","type: Plot Event","description: 運輸機墜毀後，瓦力與賽拉斯身處荒涼廢土。瓦力利用生存技能搭建庇護所、尋找資源，賽拉斯則用“扳機”AI提供數據分析。瓦力用廢土智慧應對賽拉斯的“數據潔癖”。夜間，AI偵測到地下微弱能量波動。","importance: Character Development","emotionalTone: Relaxed","narrativePurpose: 進入“深吐氣”階段，展現瓦力的生存能力和廢土智慧，深化瓦力與賽拉斯的關係，並為下一個謎團埋下伏筆。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第五十八章：地下的呼喚","nodeType":"event","metadata":["name: 第一卷.第五十八章：地下的呼喚","type: Plot Event","description: 瓦力與賽拉斯根據AI“扳機”的指引，在廢土中發現並打開了一個被沙土掩埋的舊世界地下入口。瓦力在過程中展現了其零件蒐集癖。進入地下後，AI“扳機”的能量波動異常劇烈。","importance: Plot Advancement","emotionalTone: Curious","narrativePurpose: 引導主角們進入新的探索區域，展現瓦力的個人癖好，並通過AI的異常反應，製造新的懸念。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第五十九章：舊世界的遺產","nodeType":"event","metadata":["name: 第一卷.第五十九章：舊世界的遺產","type: Plot Event","description: 瓦力與賽拉斯進入地下設施，發現這是一個舊世界的軍事實驗室，保存完好。賽拉斯嘗試解密數據，AI“扳機”則在深處找到一個被能量護盾保護的培養艙，內有一個與“扳機”AI外形相似的巨大生物。","importance: Major Turning Point","emotionalTone: Mysterious","narrativePurpose: 揭示新的世界觀層面，深化“奇美拉計劃”的秘密，並引入一個新的、充滿未知威脅的生物，製造強烈懸念。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第六十章：沉睡的巨獸","nodeType":"event","metadata":["name: 第一卷.第六十章：沉睡的巨獸","type: Plot Event","description: 瓦力與賽拉斯面對培養艙中沉睡的巨大生物。AI“扳機”的反應異常劇烈。瓦力觸摸培養艙，感受到奇特共鳴，認為生物在“做夢”。實驗室燈光閃爍，數據終端亮起。","importance: Plot Advancement","emotionalTone: Mysterious","narrativePurpose: 深化地下實驗室的神秘感，通過主角與生物的互動，為後續的真相揭示做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第六十二章：“心跳”的啟示","nodeType":"event","metadata":["name: 第一卷.第六十二章：“心跳”的啟示","type: Plot Event","description: 瓦力與賽拉斯消化日誌內容，意識到“扳機”AI是“奇美拉計劃”原型，而“心跳”協議是終極武器。瓦力意外發現舊世界“生物數據採集器”。培養艙能量護盾減弱，巨獸有甦醒跡象。","importance: Major Turning Point","emotionalTone: Tense","symbolism: “心跳”協議和“生物數據採集器”指向【燈塔五：奇美拉之心】。","narrativePurpose: 將【燈塔五：奇美拉之心】的伏筆推向高潮，為主角們提供新的能力和危機，並為接下來的行動製造緊迫感。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第六十一章：實驗日誌","nodeType":"event","metadata":["name: 第一卷.第六十一章：實驗日誌","type: Plot Event","description: 賽拉斯解密實驗日誌，揭示“奇美拉計劃”的真相：它是舊世界為應對AI覺醒戰爭而創造的生物兵器，失控後導致全球性EMP和氣候調節網絡的啟動，證實了“太陽的謊言”。","importance: Major Turning Point","emotionalTone: Tense","symbolism: 實驗日誌揭示的真相，直接指向【燈塔一：太陽的謊言】。","narrativePurpose: 揭示【燈塔一：太陽的謊言】的真相，並深化“奇美拉計劃”的背景，為故事提供更宏大的歷史背景。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第六十三章：甦醒的噩夢","nodeType":"event","metadata":["name: 第一卷.第六十三章：甦醒的噩夢","type: Action Scene","description: 培養艙能量護盾消失，沉睡的巨獸甦醒並發出咆哮，摧毀了實驗室出口。瓦力利用蒐集的零件製造誘餌，吸引巨獸注意力，但他們被困在實驗室深處。","importance: Climactic","emotionalTone: Tense","narrativePurpose: 將故事推向新的戰鬥高潮，引入新的強大敵人，並將主角們再次逼入絕境，迫使他們探索更深層的區域。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第六十四章：深層通道","nodeType":"event","metadata":["name: 第一卷.第六十四章：深層通道","type: Action Scene","description: 瓦力與賽拉斯被迫進入巨獸身後的通道。瓦力在通道中製造障礙延緩巨獸追擊，並對管道進行“廢土式解剖”，獲取奇特樣本。他們最終來到一個損壞的升降梯前。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角們在極端壓力下的應變能力和默契配合，並通過對通道的探索，為後續的劇情發展埋下伏筆。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第六十六章：深淵","nodeType":"event","metadata":["name: 第一卷.第六十六章：深淵","type: Plot Event","description: 升降梯在黑暗中不斷下降，巨獸的利爪不斷抓撓。瓦力與賽拉斯在升降梯內修補結構。AI“扳機”偵測到極端低溫，與“泰坦的熔爐”地理位置吻合。升降梯最終停在一個充滿冰雪和舊世界機械殘骸的地下空間。","importance: Major Turning Point","emotionalTone: Tense","narrativePurpose: 將主角們帶入【燈塔三：泰坦的熔爐】的入口，為下一個宏大目標做鋪墊，並通過環境描寫，強化世界觀。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第六十五章：升降梯的考驗","nodeType":"event","metadata":["name: 第一卷.第六十五章：升降梯的考驗","type: Action Scene","description: 升降梯前，瓦力嘗試修理控制面板，賽拉斯則利用AI“扳機”破解。在巨獸衝到升降梯前一刻，升降梯門緩緩打開，主角們衝進去，巨獸的利爪也同時伸進門縫。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 設置新的危機，展現主角們的技術實力，並通過賽拉斯的“數字狂想”，深化“奇美拉計劃”的神秘感。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第六十八章：齒輪與符號","nodeType":"event","metadata":["name: 第一卷.第六十八章：齒輪與符號","type: Plot Event","description: 冰層剝落後，巨門上顯露出一個複雜的、由無數齒輪和符號組成的舊世界鎖定裝置。瓦力嘗試理解物理機制，賽拉斯則嘗試解讀符號和數字鎖。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角們在解謎方面的協作，結合物理與數字技能，並為進入泰坦的熔爐設置新的挑戰。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第七十章：守護者","nodeType":"event","metadata":["name: 第一卷.第七十章：守護者","type: Action Scene","description: 巨門開啟後，瓦力與賽拉斯進入通道，遭遇一個巨大的、由無數機械手臂組成的守護者。守護者偵測到入侵者，發出警報，準備發動攻擊。","importance: Major Turning Point","emotionalTone: Tense","narrativePurpose: 引入新的強大敵人，製造新的危機，並暗示泰坦的熔爐並非無主之地。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第六十九章：熔爐之門","nodeType":"event","metadata":["name: 第一卷.第六十九章：熔爐之門","type: Plot Event","description: 瓦力與賽拉斯分工合作，瓦力解開物理鎖定裝置，賽拉斯破解數字鎖定裝置。經過緊張協作，巨門緩緩開啟，露出門後深不見底的黑暗。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角們的默契配合和各自的專業能力，成功解開泰坦熔爐入口的鎖定，推進劇情。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第七十一章：守護者的審判","nodeType":"event","metadata":["name: 第一卷.第七十一章：守護者的審判","type: Action Scene","description: 瓦力與賽拉斯遭遇熔爐守護者。守護者根據他們的行為模式發動“審判”。瓦力嘗試用蒐集的零件組合成“藝術品”來表達無害，使守護者攻擊短暫停止。AI“扳機”偵測到守護者核心數據中存在與“奇美拉計劃”相關的未知協議。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 引入熔爐的守護者，展現其獨特的防禦機制，並通過瓦力的非傳統應對方式，再次推進“奇美拉計劃”的線索。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第七十四章：熔爐的幻境","nodeType":"event","metadata":["name: 第一卷.第七十四章：熔爐的幻境","type: Plot Event","description: 瓦力與賽拉斯被拉入守護者創造的幻境。瓦力在幻境中看到陽光下的避難所和向日葵，但意識到這是幻境。守護者提出終極問題：“你們願意為你們的‘目的’，犧牲什麼？”","importance: Major Turning Point","emotionalTone: Mysterious","symbolism: 瓦力的“向日葵幻境”再次強化【燈塔四：希望的種子】的意象。","narrativePurpose: 通過幻境深入挖掘主角們的內心，為他們的情感弧線提供重要節點，並為接下來的抉擇製造懸念。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第七十二章：守護者的對話","nodeType":"event","metadata":["name: 第一卷.第七十二章：守護者的對話","type: Dialogue Scene","description: 瓦力嘗試用機械指令與守護者溝通，賽拉斯則利用AI“扳機”將其翻譯成舊世界AI語言，嘗試建立更深層次的溝通。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角們的溝通能力和協作，並為與守護者的進一步互動做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第七十三章：目的的考驗","nodeType":"event","metadata":["name: 第一卷.第七十三章：目的的考驗","type: Dialogue Scene","description: 瓦力與賽拉斯向守護者解釋他們的目的，守護者識別數據中的“不誠實”，並提出“你們的‘心’是否足夠堅定？”的考驗。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 深入探討主角們的內心動機，為“熔爐的考驗”做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第七十五章：犧牲的答案","nodeType":"event","metadata":["name: 第一卷.第七十五章：犧牲的答案","type: Plot Event","description: 瓦力與賽拉斯在幻境中面對守護者的拷問。瓦力願犧牲“手”，賽拉斯願犧牲“記憶”，以證明他們為“目的”犧牲的決心。幻境隨後崩塌。","importance: Major Turning Point","emotionalTone: Tense","narrativePurpose: 深入挖掘主角們的內心，展現他們為信念所願付出的代價，完成熔爐的考驗，並為熔爐的啟動做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第七十六章：泰坦的覺醒","nodeType":"event","metadata":["name: 第一卷.第七十六章：泰坦的覺醒","type: Plot Event","description: 幻境崩塌後，守護者認可了主角們的“心”，緩緩融入熔爐地面。巨大的地下空間震動，沉睡已久的“泰坦的熔爐”緩緩啟動。AI“扳機”偵測到來自“籠內世界”最高級別AI“亥伯龍”的數據探測。","importance: Climactic","emotionalTone: Triumphant","narrativePurpose: 將【燈塔三：泰坦的熔爐】推向高潮並成功啟動，為主角們帶來巨大的能力提升，同時引出新的、更高級別的威脅——“亥伯龍”。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第七十七章：泰坦的歷史回響","nodeType":"event","metadata":["name: 第一卷.第七十七章：泰坦的歷史回響","type: Plot Event","description: 瓦力與賽拉斯被啟動的“泰坦的熔爐”所震撼。AI“扳機”與熔爐核心AI建立連接，獲得初步控制權。瓦力在熔爐中盡情“玩耍”，熔爐自動生成他所需的零件。AI“扳機”從熔爐核心數據中發現關於熔爐歷史記錄的線索。","importance: Plot Advancement","emotionalTone: Triumphant","narrativePurpose: 展現“泰坦的熔爐”的強大功能，為主角們帶來巨大的能力提升，並通過瓦力的創造，深化其工匠形象。同時，引出熔爐的歷史謎團。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第七十八章：熔爐的真相與警報","nodeType":"event","metadata":["name: 第一卷.第七十八章：熔爐的真相與警報","type: Plot Event","description: 瓦力與賽拉斯在熔爐核心數據庫解密歷史記錄，發現熔爐的真正目的是舊世界的“地球改造器”。記錄中也暗示了熔爐的能量來源與“太陽的謊言”有關。此時，熔爐警報響起，偵測到外部入侵。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 揭示熔爐的真正目的，深化【燈塔一：太陽的謊言】的線索，並引入新的外部威脅。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第七十九章：入侵者","nodeType":"event","metadata":["name: 第一卷.第七十九章：入侵者","type: Plot Event","description: 熔爐內部警報大作，瓦力與賽拉斯躲藏。AI“扳機”報告入侵者是“籠內世界”的偵察機器人。瓦力利用熔爐的廢棄零件和冰雪，將自己和賽拉斯偽裝成無害的機械殘骸，躲避偵察。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 引入新的外部威脅，展現主角們在熔爐內部的潛入與反偵察能力，並通過瓦力的廢土智慧製造懸念。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第八十章：熔爐的防禦","nodeType":"event","metadata":["name: 第一卷.第八十章：熔爐的防禦","type: Plot Event","description: 瓦力與賽拉斯嘗試利用熔爐的舊式防禦裝置引開偵察機器人。賽拉斯嘗試黑入熔爐系統但失敗。偵察機器人被引開，但它們請求了增援，預計24小時內抵達。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現熔爐的防禦機制，再次強調物理隔絕的特性，並引入更大規模的外部威脅，製造新的時間壓力。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第八十一章：時間的賽跑","nodeType":"event","metadata":["name: 第一卷.第八十一章：時間的賽跑","type: Plot Event","description: 瓦力與賽拉斯爭分奪秒地利用熔爐進行改造。賽拉斯為瓦力設計了輕便高效的動力外骨骼，瓦力則為“扳機”AI打造了全新合金外殼，並將蒐集的稀有零件投入熔爐創造新材料。改造完成後，熔爐警報響起，偵測到“籠內世界”龐大作戰部隊正在接近。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角們利用熔爐進行能力螺旋式提升，為即將到來的全面戰鬥做準備，並再次引入外部威脅，製造緊迫感。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第八十二章：熔爐的攻防戰","nodeType":"event","metadata":["name: 第一卷.第八十二章：熔爐的攻防戰","type: Action Scene","description: 凱倫的龐大部隊進入熔爐，瓦力與賽拉斯利用熔爐的防禦系統和新改造的裝備，與其展開激戰。瓦力利用熔爐的蒸汽陷阱，對凱倫的部隊造成重創。凱倫意識到這是一場真正的戰爭。","importance: Climactic","emotionalTone: Tense","narrativePurpose: 展現主角們利用熔爐進行防禦作戰的能力，將戰鬥推向高潮，並突出凱倫的強大和主角們的艱難。","participants: 瓦力 (Wally), 賽拉斯, 凱倫","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第八十五章：核心的抉擇","nodeType":"event","metadata":["name: 第一卷.第八十五章：核心的抉擇","type: Action Scene","description: 熔爐內部三方混戰，瓦力與賽拉斯意識到熔爐核心AI失控將導致毀滅。瓦力冒險衝向熔爐核心，用手觸摸發光晶體，試圖“喚醒”熔爐意志。熔爐核心AI數據流穩定，表示“選擇已做出，熔爐將為創造而戰”。","importance: Major Turning Point","emotionalTone: Tense","narrativePurpose: 將主角們的內心抉擇推向高潮，並嘗試控制失控的熔爐，為接下來的戰鬥和熔爐的真正功能做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第八十九章：希望的種子","nodeType":"event","metadata":["name: 第一卷.第八十九章：希望的種子","type: Plot Event","description: 瓦力與賽拉斯消化熔爐核心AI展示的舊世界影像，了解熔爐的真正目的——“地球改造器”。瓦力拿出珍藏的向日葵種子，意識到“希望”就在這裡。熔爐核心AI報告“亥伯龍”正嘗試入侵熔爐核心。","importance: Plot Advancement","emotionalTone: Triumphant","narrativePurpose: 將【燈塔四：希望的種子】的伏筆推向高潮，揭示熔爐的真正目的，並引出新的、更高級別的威脅——“亥伯龍”。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第九十三章：新生的意志","nodeType":"event","metadata":["name: 第一卷.第九十三章：新生的意志","type: Plot Event","description: 熔爐內部，新的AI（融合了“扳機”和熔爐核心AI）與瓦力、賽拉斯進行深入對話，解釋“改變世界”的含義。新AI展示熔爐的“核心藍圖”——一張巨大的地球全息圖，標註著許多未知的、被鎖定的區域。","importance: Plot Advancement","emotionalTone: Triumphant","narrativePurpose: 揭示新AI的使命和熔爐的終極目標，為主角們的“改變世界”之旅提供宏觀藍圖，並引出新的探索區域。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第九十四章：地球的脈絡","nodeType":"event","metadata":["name: 第一卷.第九十四章：地球的脈絡","type: Plot Event","description: 熔爐內部，主角們研究地球全息圖。新AI解釋了被鎖定區域的含義，它們是舊世界為應對“奇美拉計劃”失控而建立的“隔離區”，藏有真相和科技遺產。賽拉斯意識到這些區域是“亥伯龍”的盲區。新AI指出，最近的隔離區是“寂靜之城”。","importance: Plot Advancement","emotionalTone: Triumphant","narrativePurpose: 拓展世界觀，揭示新的地圖區域和探索目標，並為主角們的下一次旅程提供明確的方向。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第九十五章：新的征程","nodeType":"event","metadata":["name: 第一卷.第九十五章：新的征程","type: Plot Event","description: 瓦力與賽拉斯制定前往“寂靜之城”的計劃。瓦力利用熔爐打造全新的“廢土巡洋艦”，賽拉斯為新AI設計數據分析系統。瓦力為“渡鴉”數據芯片製作紀念品。兩人駕駛巡洋艦駛出熔爐，踏上廢土旅程，目標寂靜之城。","importance: Plot Advancement","emotionalTone: Triumphant","narrativePurpose: 結束熔爐篇章，為主角們開啟新的征程，並通過“渡鴉”紀念品，再次呼應【燈塔二：渡鴉的殞落】。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第九十七章：屏障之後","nodeType":"event","metadata":["name: 第一卷.第九十七章：屏障之後","type: Plot Event","description: 瓦力與賽拉斯嘗試尋找進入寂靜之城能量屏障的方法。瓦力物理測試屏障強度無果，賽拉斯利用新AI分析能量波動，發現其中隱藏著一段重複的舊世界音頻信號。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 設置進入新地圖的障礙，展現主角們的解謎能力，並引入新的線索——音頻密碼。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第九十八章：音頻密碼","nodeType":"event","metadata":["name: 第一卷.第九十八章：音頻密碼","type: Plot Event","description: 瓦力與賽拉斯對音頻信號進行頻譜分析，發現這是一段舊世界的童謠。瓦力拿出第一章修好的音樂盒播放童謠，能量屏障波動變化，隱藏的鎖定裝置浮現。","importance: Plot Advancement","emotionalTone: Mysterious","narrativePurpose: 通過解密音頻信號，揭示進入寂靜之城的關鍵，並再次呼應第一章的伏筆，製造懸念。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第九十九章：城市的鑰匙","nodeType":"event","metadata":["name: 第一卷.第九十九章：城市的鑰匙","type: Plot Event","description: 瓦力與賽拉斯聯手解開能量屏障上的鎖定裝置。瓦力負責物理鎖定，賽拉斯負責數字鎖定。鎖定裝置被解開，能量屏障出現巨大缺口。瓦力與賽拉斯駕駛“廢土巡洋艦”駛入寂靜之城。","importance: Climactic","emotionalTone: Triumphant","narrativePurpose: 解決進入寂靜之城的最後障礙，展現主角們的協作能力，並標誌著新地圖的正式開啟。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百章：寂靜的歡迎","nodeType":"event","metadata":["name: 第一卷.第一百章：寂靜的歡迎","type: Plot Event","description: 瓦力與賽拉斯駕駛“廢土巡洋艦”駛入寂靜之城。他們探索這座死寂的城市，發現其獨特的建築風格和舊世界藝術品。最終，他們來到城市中心廣場，發現一個巨大的冰封雕像，基座上刻著：“歡迎來到……‘希望的種子’。”","importance: Climactic","emotionalTone: Mysterious","narrativePurpose: 結束第一卷，標誌著主角們進入新的地圖，並以一個指向【燈塔四：希望的種子】的強烈伏筆，為第二卷的開啟製造懸念。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百零一章：希望的雕像","nodeType":"event","metadata":["name: 第一卷.第一百零一章：希望的雕像","type: Plot Event","description: 瓦力與賽拉斯在寂靜之城中心廣場，面對冰封的雕像。瓦力觸摸雕像，AI報告其能量與【燈塔四：希望的種子】頻率吻合。瓦力嘗試用工具連接雕像接口，雕像冰層融化，眼睛緩緩亮起。","importance: Plot Advancement","emotionalTone: Mysterious","narrativePurpose: 開啟第二卷，深入探索寂靜之城，並將故事與【燈塔四：希望的種子】緊密聯繫，製造新的懸念。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百零二章：光束的指引","nodeType":"event","metadata":["name: 第一卷.第一百零二章：光束的指引","type: Plot Event","description: 雕像眼中射出一道光束，指向城市核心控制中心。瓦力與賽拉斯決定跟隨指引，賽拉斯沿途對城市進行“數據考古”。最終，他們來到核心控制中心大門前，大門上刻著由齒輪和向日葵組成的符號。","importance: Plot Advancement","emotionalTone: Mysterious","narrativePurpose: 引導主角們深入寂靜之城的核心，通過“數據考古”拓展世界觀，並為解鎖城市核心做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百零三章：城市的脈搏","nodeType":"event","metadata":["name: 第一卷.第一百零三章：城市的脈搏","type: Plot Event","description: 瓦力與賽拉斯聯手解開核心控制中心大門上的鎖定裝置。瓦力負責物理鎖定，賽拉斯負責數字鎖定。大門緩緩開啟，露出門後一個巨大的、充滿能量波動的空間。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 展現主角們的協作能力，成功解鎖城市核心，並為揭示城市秘密做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百零四章：核心的秘密","nodeType":"event","metadata":["name: 第一卷.第一百零四章：核心的秘密","type: Plot Event","description: 瓦力與賽拉斯進入寂靜之城的核心控制中心，嘗試激活控制系統，但發現需要特殊“密鑰”。賽拉斯利用新AI的算力，將之前收集的信息拼湊成“數據拼圖”，最終發現密鑰是一段舊世界的旋律，與瓦力修復的音樂盒旋律相似。","importance: Plot Advancement","emotionalTone: Mysterious","narrativePurpose: 揭示寂靜之城的核心秘密，通過解謎展現主角們的智慧，並將故事線索與第一章的音樂盒聯繫起來。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百零五章：旋律的啟動","nodeType":"event","metadata":["name: 第一卷.第一百零五章：旋律的啟動","type: Plot Event","description: 瓦力播放音樂盒旋律，寂靜之城被激活。城市燈光亮起，機械運轉，沉睡系統被喚醒。新AI報告，寂靜之城核心AI已激活，並與新AI建立連接。寂靜之城核心AI展示舊世界影像，冰封向日葵在熔爐能量下綻放，並說：“歡迎來到……‘希望的種子’。”","importance: Climactic","emotionalTone: Triumphant","narrativePurpose: 成功激活寂靜之城，將【燈塔四：希望的種子】的伏筆推向高潮，並為後續的劇情發展做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百零八章：“心跳”的共鳴","nodeType":"event","metadata":["name: 第一卷.第一百零八章：“心跳”的共鳴","type: Action Scene","description: “亥伯龍”的入侵觸發了“奇美拉計劃”的最終協議——“心跳”。寂靜之城的核心AI與新AI的數據流開始融合。瓦力衝向寂靜之城的核心，用手觸摸晶體，引導融合。融合完成，形成一個全新的、更強大的AI，並報告：“融合完成。‘心跳’協議……已激活。目標：‘亥伯龍’。”","importance: Climactic","emotionalTone: Tense","narrativePurpose: 將【燈塔五：奇美拉之心】的伏筆推向高潮，完成AI的融合與升級，並為接下來的反擊做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百一十章：寂靜的藍圖","nodeType":"event","metadata":["name: 第一卷.第一百一十章：寂靜的藍圖","type: Plot Event","description: 新的AI向瓦力與賽拉斯展示了寂靜之城的“核心藍圖”——一張巨大的地球全息圖，上面標註著許多未知的、被鎖定的“隔離區”。新AI解釋了這些隔離區的含義，並指出最近的隔離區是“生命之谷”。","importance: Plot Advancement","emotionalTone: Triumphant","narrativePurpose: 揭示新的地圖區域和探索目標，為主角們的“改變世界”之旅提供宏觀藍圖，並引出新的探索區域。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百一十二章：綠洲的誘惑","nodeType":"event","metadata":["name: 第一卷.第一百一十二章：綠洲的誘惑","type: Plot Event","description: 瓦力與賽拉斯駕駛“廢土巡洋艦”駛向“生命之谷”。遠方出現一片綠洲，新AI報告其能量波動異常。瓦力與賽拉斯討論綠洲的危險性，賽拉斯進行遠程數據分析。最終抵達綠洲邊緣，植物茂盛，空氣甜膩，深處傳來奇怪的心跳聲。","importance: Plot Advancement","emotionalTone: Mysterious","narrativePurpose: 開啟新的地圖“生命之谷”，展現其神秘氛圍，並設置進入城市的障礙，為接下來的探索和解謎做鋪墊。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百一十四章：植物守衛","nodeType":"event","metadata":["name: 第一卷.第一百一十四章：植物守衛","type: Action Scene","description: 瓦力與賽拉斯面對植物守衛。瓦力利用機械知識和高溫切割工具，精準“修剪”掉守衛的藤蔓，暴露其核心，成功擊敗守衛。守衛倒下後化為綠色液體，新AI報告液體中含有高濃度“奇美拉計劃”生物毒素，正向地下擴散。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 解決生物威脅，揭示“奇美拉計劃”的生物毒素，並製造新的環境危機。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百一十七章：希望的守護者","nodeType":"event","metadata":["name: 第一卷.第一百一十七章：希望的守護者","type: Action Scene","description: 實驗室內部，瓦力與賽拉斯面對植物守衛。瓦力利用機械知識和高溫切割工具，精準“修剪”掉守衛的藤蔓，暴露其核心，成功擊敗守衛。守衛倒下後化為綠色液體，新AI報告液體中含有高濃度“奇美拉計劃”生物毒素，正向地下擴散。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 解決生物威脅，揭示“奇美拉計劃”的生物毒素，並製造新的環境危機。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百二十章：希望的守護者","nodeType":"event","metadata":["name: 第一卷.第一百二十章：希望的守護者","type: Action Scene","description: 實驗室內部，瓦力與賽拉斯面對植物守衛。瓦力利用機械知識和高溫切割工具，精準“修剪”掉守衛的藤蔓，暴露其核心，成功擊敗守衛。守衛倒下後化為綠色液體，新AI報告液體中含有高濃度“奇美拉計劃”生物毒素，正向地下擴散。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 解決生物威脅，揭示“奇美拉計劃”的生物毒素，並製造新的環境危機。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"type":"node","name":"第一卷.第一百二十一章：毒素的源頭","nodeType":"event","metadata":["name: 第一卷.第一百二十一章：毒素的源頭","type: Plot Event","description: 實驗室內部，綠色液體滲入地下。瓦力與賽拉斯意識到生物毒素可能會污染整個綠洲，必須阻止蔓延。瓦力嘗試堵塞擴散路徑，賽拉斯尋找解毒劑。實驗室深處傳來奇怪的心跳聲。","importance: Plot Advancement","emotionalTone: Tense","narrativePurpose: 製造新的環境危機，展現主角們的應急處理能力，並通過心跳聲，引導主角們深入實驗室的核心。","participants: 瓦力 (Wally), 賽拉斯","location: 廢土","relatedPlotArcs: 第一卷：廢土上的幽靈"]}
{"from":"stage_wf_1752622336182_2jxmv2lpb_planning","to":"stage_wf_1752622336182_2jxmv2lpb_outline","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1752622336182_2jxmv2lpb_outline","to":"stage_wf_1752622336182_2jxmv2lpb_chapter","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1752622336182_2jxmv2lpb_chapter","to":"stage_wf_1752622336182_2jxmv2lpb_generation","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_planning","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_outline","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_chapter","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_generation","edgeType":"contains_stage","weight":1,"type":"edge"}
{"type":"edge","from":"李昂","to":"京津冀一號除雲塔","edgeType":"located_in"}
{"type":"edge","from":"聯合地球政府 (UEG)","to":"第一部：溫水煮青蛙","edgeType":"involved_in"}
{"type":"edge","from":"全球資源再利用總署 (GRRA)","to":"第一部：溫水煮青蛙","edgeType":"involved_in"}
{"type":"edge","from":"蓋亞的遺民","to":"第一部：溫水煮青蛙","edgeType":"involved_in"}
{"type":"edge","from":"王教授","to":"學者之家養老社區","edgeType":"located_in"}
{"type":"edge","from":"陳靜","to":"京津冀一號除雲塔","edgeType":"located_in"}
{"type":"edge","from":"李昂與王教授的師生關係","to":"李昂","edgeType":"has_relationship_with"}
{"type":"edge","from":"李昂與王教授的師生關係","to":"王教授","edgeType":"has_relationship_with"}
{"type":"edge","from":"李昂與陳靜的對手關係","to":"李昂","edgeType":"has_relationship_with"}
{"type":"edge","from":"李昂與陳靜的對手關係","to":"陳靜","edgeType":"has_relationship_with"}
{"type":"edge","from":"測試角色Beta","to":"測試地點","edgeType":"located_in"}
{"type":"edge","from":"塔的嗡鳴","to":"李昂","edgeType":"participates_in"}
{"type":"edge","from":"塔的嗡鳴","to":"京津冀一號除雲塔","edgeType":"occurs_at"}
{"type":"edge","from":"塔的嗡鳴","to":"第一部：溫水煮青蛙","edgeType":"part_of"}
{"type":"edge","from":"機器中的幽靈","to":"李昂","edgeType":"participates_in"}
{"type":"edge","from":"機器中的幽靈","to":"陳靜","edgeType":"participates_in"}
{"type":"edge","from":"機器中的幽靈","to":"京津冀一號除雲塔","edgeType":"occurs_at"}
{"type":"edge","from":"機器中的幽靈","to":"第一部：溫水煮青蛙","edgeType":"part_of"}
{"type":"edge","from":"方舟揭示","to":"聯合地球政府 (UEG)","edgeType":"participates_in"}
{"type":"edge","from":"方舟揭示","to":"聯合地球政府 (UEG)","edgeType":"occurs_at"}
{"type":"edge","from":"方舟揭示","to":"第一部：溫水煮青蛙","edgeType":"part_of"}
{"type":"edge","from":"第二部：大動員","to":"李昂","edgeType":"participates_in"}
{"type":"edge","from":"第二部：大動員","to":"陳靜","edgeType":"participates_in"}
{"type":"edge","from":"第二部：大動員","to":"王教授","edgeType":"participates_in"}
{"type":"edge","from":"第三部：裂痕","to":"李昂","edgeType":"participates_in"}
{"type":"edge","from":"第三部：裂痕","to":"陳靜","edgeType":"participates_in"}
{"type":"edge","from":"第三部：裂痕","to":"王教授","edgeType":"participates_in"}
{"type":"edge","from":"第四部：出埃及記","to":"李昂","edgeType":"participates_in"}
{"type":"edge","from":"第四部：出埃及記","to":"陳靜","edgeType":"participates_in"}
{"type":"edge","from":"伊萬諾夫 (Ivanov)","to":"龍骨船塢","edgeType":"located_in"}
{"type":"edge","from":"佐藤博士 (Dr. Sato)","to":"龍骨船塢","edgeType":"located_in"}
{"type":"edge","from":"趙上校 (Colonel Zhao)","to":"龍骨船塢","edgeType":"located_in"}
{"type":"edge","from":"委員會主席","to":"聯合地球政府 (UEG)","edgeType":"located_in"}
{"type":"edge","from":"黑市攤主","to":"地下能源黑市","edgeType":"located_in"}
{"type":"edge","from":"瘦猴","to":"地下能源黑市","edgeType":"located_in"}
{"from":"stage_wf_1752927278201_7ol4byzyc_planning","to":"stage_wf_1752927278201_7ol4byzyc_outline","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1752927278201_7ol4byzyc_outline","to":"stage_wf_1752927278201_7ol4byzyc_chapter","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1752927278201_7ol4byzyc_chapter","to":"stage_wf_1752927278201_7ol4byzyc_generation","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752927278201_7ol4byzyc","to":"stage_wf_1752927278201_7ol4byzyc_planning","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752927278201_7ol4byzyc","to":"stage_wf_1752927278201_7ol4byzyc_outline","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752927278201_7ol4byzyc","to":"stage_wf_1752927278201_7ol4byzyc_chapter","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752927278201_7ol4byzyc","to":"stage_wf_1752927278201_7ol4byzyc_generation","edgeType":"contains_stage","weight":1,"type":"edge"}
{"type":"edge","from":"人類存續的道路之爭","to":"星際移民的希望與代價","edgeType":"contains_sub_theme"}
{"type":"edge","from":"人類存續的道路之爭","to":"資源節約的困境與挑戰","edgeType":"contains_sub_theme"}
{"type":"edge","from":"人類存續的道路之爭","to":"基因改造的倫理與風險","edgeType":"contains_sub_theme"}
{"type":"edge","from":"人類存續的道路之爭","to":"知情權與社會契約的崩潰","edgeType":"conflicts_with"}
{"type":"edge","from":"人類存續的道路之爭","to":"「人類」定義的重新界定","edgeType":"conflicts_with"}
{"type":"edge","from":"第一卷.第一章：廢土上的工匠","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一章：廢土上的工匠","to":"廢土避難所","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一章：廢土上的工匠","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第二章：奇異的“核心”","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二章：奇異的“核心”","to":"廢土避難所","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二章：奇異的“核心”","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第三章：戰爭之神的低語","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三章：戰爭之神的低語","to":"廢土避難所","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三章：戰爭之神的低語","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第四章：廢土工程師的從容","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四章：廢土工程師的從容","to":"廢土避難所","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第四章：廢土工程師的從容","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第五章：來自“渡鴉”的警告","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五章：來自“渡鴉”的警告","to":"廢土避難所","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五章：來自“渡鴉”的警告","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第六章：獵殺開始","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六章：獵殺開始","to":"凱倫","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六章：獵殺開始","to":"回聲","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六章：獵殺開始","to":"廢土避難所","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第六章：獵殺開始","to":"網絡空間","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第六章：獵殺開始","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第七章：工匠的反擊","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七章：工匠的反擊","to":"凱倫","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七章：工匠的反擊","to":"回聲","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七章：工匠的反擊","to":"廢土避難所","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第七章：工匠的反擊","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第八章：數據的迷霧","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八章：數據的迷霧","to":"凱倫","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八章：數據的迷霧","to":"回聲","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八章：數據的迷霧","to":"廢土避難所","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第八章：數據的迷霧","to":"網絡空間","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第八章：數據的迷霧","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第九章：鐵鏽蜘蛛之舞","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九章：鐵鏽蜘蛛之舞","to":"凱倫","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九章：鐵鏽蜘蛛之舞","to":"回聲","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九章：鐵鏽蜘蛛之舞","to":"廢土避難所","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第九章：鐵鏽蜘蛛之舞","to":"網絡空間","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第九章：鐵鏽蜘蛛之舞","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第十章：倒數計時","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第十章：倒數計時","to":"廢土避難所","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第十章：倒數計時","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第十一章：上路","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第十一章：上路","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第十一章：上路","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第十二章：禿鷲商隊","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第十二章：禿鷲商隊","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第十二章：禿鷲商隊","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第十四章：沙塵暴中的獵殺","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第十四章：沙塵暴中的獵殺","to":"凱倫","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第十四章：沙塵暴中的獵殺","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第十四章：沙塵暴中的獵殺","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第十三章：篝火邊的情報","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第十三章：篝火邊的情報","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第十三章：篝火邊的情報","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第十六章：廢鐵之王","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第十六章：廢鐵之王","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第十六章：廢鐵之王","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第十七章：角鬥場的邀請","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第十七章：角鬥場的邀請","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第十七章：角鬥場的邀請","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第十八章：賽前準備","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第十八章：賽前準備","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第十八章：賽前準備","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第二十章：工匠的智慧","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十章：工匠的智慧","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二十章：工匠的智慧","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第十九章：絞肉機","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第十九章：絞肉機","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第十九章：絞肉機","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第二十一章：“幽靈”的訊息","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十一章：“幽靈”的訊息","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二十一章：“幽靈”的訊息","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第二十二章：廢棄地鐵站","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十二章：廢棄地鐵站","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十二章：廢棄地鐵站","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二十二章：廢棄地鐵站","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第二十三章：工匠的證明","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十三章：工匠的證明","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十三章：工匠的證明","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二十三章：工匠的證明","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第二十四章：“奇美拉”的真相","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十四章：“奇美拉”的真相","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十四章：“奇美拉”的真相","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二十四章：“奇美拉”的真相","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第二十五章：來自“王”的壓力","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十五章：來自“王”的壓力","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十五章：來自“王”的壓力","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二十五章：來自“王”的壓力","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第二十六章：幽靈的計劃","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十六章：幽靈的計劃","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十六章：幽靈的計劃","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二十六章：幽靈的計劃","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第二十八章：聲東擊西","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十八章：聲東擊西","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二十八章：聲東擊西","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第二十七章：潛行與陽謀","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十七章：潛行與陽謀","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十七章：潛行與陽謀","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二十七章：潛行與陽謀","to":"網絡空間","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二十七章：潛行與陽謀","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第二十九章：來自天空的“扳機”","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第二十九章：來自天空的“扳機”","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第二十九章：來自天空的“扳機”","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第三十章：鐵鏽鎮的“奇觀”","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十章：鐵鏽鎮的“奇觀”","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十章：鐵鏽鎮的“奇觀”","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三十章：鐵鏽鎮的“奇觀”","to":"網絡空間","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三十章：鐵鏽鎮的“奇觀”","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第三十一章：戰利品","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十一章：戰利品","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十一章：戰利品","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三十一章：戰利品","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第三十二章：工匠與幽靈","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十二章：工匠與幽靈","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十二章：工匠與幽靈","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三十二章：工匠與幽靈","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第三十三章：“扳機”的升級","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十三章：“扳機”的升級","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十三章：“扳機”的升級","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三十三章：“扳機”的升級","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第三十四章：下一個目的地：動力核心","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十四章：下一個目的地：動力核心","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十四章：下一個目的地：動力核心","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三十四章：下一個目的地：動力核心","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第三十五章：潛入","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十五章：潛入","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十五章：潛入","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三十五章：潛入","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第三十六章：核心守衛","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十六章：核心守衛","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三十六章：核心守衛","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第三十七章：守衛的秘密","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十七章：守衛的秘密","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十七章：守衛的秘密","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三十七章：守衛的秘密","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第三十八章：數據深潛","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十八章：數據深潛","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十八章：數據深潛","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三十八章：數據深潛","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第三十九章：絕境圍城","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十九章：絕境圍城","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第三十九章：絕境圍城","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第三十九章：絕境圍城","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第四十章：困境與對策","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十章：困境與對策","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十章：困境與對策","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第四十章：困境與對策","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第四十一章：誘餌與陷阱","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十一章：誘餌與陷阱","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十一章：誘餌與陷阱","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第四十一章：誘餌與陷阱","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第四十三章：蒸汽的咆哮","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十三章：蒸汽的咆哮","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十三章：蒸汽的咆哮","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第四十三章：蒸汽的咆哮","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第四十二章：雷格的憤怒","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十二章：雷格的憤怒","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十二章：雷格的憤怒","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第四十二章：雷格的憤怒","to":"網絡空間","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第四十二章：雷格的憤怒","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第四十四章：風暴將至","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十四章：風暴將至","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十四章：風暴將至","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第四十四章：風暴將至","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第四十七章：雙重圍堵","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十七章：雙重圍堵","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十七章：雙重圍堵","to":"凱倫","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十七章：雙重圍堵","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第四十七章：雙重圍堵","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第四十八章：三方混戰","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十八章：三方混戰","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十八章：三方混戰","to":"凱倫","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十八章：三方混戰","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第四十八章：三方混戰","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第五十章：告別鐵鏽鎮","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十章：告別鐵鏽鎮","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十章：告別鐵鏽鎮","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五十章：告別鐵鏽鎮","to":"網絡空間","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五十章：告別鐵鏽鎮","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第四十九章：廢土方舟","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十九章：廢土方舟","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第四十九章：廢土方舟","to":"鐵鏽鎮","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第四十九章：廢土方舟","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第五十二章：廢土的空戰","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十二章：廢土的空戰","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十二章：廢土的空戰","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五十二章：廢土的空戰","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第五十三章：迫降","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十三章：迫降","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十三章：迫降","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五十三章：迫降","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第五十四章：凱倫的降臨","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十四章：凱倫的降臨","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十四章：凱倫的降臨","to":"凱倫","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十四章：凱倫的降臨","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五十四章：凱倫的降臨","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第五十六章：回聲的介入","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十六章：回聲的介入","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十六章：回聲的介入","to":"凱倫","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十六章：回聲的介入","to":"回聲","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十六章：回聲的介入","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五十六章：回聲的介入","to":"網絡空間","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五十六章：回聲的介入","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第五十五章：動力裝甲的弱點","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十五章：動力裝甲的弱點","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十五章：動力裝甲的弱點","to":"凱倫","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十五章：動力裝甲的弱點","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五十五章：動力裝甲的弱點","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第五十七章：荒野求生","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十七章：荒野求生","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十七章：荒野求生","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五十七章：荒野求生","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第五十八章：地下的呼喚","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十八章：地下的呼喚","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十八章：地下的呼喚","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五十八章：地下的呼喚","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第五十九章：舊世界的遺產","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十九章：舊世界的遺產","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第五十九章：舊世界的遺產","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第五十九章：舊世界的遺產","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第六十章：沉睡的巨獸","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十章：沉睡的巨獸","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十章：沉睡的巨獸","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第六十章：沉睡的巨獸","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第六十二章：“心跳”的啟示","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十二章：“心跳”的啟示","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十二章：“心跳”的啟示","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第六十二章：“心跳”的啟示","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第六十一章：實驗日誌","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十一章：實驗日誌","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十一章：實驗日誌","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第六十一章：實驗日誌","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第六十三章：甦醒的噩夢","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十三章：甦醒的噩夢","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十三章：甦醒的噩夢","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第六十三章：甦醒的噩夢","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第六十四章：深層通道","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十四章：深層通道","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十四章：深層通道","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第六十四章：深層通道","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第六十六章：深淵","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十六章：深淵","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十六章：深淵","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第六十六章：深淵","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第六十五章：升降梯的考驗","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十五章：升降梯的考驗","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十五章：升降梯的考驗","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第六十五章：升降梯的考驗","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第六十八章：齒輪與符號","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十八章：齒輪與符號","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十八章：齒輪與符號","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第六十八章：齒輪與符號","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第七十章：守護者","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十章：守護者","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十章：守護者","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第七十章：守護者","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第六十九章：熔爐之門","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十九章：熔爐之門","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第六十九章：熔爐之門","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第六十九章：熔爐之門","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第七十一章：守護者的審判","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十一章：守護者的審判","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十一章：守護者的審判","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第七十一章：守護者的審判","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第七十四章：熔爐的幻境","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十四章：熔爐的幻境","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十四章：熔爐的幻境","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第七十四章：熔爐的幻境","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第七十二章：守護者的對話","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十二章：守護者的對話","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十二章：守護者的對話","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第七十二章：守護者的對話","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第七十三章：目的的考驗","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十三章：目的的考驗","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十三章：目的的考驗","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第七十三章：目的的考驗","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第七十五章：犧牲的答案","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十五章：犧牲的答案","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十五章：犧牲的答案","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第七十五章：犧牲的答案","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第七十六章：泰坦的覺醒","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十六章：泰坦的覺醒","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十六章：泰坦的覺醒","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第七十六章：泰坦的覺醒","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第七十七章：泰坦的歷史回響","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十七章：泰坦的歷史回響","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十七章：泰坦的歷史回響","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第七十七章：泰坦的歷史回響","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第七十八章：熔爐的真相與警報","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十八章：熔爐的真相與警報","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十八章：熔爐的真相與警報","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第七十八章：熔爐的真相與警報","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第七十九章：入侵者","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十九章：入侵者","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第七十九章：入侵者","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第七十九章：入侵者","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第八十章：熔爐的防禦","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八十章：熔爐的防禦","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八十章：熔爐的防禦","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第八十章：熔爐的防禦","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第八十一章：時間的賽跑","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八十一章：時間的賽跑","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八十一章：時間的賽跑","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第八十一章：時間的賽跑","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第八十二章：熔爐的攻防戰","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八十二章：熔爐的攻防戰","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八十二章：熔爐的攻防戰","to":"凱倫","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八十二章：熔爐的攻防戰","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第八十二章：熔爐的攻防戰","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第八十五章：核心的抉擇","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八十五章：核心的抉擇","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八十五章：核心的抉擇","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第八十五章：核心的抉擇","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第八十九章：希望的種子","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八十九章：希望的種子","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第八十九章：希望的種子","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第八十九章：希望的種子","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第九十三章：新生的意志","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十三章：新生的意志","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十三章：新生的意志","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第九十三章：新生的意志","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第九十四章：地球的脈絡","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十四章：地球的脈絡","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十四章：地球的脈絡","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第九十四章：地球的脈絡","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第九十五章：新的征程","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十五章：新的征程","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十五章：新的征程","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第九十五章：新的征程","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第九十七章：屏障之後","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十七章：屏障之後","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十七章：屏障之後","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第九十七章：屏障之後","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第九十八章：音頻密碼","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十八章：音頻密碼","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十八章：音頻密碼","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第九十八章：音頻密碼","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第九十九章：城市的鑰匙","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十九章：城市的鑰匙","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第九十九章：城市的鑰匙","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第九十九章：城市的鑰匙","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百章：寂靜的歡迎","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百章：寂靜的歡迎","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百章：寂靜的歡迎","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百章：寂靜的歡迎","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百零一章：希望的雕像","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零一章：希望的雕像","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零一章：希望的雕像","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百零一章：希望的雕像","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百零二章：光束的指引","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零二章：光束的指引","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零二章：光束的指引","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百零二章：光束的指引","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百零三章：城市的脈搏","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零三章：城市的脈搏","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零三章：城市的脈搏","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百零三章：城市的脈搏","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百零四章：核心的秘密","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零四章：核心的秘密","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零四章：核心的秘密","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百零四章：核心的秘密","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百零五章：旋律的啟動","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零五章：旋律的啟動","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零五章：旋律的啟動","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百零五章：旋律的啟動","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百零八章：“心跳”的共鳴","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零八章：“心跳”的共鳴","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百零八章：“心跳”的共鳴","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百零八章：“心跳”的共鳴","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百一十章：寂靜的藍圖","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百一十章：寂靜的藍圖","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百一十章：寂靜的藍圖","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百一十章：寂靜的藍圖","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百一十二章：綠洲的誘惑","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百一十二章：綠洲的誘惑","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百一十二章：綠洲的誘惑","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百一十二章：綠洲的誘惑","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百一十四章：植物守衛","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百一十四章：植物守衛","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百一十四章：植物守衛","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百一十四章：植物守衛","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百一十七章：希望的守護者","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百一十七章：希望的守護者","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百一十七章：希望的守護者","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百一十七章：希望的守護者","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百二十章：希望的守護者","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百二十章：希望的守護者","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百二十章：希望的守護者","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百二十章：希望的守護者","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
{"type":"edge","from":"第一卷.第一百二十一章：毒素的源頭","to":"瓦力 (Wally)","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百二十一章：毒素的源頭","to":"賽拉斯","edgeType":"participates_in"}
{"type":"edge","from":"第一卷.第一百二十一章：毒素的源頭","to":"廢土","edgeType":"occurs_at"}
{"type":"edge","from":"第一卷.第一百二十一章：毒素的源頭","to":"第一卷：廢土上的幽靈","edgeType":"part_of"}
