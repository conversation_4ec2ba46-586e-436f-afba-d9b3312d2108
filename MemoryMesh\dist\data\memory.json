{"type":"node","name":"workflow_wf_1752622336182_2jxmv2lpb","nodeType":"workflow","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","template_id: novel_standard_v1","template_version: 1.0.0","category: novel","status: not_started","current_stage: 0","total_stages: 4","progress: 0","created_at: 2025-07-15T23:32:16.182Z","updated_at: 2025-07-15T23:32:16.183Z","stages: [{\"id\":\"planning\",\"name\":\"規劃階段\",\"order\":0,\"status\":\"active\"},{\"id\":\"outline\",\"name\":\"大綱階段\",\"order\":1,\"status\":\"pending\"},{\"id\":\"chapter\",\"name\":\"章節階段\",\"order\":2,\"status\":\"pending\"},{\"id\":\"generation\",\"name\":\"生成階段\",\"order\":3,\"status\":\"pending\"}]"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_planning","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: planning","stage_name: 規劃階段","stage_order: 0","status: active","required_node_types: [\"character\",\"setting\",\"theme\"]","optional_node_types: [\"timeline\",\"worldbuilding\"]","completion_criteria: {\"minNodes\":3,\"requiredFields\":[\"name\",\"description\",\"background\"],\"minimumQualityScore\":70,\"qualityChecks\":[{\"type\":\"character_depth\",\"description\":\"角色需要有明確的動機、衝突和成長弧線\",\"validator\":\"character_quality_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"setting_richness\",\"description\":\"設定需要包含感官細節和文化背景\",\"validator\":\"setting_quality_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"theme_clarity\",\"description\":\"主題應該具體且能通過故事元素體現\",\"validator\":\"theme_quality_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"character\":{\"description\":\"創建具有三維特質的角色：外在目標、內在需求、致命缺陷\",\"examples\":[\"主角：年輕騎士，外在目標是拯救王國，內在需求是證明自己的價值，致命缺陷是過度自信\",\"反派：墮落法師，外在目標是獲得永生，內在需求是被理解和接納，致命缺陷是無法信任他人\"],\"qualityTips\":[\"每個角色都應該有獨特的說話方式和行為模式\",\"角色的背景故事要與當前情節相關\",\"確保角色有成長和變化的空間\"],\"commonMistakes\":[\"創建完美無缺的角色\",\"角色動機不明確或不合理\",\"所有角色說話方式相同\"]},\"setting\":{\"description\":\"描述不僅是地點，更是氛圍和情感的載體\",\"examples\":[\"古老的圖書館：塵埃在陽光中飛舞，羊皮紙的味道混合著蠟燭的香氣，暗示著知識的神秘和時間的流逝\",\"戰場：泥濘的土地，鐵鏽和血腥的味道，遠處的號角聲，體現戰爭的殘酷和英雄主義的複雜性\"],\"qualityTips\":[\"使用五感描述來增強沉浸感\",\"讓環境反映角色的內心狀態\",\"考慮設定對情節發展的影響\"],\"commonMistakes\":[\"過度描述無關緊要的細節\",\"設定與故事氛圍不符\",\"忽略環境對角色行為的影響\"]},\"theme\":{\"description\":\"主題應該通過角色行動和情節發展自然呈現\",\"examples\":[\"成長主題：通過主角面對挑戰、犯錯、學習的過程來體現\",\"友誼主題：通過角色間的互助、衝突、和解來展現\"],\"qualityTips\":[\"避免直接說教，讓主題通過故事自然流露\",\"確保主題與角色弧線和情節發展一致\",\"可以有多個相關的次主題\"],\"commonMistakes\":[\"主題過於抽象或模糊\",\"強行插入主題相關的對話\",\"主題與故事內容脫節\"]}}}","estimated_duration: 1-2 weeks","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_outline","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: outline","stage_name: 大綱階段","stage_order: 1","status: pending","required_node_types: [\"plotarc\",\"timeline\"]","optional_node_types: [\"conflict\",\"theme\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"main_plot\",\"key_events\"],\"minimumQualityScore\":75,\"qualityChecks\":[{\"type\":\"plot_structure\",\"description\":\"情節結構需要有清晰的起承轉合\",\"validator\":\"plot_structure_validator\",\"weight\":0.5,\"required\":true},{\"type\":\"pacing_balance\",\"description\":\"節奏安排需要有張弛有度的變化\",\"validator\":\"pacing_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"conflict_escalation\",\"description\":\"衝突需要有合理的升級和解決\",\"validator\":\"conflict_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"plotarc\":{\"description\":\"建立引人入勝的情節弧線，包含起因、發展、高潮、結局\",\"examples\":[\"三幕結構：建立(25%) → 對抗(50%) → 解決(25%)\",\"英雄之旅：平凡世界 → 冒險召喚 → 拒絕召喚 → 遇見導師 → 跨越門檻...\"],\"qualityTips\":[\"確保每個情節點都推動故事前進\",\"高潮應該是情感和行動的雙重頂點\",\"結局要回應開頭提出的問題\"],\"commonMistakes\":[\"中段情節拖沓，缺乏推進力\",\"高潮來得太突然或太平淡\",\"結局過於匆忙或留下太多未解之謎\"]},\"timeline\":{\"description\":\"建立清晰的時間線，確保事件邏輯合理\",\"examples\":[\"線性時間線：按時間順序發展的故事\",\"非線性時間線：使用倒敘、插敘等技巧\"],\"qualityTips\":[\"重要事件之間要有合理的時間間隔\",\"考慮季節、天氣對故事氛圍的影響\",\"確保角色成長與時間推移相符\"],\"commonMistakes\":[\"時間跳躍過於頻繁或突兀\",\"忽略時間對角色和環境的影響\",\"前後時間設定不一致\"]}}}","estimated_duration: 1 week","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_chapter","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: chapter","stage_name: 章節階段","stage_order: 2","status: pending","required_node_types: [\"scene\",\"chapter\"]","optional_node_types: [\"dialogue\",\"description\"]","completion_criteria: {\"minNodes\":5,\"requiredFields\":[\"scene_setting\",\"characters_involved\"],\"minimumQualityScore\":80,\"qualityChecks\":[{\"type\":\"scene_depth\",\"description\":\"場景需要有豐富的感官細節和情感層次\",\"validator\":\"scene_depth_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"dialogue_authenticity\",\"description\":\"對話需要符合角色性格且推動情節\",\"validator\":\"dialogue_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"pacing_control\",\"description\":\"章節節奏需要有適當的張弛變化\",\"validator\":\"chapter_pacing_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"scene\":{\"description\":\"創建生動的場景，平衡行動、對話、描述和內心獨白\",\"examples\":[\"行動場景：劍鬥 - 快節奏，短句，感官細節，情緒張力\",\"對話場景：談判 - 角色動機衝突，潛台詞，肢體語言\",\"反思場景：獨處 - 內心掙扎，回憶片段，情感深度\"],\"qualityTips\":[\"每個場景都要有明確的目的和衝突\",\"使用具體的感官細節增強沉浸感\",\"確保場景推動情節或角色發展\",\"注意場景間的過渡和連接\"],\"commonMistakes\":[\"場景過長或過短，節奏失衡\",\"缺乏具體的環境描述\",\"場景目的不明確，流於表面\",\"忽略角色在場景中的情感變化\"]},\"chapter\":{\"description\":\"構建完整的章節，有開頭鉤子、發展和結尾懸念\",\"examples\":[\"開頭鉤子：突發事件、謎團、衝突、有趣對話\",\"章節發展：推進主線、深化角色、揭示信息\",\"結尾懸念：未解問題、新的威脅、情感轉折\"],\"qualityTips\":[\"每章都要推進整體故事進程\",\"保持讀者的閱讀興趣和期待\",\"平衡不同類型的內容（行動、對話、描述）\",\"確保章節長度適中且一致\"],\"commonMistakes\":[\"章節缺乏內在結構和節奏\",\"結尾過於平淡，缺乏懸念\",\"章節間缺乏連貫性\",\"過度依賴對話或描述\"]}}}","estimated_duration: 2-3 weeks","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_generation","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: generation","stage_name: 生成階段","stage_order: 3","status: pending","required_node_types: [\"chapter\"]","optional_node_types: [\"revision_notes\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"content\",\"word_count\"],\"minimumQualityScore\":85,\"qualityChecks\":[{\"type\":\"narrative_consistency\",\"description\":\"敘述風格和視角需要保持一致\",\"validator\":\"narrative_consistency_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"character_voice\",\"description\":\"角色聲音需要獨特且一致\",\"validator\":\"character_voice_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"thematic_integration\",\"description\":\"主題需要自然融入故事中\",\"validator\":\"theme_integration_validator\",\"weight\":0.2,\"required\":true},{\"type\":\"prose_quality\",\"description\":\"文筆需要流暢且富有表現力\",\"validator\":\"prose_quality_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"chapter\":{\"description\":\"完成高質量的章節內容，整合所有故事元素\",\"examples\":[\"完整章節：開頭引人入勝，中段發展充實，結尾留有懸念\",\"修訂版本：基於反饋改進的內容，提升可讀性和影響力\"],\"qualityTips\":[\"確保每章都有明確的情節進展\",\"保持敘述聲音的一致性\",\"平衡描述、對話、行動和內心獨白\",\"注意章節間的過渡和連貫性\",\"最終校對語法、拼寫和格式\"],\"commonMistakes\":[\"急於完成而忽略質量\",\"前後章節風格不一致\",\"忽略細節的打磨和完善\",\"缺乏最終的整體審視和修訂\"]}}}","estimated_duration: 4-6 weeks","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"2150年的地球","nodeType":"setting","metadata":["name: 2150年的地球","type: Natural","description: 時間設定在2150年，此時的地球科技已高度發達。然而，世界正籠罩在巨大的危機之下：太陽黑子活動異常降低，導致全球氣溫急劇下降，威脅著所有生命。為了生存，人類依賴先進科技進行大規模的人工除雲作業，試圖讓更多的太陽輻射抵達地表以維持溫度。但根據超級AI的精密計算，這項措施只是飲鴆止渴，將會無止盡地消耗地球的寶貴資源。若情況持續，人類將在能夠建造足夠的逃生艦隊前耗盡所有資源，最終被困在冰封的地球上，迎來滅亡。","status: Active","atmosphere: 充滿末日感的絕望與掙扎，科技的冰冷與人性的求生慾望交織。","significance: Critical","notableFeatures: 全球性的人工除雲系統, 用於監控地球狀態與資源消耗的超級AI, 因氣溫下降而擴張的冰川與冰封城市, 為逃離地球而建造的巨型星際艦隊船塢","symbolicMeaning: 人類在科技、自然與生存之間的掙扎，以及希望與絕望的對立。"]}
{"type":"node","name":"即刻作戰_全員逃離地球","nodeType":"novel_project","metadata":["Projectname: 即刻作戰_全員逃離地球","Genre: 科幻, 硬科幻, 末日, 網路小說","Targetaudience: 喜歡慢節奏、硬科幻、沉浸式體驗及深度角色刻劃的讀者。","Writingstyle: 超慢節奏商業網文，超級沉浸式，著重在角色內心描寫和科技細節。","Plannedlength: 約200萬字","Currentprogress: 規劃階段 - 核心世界觀與風格設定完成","Thematicelements: 末日下的生存、科技與人性的掙扎、希望與絕望、集體主義與個人犧牲。","Qualitytargets: 提供高度沉浸感，透過細膩的科技描寫與深刻的角色內心戲，引發讀者對末日情境的深度思考。"]}
{"type":"node","name":"第一部：溫水煮青蛙","nodeType":"plotarc","metadata":["name: 第一部：溫水煮青蛙","type: Main Plot","description: 故事的開端。危機已經存在，但尚未達到臨界點。『方舟計畫』在高層中是個秘密，對普通大眾來說，生活只是變得『有點不方便』。透過主角的視角，展現2150年世界的日常、科技與潛在的危機，並在結尾以一場災難將末日預言推向檯面，啟動整個故事的核心衝突。","status: Planning","importance: Critical","progressPercentage: 0","mainCharacters: "]}
{"type":"node","name":"李昂","nodeType":"character","metadata":["Name: 李昂","Role: 除雲塔資深工程師","Status: Introduced","Description: 一位典型的技術專家，負責維護巨型『人工除雲塔』。他對系統瞭若指掌，相信技術能解決一切問題，但內心深處對系統日益增長的異常數據感到不安。他有輕微的強迫症和社交恐懼，更喜歡與機器打交道。","Abilities: 擁有被稱為『數據直感』的特殊能力。這是一種罕見的神經狀態，讓他能以直覺感知龐大的數據流。他能『感覺』到系統的健康狀態，將複雜的數據模式識別為和諧的『共鳴』或刺耳的『噪音』，從而發現連AI都可能忽略的潛在故障。","Importance: Protagonist","Motivation: 維持系統的完美運行，並找出讓他感到不安的數據異常的根源。","Internalconflict: 對技術的極度信任與其『數據直感』所揭示的殘酷現實之間的矛盾。他該相信官方的報告，還是相信自己那無法解釋的直覺？","Currentlocation: 京津冀一號除雲塔","Background: 出生於全球變冷初期的普通工程師家庭，成長環境充滿了對科技的信賴和對未來的樂觀主義。從小就展現出與眾不同的『數據直感』天賦，但在標準化的教育體系中，這種天賦被視為『注意力不集中』。他憑藉自身的努力成為除雲塔的頂尖工程師，其背景代表了在UEG治下成長起來的、對官方宣傳深信不疑的一代人。"]}
{"type":"node","name":"京津冀一號除雲塔","nodeType":"setting","metadata":["name: 京津冀一號除雲塔","type: Building","description: 位於前華北平原的巨型人工除雲設施，是全球除雲網絡的關鍵節點之一。塔身直入雲霄，結構複雜，是人類試圖對抗自然變化的象徵性建築。","status: Active"]}
{"type":"node","name":"聯合地球政府 (UEG)","nodeType":"organization","metadata":["name: 聯合地球政府 (UEG)","type: Government Agency","description: 名義上的全球最高權力機構，負責統籌「全員逃離地球」計畫（方舟計畫），並試圖在末日降臨前維持社會秩序。其內部充滿了官僚主義、政治鬥爭和不同國家勢力的角力，決策往往是多方妥協的產物，效率低下且充滿秘密。","status: Active","goals: 統籌『方舟計畫』的執行, 維持全球社會的基本秩序, 管理全球所剩不多的資源分配","influence: Global","publicReputation: 對外宣傳為人類唯一的希望，但因其效率低下和資源分配不公而飽受民眾詬病。","internalCulture: 官僚、階級森嚴、充滿秘密主義，內部派系林立，代表著舊時代國家力量的延續。","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"全球資源再利用總署 (GRRA)","nodeType":"organization","metadata":["name: 全球資源再利用總署 (GRRA)","type: Government Agency","description: 在資源極度匱乏的背景下應運而生的超級機構。GRRA最初只是個回收部門，但現在他們控制著從廢棄的太空站、冰封的城市，到每個人生活垃圾的一切「可回收物質」的定義權、所有權和分配權。他們是原材料的絕對壟斷者，對「方舟計畫」的建造進度有著舉足輕重的影響。","status: Active","goals: 最大化回收地球上所有可用資源, 壟斷原材料供應鏈, 在『方舟計畫』中爭取最大話語權","influence: Global","publicReputation: 被民眾視為冷酷的資源掠奪者，但又不得不依賴他們提供的再生資源。是一個讓人又敬又畏的存在。","internalCulture: 極端務實、高效、冷酷無情，信奉結果主義，對浪費零容忍。內部結構類似軍隊和大型企業的結合體。","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"蓋亞的遺民","nodeType":"organization","metadata":["name: 蓋亞的遺民","type: Secret Society","description: 一個極端的環保主義與地球本土主義結合的地下組織。他們堅信人類的命運與地球緊密相連，逃離是一種懦弱的背叛。他們視「方舟計畫」為最大的罪惡，致力於用各種手段（包括破壞）來阻止艦隊的建造。","status: Active","goals: 阻止『方舟計畫』, 迫使人類將資源用於修復地球生態, 喚醒民眾對地球的『信仰』","influence: Underground","publicReputation: 被UEG定義為恐怖組織，但在部分絕望的民眾中被視為英雄或先知。","internalCulture: 充滿理想主義和宗教狂熱的氛圍，成員背景複雜，從狂熱信徒到頂尖科學家都有，組織嚴密且行動力強。","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"科技的傲慢與自然的懲罰","nodeType":"theme","metadata":["name: 科技的傲慢與自然的懲罰","type: Main Theme","description: 故事的核心哲學思辨，探討人類過度依賴科技而忽視自然規律，最終導致自我毀滅的困境。主題表現為一場慢性的、不可逆的衰亡，而非突發災難。重點在於『解藥即是新毒藥』的惡性循環、人類『自然直覺』的喪失，以及在自然的『非人格化』審判面前，科技作為『詛咒』與『方舟』的雙重矛盾。","status: Introduced","importance: Core","currentDevelopment: 已確立核心概念和四大表現要點","symbolism: 人工除雲塔, 超級AI, 冰封的城市, 方舟計畫本身"]}
{"type":"node","name":"學者之家養老社區","nodeType":"setting","metadata":["name: 學者之家養老社區","type: Building","description: 位於UEG亞洲總部附近，專為對人類有卓越貢獻的科學家、學者和官員提供的高科技養老社區。表面上是頤養天年的地方，實則可能暗流湧動，是各種舊勢力交換情報的中心。","status: Active"]}
{"type":"node","name":"王教授","nodeType":"character","metadata":["name: 王教授","role: 退休科學家，李昂的導師","status: Active","description: 一位即將退休的老科學家，是李昂所在領域的權威，也是少數還記得『真實自然』是什麼樣的人。他相信數據，但更相信經驗和直覺。","background: 曾是『方舟計畫』前身，甚至是超級AI早期開發團隊的核心成員之一，後因不明原因『榮譽退休』。可能在暗中研究一個拯救地球的『B計畫』。","importance: Supporting Character","motivation: 不明，表面上是安度晚年，實際上可能在培養『B計畫』的接班人，或彌補過去的錯誤。","currentLocation: 學者之家養老社區"]}
{"type":"node","name":"陳靜","nodeType":"character","metadata":["name: 陳靜","role: GRRA監察員","status: Introduced","description: 年輕、果斷、極端務實的GRRA監察員，是GRRA冷酷效率的化身。她起初完全不理解李昂基於『直覺』的擔憂，認為這是浪費資源的無稽之談。","background: 其家人可能在早年的資源災難中，因官方的低效和浪費而喪生，這段經歷塑造了她對『浪費』的極度憎恨和冷酷務實的性格。她可能帶著GRRA高層的秘密任務，評估除雲塔的『回收價值』。","importance: Supporting Character","motivation: 表面上是確保資源利用效率最大化，深層動機是復仇——向所有低效、浪費的體制復仇。","currentLocation: 京津冀一號除雲塔"]}
{"type":"node","name":"李昂與王教授的師生關係","nodeType":"relationship","metadata":["name: 李昂與王教授的師生關係","relationshipType: Mentor-Student","status: Stable","intimacyLevel: 7","trustLevel: 8","conflictLevel: 2","character1Perspective: 尊敬他，視他為該領域的權威和導師，但有時不理解他為何對新技術抱持懷疑，並對他的『數據直感』天賦感到憂慮。","character2Perspective: 欣賞李昂的才華和純粹，但擔心他過於依賴技術。他似乎在有意引導李昂去思考更深層次的問題，可能是在為自己的『B計畫』尋找傳承者。","description: 一段亦師亦友的關係。王教授是李昂在專業領域的引路人，但在理念上，兩人之間存在著微妙的代溝和張力。這段關係背後可能隱藏著關於『方舟計畫』和AI的過去秘密。","character1: 李昂","character2: 王教授"]}
{"type":"node","name":"李昂與陳靜的對手關係","nodeType":"relationship","metadata":["name: 李昂與陳靜的對手關係","relationshipType: Rivalry","status: Strained","intimacyLevel: 1","trustLevel: 2","conflictLevel: 8","character1Perspective: 認為她是一個冷酷、不近人情、只看重數據和指標的官僚。對她的行事風格感到反感，但又不得不佩服她的效率。","character2Perspective: 認為李昂是一個不切實際、感情用事、甚至有點神經質的工程師。對他基於『直覺』的判斷嗤之以鼻，視其為潛在的資源浪費源頭。","description: 一段充滿衝突的對手關係。兩人因工作方式和價值觀的巨大差異而頻繁碰撞。然而，隨著危機加深，他們可能會發現彼此是唯一可以依賴的人，從而發展出一段複雜的合作關係。","character1: 李昂","character2: 陳靜"]}
{"from":"stage_wf_1752622336182_2jxmv2lpb_planning","to":"stage_wf_1752622336182_2jxmv2lpb_outline","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1752622336182_2jxmv2lpb_outline","to":"stage_wf_1752622336182_2jxmv2lpb_chapter","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1752622336182_2jxmv2lpb_chapter","to":"stage_wf_1752622336182_2jxmv2lpb_generation","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_planning","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_outline","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_chapter","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_generation","edgeType":"contains_stage","weight":1,"type":"edge"}
{"type":"edge","from":"李昂","to":"京津冀一號除雲塔","edgeType":"located_in"}
{"type":"edge","from":"聯合地球政府 (UEG)","to":"第一部：溫水煮青蛙","edgeType":"involved_in"}
{"type":"edge","from":"全球資源再利用總署 (GRRA)","to":"第一部：溫水煮青蛙","edgeType":"involved_in"}
{"type":"edge","from":"蓋亞的遺民","to":"第一部：溫水煮青蛙","edgeType":"involved_in"}
{"type":"edge","from":"王教授","to":"學者之家養老社區","edgeType":"located_in"}
{"type":"edge","from":"陳靜","to":"京津冀一號除雲塔","edgeType":"located_in"}
{"type":"edge","from":"李昂與王教授的師生關係","to":"李昂","edgeType":"has_relationship_with"}
{"type":"edge","from":"李昂與王教授的師生關係","to":"王教授","edgeType":"has_relationship_with"}
{"type":"edge","from":"李昂與陳靜的對手關係","to":"李昂","edgeType":"has_relationship_with"}
{"type":"edge","from":"李昂與陳靜的對手關係","to":"陳靜","edgeType":"has_relationship_with"}
