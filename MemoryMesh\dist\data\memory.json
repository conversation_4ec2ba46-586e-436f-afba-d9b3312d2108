{"type":"node","name":"workflow_wf_1752622336182_2jxmv2lpb","nodeType":"workflow","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","template_id: novel_standard_v1","template_version: 1.0.0","category: novel","status: not_started","current_stage: 0","total_stages: 4","progress: 0","created_at: 2025-07-15T23:32:16.182Z","updated_at: 2025-07-15T23:32:16.183Z","stages: [{\"id\":\"planning\",\"name\":\"規劃階段\",\"order\":0,\"status\":\"active\"},{\"id\":\"outline\",\"name\":\"大綱階段\",\"order\":1,\"status\":\"pending\"},{\"id\":\"chapter\",\"name\":\"章節階段\",\"order\":2,\"status\":\"pending\"},{\"id\":\"generation\",\"name\":\"生成階段\",\"order\":3,\"status\":\"pending\"}]"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_planning","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: planning","stage_name: 規劃階段","stage_order: 0","status: active","required_node_types: [\"character\",\"setting\",\"theme\"]","optional_node_types: [\"timeline\",\"worldbuilding\"]","completion_criteria: {\"minNodes\":3,\"requiredFields\":[\"name\",\"description\",\"background\"],\"minimumQualityScore\":70,\"qualityChecks\":[{\"type\":\"character_depth\",\"description\":\"角色需要有明確的動機、衝突和成長弧線\",\"validator\":\"character_quality_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"setting_richness\",\"description\":\"設定需要包含感官細節和文化背景\",\"validator\":\"setting_quality_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"theme_clarity\",\"description\":\"主題應該具體且能通過故事元素體現\",\"validator\":\"theme_quality_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"character\":{\"description\":\"創建具有三維特質的角色：外在目標、內在需求、致命缺陷\",\"examples\":[\"主角：年輕騎士，外在目標是拯救王國，內在需求是證明自己的價值，致命缺陷是過度自信\",\"反派：墮落法師，外在目標是獲得永生，內在需求是被理解和接納，致命缺陷是無法信任他人\"],\"qualityTips\":[\"每個角色都應該有獨特的說話方式和行為模式\",\"角色的背景故事要與當前情節相關\",\"確保角色有成長和變化的空間\"],\"commonMistakes\":[\"創建完美無缺的角色\",\"角色動機不明確或不合理\",\"所有角色說話方式相同\"]},\"setting\":{\"description\":\"描述不僅是地點，更是氛圍和情感的載體\",\"examples\":[\"古老的圖書館：塵埃在陽光中飛舞，羊皮紙的味道混合著蠟燭的香氣，暗示著知識的神秘和時間的流逝\",\"戰場：泥濘的土地，鐵鏽和血腥的味道，遠處的號角聲，體現戰爭的殘酷和英雄主義的複雜性\"],\"qualityTips\":[\"使用五感描述來增強沉浸感\",\"讓環境反映角色的內心狀態\",\"考慮設定對情節發展的影響\"],\"commonMistakes\":[\"過度描述無關緊要的細節\",\"設定與故事氛圍不符\",\"忽略環境對角色行為的影響\"]},\"theme\":{\"description\":\"主題應該通過角色行動和情節發展自然呈現\",\"examples\":[\"成長主題：通過主角面對挑戰、犯錯、學習的過程來體現\",\"友誼主題：通過角色間的互助、衝突、和解來展現\"],\"qualityTips\":[\"避免直接說教，讓主題通過故事自然流露\",\"確保主題與角色弧線和情節發展一致\",\"可以有多個相關的次主題\"],\"commonMistakes\":[\"主題過於抽象或模糊\",\"強行插入主題相關的對話\",\"主題與故事內容脫節\"]}}}","estimated_duration: 1-2 weeks","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_outline","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: outline","stage_name: 大綱階段","stage_order: 1","status: pending","required_node_types: [\"plotarc\",\"timeline\"]","optional_node_types: [\"conflict\",\"theme\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"main_plot\",\"key_events\"],\"minimumQualityScore\":75,\"qualityChecks\":[{\"type\":\"plot_structure\",\"description\":\"情節結構需要有清晰的起承轉合\",\"validator\":\"plot_structure_validator\",\"weight\":0.5,\"required\":true},{\"type\":\"pacing_balance\",\"description\":\"節奏安排需要有張弛有度的變化\",\"validator\":\"pacing_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"conflict_escalation\",\"description\":\"衝突需要有合理的升級和解決\",\"validator\":\"conflict_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"plotarc\":{\"description\":\"建立引人入勝的情節弧線，包含起因、發展、高潮、結局\",\"examples\":[\"三幕結構：建立(25%) → 對抗(50%) → 解決(25%)\",\"英雄之旅：平凡世界 → 冒險召喚 → 拒絕召喚 → 遇見導師 → 跨越門檻...\"],\"qualityTips\":[\"確保每個情節點都推動故事前進\",\"高潮應該是情感和行動的雙重頂點\",\"結局要回應開頭提出的問題\"],\"commonMistakes\":[\"中段情節拖沓，缺乏推進力\",\"高潮來得太突然或太平淡\",\"結局過於匆忙或留下太多未解之謎\"]},\"timeline\":{\"description\":\"建立清晰的時間線，確保事件邏輯合理\",\"examples\":[\"線性時間線：按時間順序發展的故事\",\"非線性時間線：使用倒敘、插敘等技巧\"],\"qualityTips\":[\"重要事件之間要有合理的時間間隔\",\"考慮季節、天氣對故事氛圍的影響\",\"確保角色成長與時間推移相符\"],\"commonMistakes\":[\"時間跳躍過於頻繁或突兀\",\"忽略時間對角色和環境的影響\",\"前後時間設定不一致\"]}}}","estimated_duration: 1 week","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_chapter","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: chapter","stage_name: 章節階段","stage_order: 2","status: pending","required_node_types: [\"scene\",\"chapter\"]","optional_node_types: [\"dialogue\",\"description\"]","completion_criteria: {\"minNodes\":5,\"requiredFields\":[\"scene_setting\",\"characters_involved\"],\"minimumQualityScore\":80,\"qualityChecks\":[{\"type\":\"scene_depth\",\"description\":\"場景需要有豐富的感官細節和情感層次\",\"validator\":\"scene_depth_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"dialogue_authenticity\",\"description\":\"對話需要符合角色性格且推動情節\",\"validator\":\"dialogue_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"pacing_control\",\"description\":\"章節節奏需要有適當的張弛變化\",\"validator\":\"chapter_pacing_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"scene\":{\"description\":\"創建生動的場景，平衡行動、對話、描述和內心獨白\",\"examples\":[\"行動場景：劍鬥 - 快節奏，短句，感官細節，情緒張力\",\"對話場景：談判 - 角色動機衝突，潛台詞，肢體語言\",\"反思場景：獨處 - 內心掙扎，回憶片段，情感深度\"],\"qualityTips\":[\"每個場景都要有明確的目的和衝突\",\"使用具體的感官細節增強沉浸感\",\"確保場景推動情節或角色發展\",\"注意場景間的過渡和連接\"],\"commonMistakes\":[\"場景過長或過短，節奏失衡\",\"缺乏具體的環境描述\",\"場景目的不明確，流於表面\",\"忽略角色在場景中的情感變化\"]},\"chapter\":{\"description\":\"構建完整的章節，有開頭鉤子、發展和結尾懸念\",\"examples\":[\"開頭鉤子：突發事件、謎團、衝突、有趣對話\",\"章節發展：推進主線、深化角色、揭示信息\",\"結尾懸念：未解問題、新的威脅、情感轉折\"],\"qualityTips\":[\"每章都要推進整體故事進程\",\"保持讀者的閱讀興趣和期待\",\"平衡不同類型的內容（行動、對話、描述）\",\"確保章節長度適中且一致\"],\"commonMistakes\":[\"章節缺乏內在結構和節奏\",\"結尾過於平淡，缺乏懸念\",\"章節間缺乏連貫性\",\"過度依賴對話或描述\"]}}}","estimated_duration: 2-3 weeks","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"stage_wf_1752622336182_2jxmv2lpb_generation","nodeType":"stage","metadata":["workflow_id: wf_1752622336182_2jxmv2lpb","stage_id: generation","stage_name: 生成階段","stage_order: 3","status: pending","required_node_types: [\"chapter\"]","optional_node_types: [\"revision_notes\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"content\",\"word_count\"],\"minimumQualityScore\":85,\"qualityChecks\":[{\"type\":\"narrative_consistency\",\"description\":\"敘述風格和視角需要保持一致\",\"validator\":\"narrative_consistency_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"character_voice\",\"description\":\"角色聲音需要獨特且一致\",\"validator\":\"character_voice_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"thematic_integration\",\"description\":\"主題需要自然融入故事中\",\"validator\":\"theme_integration_validator\",\"weight\":0.2,\"required\":true},{\"type\":\"prose_quality\",\"description\":\"文筆需要流暢且富有表現力\",\"validator\":\"prose_quality_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"chapter\":{\"description\":\"完成高質量的章節內容，整合所有故事元素\",\"examples\":[\"完整章節：開頭引人入勝，中段發展充實，結尾留有懸念\",\"修訂版本：基於反饋改進的內容，提升可讀性和影響力\"],\"qualityTips\":[\"確保每章都有明確的情節進展\",\"保持敘述聲音的一致性\",\"平衡描述、對話、行動和內心獨白\",\"注意章節間的過渡和連貫性\",\"最終校對語法、拼寫和格式\"],\"commonMistakes\":[\"急於完成而忽略質量\",\"前後章節風格不一致\",\"忽略細節的打磨和完善\",\"缺乏最終的整體審視和修訂\"]}}}","estimated_duration: 4-6 weeks","created_at: 2025-07-15T23:32:16.183Z"]}
{"type":"node","name":"2150年的地球","nodeType":"setting","metadata":["name: 2150年的地球","type: Natural","description: 時間設定在2150年，此時的地球科技已高度發達。然而，世界正籠罩在巨大的危機之下：太陽黑子活動異常降低，導致全球氣溫急劇下降，威脅著所有生命。為了生存，人類依賴先進科技進行大規模的人工除雲作業，試圖讓更多的太陽輻射抵達地表以維持溫度。但根據超級AI的精密計算，這項措施只是飲鴆止渴，將會無止盡地消耗地球的寶貴資源。若情況持續，人類將在能夠建造足夠的逃生艦隊前耗盡所有資源，最終被困在冰封的地球上，迎來滅亡。","status: Active","atmosphere: 充滿末日感的絕望與掙扎，科技的冰冷與人性的求生慾望交織。","significance: Critical","notableFeatures: 全球性的人工除雲系統, 用於監控地球狀態與資源消耗的超級AI, 因氣溫下降而擴張的冰川與冰封城市, 為逃離地球而建造的巨型星際艦隊船塢","symbolicMeaning: 人類在科技、自然與生存之間的掙扎，以及希望與絕望的對立。"]}
{"type":"node","name":"即刻作戰_全員逃離地球","nodeType":"novel_project","metadata":["Projectname: 即刻作戰_全員逃離地球","Genre: 科幻, 硬科幻, 末日, 網路小說","Targetaudience: 喜歡慢節奏、硬科幻、沉浸式體驗及深度角色刻劃的讀者。","Writingstyle: 超慢節奏商業網文，超級沉浸式，著重在角色內心描寫和科技細節。","Plannedlength: 約200萬字","Currentprogress: 規劃階段 - 核心世界觀與風格設定完成","Thematicelements: 末日下的生存、科技與人性的掙扎、希望與絕望、集體主義與個人犧牲。","Qualitytargets: 提供高度沉浸感，透過細膩的科技描寫與深刻的角色內心戲，引發讀者對末日情境的深度思考。"]}
{"type":"node","name":"第一部：溫水煮青蛙","nodeType":"plotarc","metadata":["name: 第一部：溫水煮青蛙","type: Main Plot","description: 故事的開端。危機已經存在，但尚未達到臨界點。『方舟計畫』在高層中是個秘密，對普通大眾來說，生活只是變得『有點不方便』。透過主角的視角，展現2150年世界的日常、科技與潛在的危機，並在結尾以一場災難將末日預言推向檯面，啟動整個故事的核心衝突。","status: Planning","importance: Critical","progressPercentage: 0","mainCharacters: "]}
{"type":"node","name":"李昂","nodeType":"character","metadata":["Name: 李昂","Role: 除雲塔資深工程師","Status: Introduced","Description: 一位典型的技術專家，負責維護巨型『人工除雲塔』。他對系統瞭若指掌，相信技術能解決一切問題，但內心深處對系統日益增長的異常數據感到不安。他有輕微的強迫症和社交恐懼，更喜歡與機器打交道。","Abilities: 擁有被稱為『數據直感』的特殊能力。這是一種罕見的神經狀態，讓他能以直覺感知龐大的數據流。他能『感覺』到系統的健康狀態，將複雜的數據模式識別為和諧的『共鳴』或刺耳的『噪音』，從而發現連AI都可能忽略的潛在故障。","Importance: Protagonist","Motivation: 維持系統的完美運行，並找出讓他感到不安的數據異常的根源。","Internalconflict: 對『秩序』的強迫性追求，與一個走向終極混亂的世界之間的根本矛盾。他對處理複雜情感的迴避，與末日下越發激烈的人性衝突之間的矛盾。他對『真相』近乎殉道的執著，與權力者需要用謊言維持穩定之間的矛盾。","Currentlocation: 京津冀一號除雲塔","Background: 出生於全球變冷初期的普通工程師家庭，成長環境充滿了對科技的信賴和對未來的樂觀主義。從小就展現出與眾不同的『數據直感』天賦，但在標準化的教育體系中，這種天賦被視為『注意力不集中』。他憑藉自身的努力成為除雲塔的頂尖工程師，其背景代表了在UEG治下成長起來的、對官方宣傳深信不疑的一代人。","Traits: 『秩序』的強迫症：內心極度渴求可預測性和控制感，對任何形式的『失序』都難以忍受。, 『情感』的迴避者：不擅長處理複雜的人際關係和強烈的情感，習慣性地退回到有邏輯的技術世界。, 『真相』的殉道者：一旦確認了某件事是『真實』的，會不計後果地去揭示它，恢復他所認為的『秩序』。"]}
{"type":"node","name":"京津冀一號除雲塔","nodeType":"setting","metadata":["name: 京津冀一號除雲塔","type: Building","description: 位於前華北平原的巨型人工除雲設施，是全球除雲網絡的關鍵節點之一。塔身直入雲霄，結構複雜，是人類試圖對抗自然變化的象徵性建築。","status: Active"]}
{"type":"node","name":"聯合地球政府 (UEG)","nodeType":"organization","metadata":["name: 聯合地球政府 (UEG)","type: Government Agency","description: 名義上的全球最高權力機構，負責統籌「全員逃離地球」計畫（方舟計畫），並試圖在末日降臨前維持社會秩序。其內部充滿了官僚主義、政治鬥爭和不同國家勢力的角力，決策往往是多方妥協的產物，效率低下且充滿秘密。","status: Active","goals: 統籌『方舟計畫』的執行, 維持全球社會的基本秩序, 管理全球所剩不多的資源分配","influence: Global","publicReputation: 對外宣傳為人類唯一的希望，但因其效率低下和資源分配不公而飽受民眾詬病。","internalCulture: 官僚、階級森嚴、充滿秘密主義，內部派系林立，代表著舊時代國家力量的延續。","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"全球資源再利用總署 (GRRA)","nodeType":"organization","metadata":["name: 全球資源再利用總署 (GRRA)","type: Government Agency","description: 在資源極度匱乏的背景下應運而生的超級機構。GRRA最初只是個回收部門，但現在他們控制著從廢棄的太空站、冰封的城市，到每個人生活垃圾的一切「可回收物質」的定義權、所有權和分配權。他們是原材料的絕對壟斷者，對「方舟計畫」的建造進度有著舉足輕重的影響。","status: Active","goals: 最大化回收地球上所有可用資源, 壟斷原材料供應鏈, 在『方舟計畫』中爭取最大話語權","influence: Global","publicReputation: 被民眾視為冷酷的資源掠奪者，但又不得不依賴他們提供的再生資源。是一個讓人又敬又畏的存在。","internalCulture: 極端務實、高效、冷酷無情，信奉結果主義，對浪費零容忍。內部結構類似軍隊和大型企業的結合體。","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"蓋亞的遺民","nodeType":"organization","metadata":["name: 蓋亞的遺民","type: Secret Society","description: 一個極端的環保主義與地球本土主義結合的地下組織。他們堅信人類的命運與地球緊密相連，逃離是一種懦弱的背叛。他們視「方舟計畫」為最大的罪惡，致力於用各種手段（包括破壞）來阻止艦隊的建造。","status: Active","goals: 阻止『方舟計畫』, 迫使人類將資源用於修復地球生態, 喚醒民眾對地球的『信仰』","influence: Underground","publicReputation: 被UEG定義為恐怖組織，但在部分絕望的民眾中被視為英雄或先知。","internalCulture: 充滿理想主義和宗教狂熱的氛圍，成員背景複雜，從狂熱信徒到頂尖科學家都有，組織嚴密且行動力強。","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"科技的傲慢與自然的懲罰","nodeType":"theme","metadata":["name: 科技的傲慢與自然的懲罰","type: Main Theme","description: 故事的核心哲學思辨，探討人類過度依賴科技而忽視自然規律，最終導致自我毀滅的困境。主題表現為一場慢性的、不可逆的衰亡，而非突發災難。重點在於『解藥即是新毒藥』的惡性循環、人類『自然直覺』的喪失，以及在自然的『非人格化』審判面前，科技作為『詛咒』與『方舟』的雙重矛盾。","status: Introduced","importance: Core","currentDevelopment: 已確立核心概念和四大表現要點","symbolism: 人工除雲塔, 超級AI, 冰封的城市, 方舟計畫本身"]}
{"type":"node","name":"學者之家養老社區","nodeType":"setting","metadata":["name: 學者之家養老社區","type: Building","description: 位於UEG亞洲總部附近，專為對人類有卓越貢獻的科學家、學者和官員提供的高科技養老社區。表面上是頤養天年的地方，實則可能暗流湧動，是各種舊勢力交換情報的中心。","status: Active"]}
{"type":"node","name":"王教授","nodeType":"character","metadata":["name: 王教授","role: 退休科學家，李昂的導師","status: Active","description: 一位即將退休的老科學家，是李昂所在領域的權威，也是少數還記得『真實自然』是什麼樣的人。他相信數據，但更相信經驗和直覺。","background: 曾是『方舟計畫』前身，甚至是超級AI早期開發團隊的核心成員之一，後因不明原因『榮譽退休』。可能在暗中研究一個拯救地球的『B計畫』。","importance: Supporting Character","motivation: 不明，表面上是安度晚年，實際上可能在培養『B計畫』的接班人，或彌補過去的錯誤。","currentLocation: 學者之家養老社區"]}
{"type":"node","name":"陳靜","nodeType":"character","metadata":["name: 陳靜","role: GRRA監察員","status: Introduced","description: 年輕、果斷、極端務實的GRRA監察員，是GRRA冷酷效率的化身。她起初完全不理解李昂基於『直覺』的擔憂，認為這是浪費資源的無稽之談。","background: 其家人可能在早年的資源災難中，因官方的低效和浪費而喪生，這段經歷塑造了她對『浪費』的極度憎恨和冷酷務實的性格。她可能帶著GRRA高層的秘密任務，評估除雲塔的『回收價值』。","importance: Supporting Character","motivation: 表面上是確保資源利用效率最大化，深層動機是復仇——向所有低效、浪費的體制復仇。","currentLocation: 京津冀一號除雲塔"]}
{"type":"node","name":"李昂與王教授的師生關係","nodeType":"relationship","metadata":["name: 李昂與王教授的師生關係","relationshipType: Mentor-Student","status: Stable","intimacyLevel: 7","trustLevel: 8","conflictLevel: 2","character1Perspective: 尊敬他，視他為該領域的權威和導師，但有時不理解他為何對新技術抱持懷疑，並對他的『數據直感』天賦感到憂慮。","character2Perspective: 欣賞李昂的才華和純粹，但擔心他過於依賴技術。他似乎在有意引導李昂去思考更深層次的問題，可能是在為自己的『B計畫』尋找傳承者。","description: 一段亦師亦友的關係。王教授是李昂在專業領域的引路人，但在理念上，兩人之間存在著微妙的代溝和張力。這段關係背後可能隱藏著關於『方舟計畫』和AI的過去秘密。","character1: 李昂","character2: 王教授"]}
{"type":"node","name":"李昂與陳靜的對手關係","nodeType":"relationship","metadata":["name: 李昂與陳靜的對手關係","relationshipType: Rivalry","status: Strained","intimacyLevel: 1","trustLevel: 2","conflictLevel: 8","character1Perspective: 認為她是一個冷酷、不近人情、只看重數據和指標的官僚。對她的行事風格感到反感，但又不得不佩服她的效率。","character2Perspective: 認為李昂是一個不切實際、感情用事、甚至有點神經質的工程師。對他基於『直覺』的判斷嗤之以鼻，視其為潛在的資源浪費源頭。","description: 一段充滿衝突的對手關係。兩人因工作方式和價值觀的巨大差異而頻繁碰撞。然而，隨著危機加深，他們可能會發現彼此是唯一可以依賴的人，從而發展出一段複雜的合作關係。","character1: 李昂","character2: 陳靜"]}
{"type":"node","name":"workflow_wf_1752671516658_52nrgantj","nodeType":"workflow","metadata":["workflow_id: wf_1752671516658_52nrgantj","template_id: novel_standard_v1","template_version: 1.0.0","category: novel","status: not_started","current_stage: 0","total_stages: 4","progress: 0","created_at: 2025-07-16T13:11:56.659Z","updated_at: 2025-07-16T13:11:56.661Z","stages: [{\"id\":\"planning\",\"name\":\"規劃階段\",\"order\":0,\"status\":\"active\"},{\"id\":\"outline\",\"name\":\"大綱階段\",\"order\":1,\"status\":\"pending\"},{\"id\":\"chapter\",\"name\":\"章節階段\",\"order\":2,\"status\":\"pending\"},{\"id\":\"generation\",\"name\":\"生成階段\",\"order\":3,\"status\":\"pending\"}]","custom_purpose: BUG偵錯測試","custom_testCase: MM-BUG-001","custom_description: 測試stage_validate節點讀取問題"]}
{"type":"node","name":"stage_wf_1752671516658_52nrgantj_planning","nodeType":"stage","metadata":["workflow_id: wf_1752671516658_52nrgantj","stage_id: planning","stage_name: 規劃階段","stage_order: 0","status: active","required_node_types: [\"character\",\"setting\",\"theme\"]","optional_node_types: [\"timeline\",\"worldbuilding\"]","completion_criteria: {\"minNodes\":3,\"requiredFields\":[\"name\",\"description\",\"background\"],\"minimumQualityScore\":70,\"qualityChecks\":[{\"type\":\"character_depth\",\"description\":\"角色需要有明確的動機、衝突和成長弧線\",\"validator\":\"character_quality_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"setting_richness\",\"description\":\"設定需要包含感官細節和文化背景\",\"validator\":\"setting_quality_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"theme_clarity\",\"description\":\"主題應該具體且能通過故事元素體現\",\"validator\":\"theme_quality_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"character\":{\"description\":\"創建具有三維特質的角色：外在目標、內在需求、致命缺陷\",\"examples\":[\"主角：年輕騎士，外在目標是拯救王國，內在需求是證明自己的價值，致命缺陷是過度自信\",\"反派：墮落法師，外在目標是獲得永生，內在需求是被理解和接納，致命缺陷是無法信任他人\"],\"qualityTips\":[\"每個角色都應該有獨特的說話方式和行為模式\",\"角色的背景故事要與當前情節相關\",\"確保角色有成長和變化的空間\"],\"commonMistakes\":[\"創建完美無缺的角色\",\"角色動機不明確或不合理\",\"所有角色說話方式相同\"]},\"setting\":{\"description\":\"描述不僅是地點，更是氛圍和情感的載體\",\"examples\":[\"古老的圖書館：塵埃在陽光中飛舞，羊皮紙的味道混合著蠟燭的香氣，暗示著知識的神秘和時間的流逝\",\"戰場：泥濘的土地，鐵鏽和血腥的味道，遠處的號角聲，體現戰爭的殘酷和英雄主義的複雜性\"],\"qualityTips\":[\"使用五感描述來增強沉浸感\",\"讓環境反映角色的內心狀態\",\"考慮設定對情節發展的影響\"],\"commonMistakes\":[\"過度描述無關緊要的細節\",\"設定與故事氛圍不符\",\"忽略環境對角色行為的影響\"]},\"theme\":{\"description\":\"主題應該通過角色行動和情節發展自然呈現\",\"examples\":[\"成長主題：通過主角面對挑戰、犯錯、學習的過程來體現\",\"友誼主題：通過角色間的互助、衝突、和解來展現\"],\"qualityTips\":[\"避免直接說教，讓主題通過故事自然流露\",\"確保主題與角色弧線和情節發展一致\",\"可以有多個相關的次主題\"],\"commonMistakes\":[\"主題過於抽象或模糊\",\"強行插入主題相關的對話\",\"主題與故事內容脫節\"]}}}","estimated_duration: 1-2 weeks","created_at: 2025-07-16T13:11:56.662Z"]}
{"type":"node","name":"stage_wf_1752671516658_52nrgantj_outline","nodeType":"stage","metadata":["workflow_id: wf_1752671516658_52nrgantj","stage_id: outline","stage_name: 大綱階段","stage_order: 1","status: pending","required_node_types: [\"plotarc\",\"timeline\"]","optional_node_types: [\"conflict\",\"theme\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"main_plot\",\"key_events\"],\"minimumQualityScore\":75,\"qualityChecks\":[{\"type\":\"plot_structure\",\"description\":\"情節結構需要有清晰的起承轉合\",\"validator\":\"plot_structure_validator\",\"weight\":0.5,\"required\":true},{\"type\":\"pacing_balance\",\"description\":\"節奏安排需要有張弛有度的變化\",\"validator\":\"pacing_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"conflict_escalation\",\"description\":\"衝突需要有合理的升級和解決\",\"validator\":\"conflict_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"plotarc\":{\"description\":\"建立引人入勝的情節弧線，包含起因、發展、高潮、結局\",\"examples\":[\"三幕結構：建立(25%) → 對抗(50%) → 解決(25%)\",\"英雄之旅：平凡世界 → 冒險召喚 → 拒絕召喚 → 遇見導師 → 跨越門檻...\"],\"qualityTips\":[\"確保每個情節點都推動故事前進\",\"高潮應該是情感和行動的雙重頂點\",\"結局要回應開頭提出的問題\"],\"commonMistakes\":[\"中段情節拖沓，缺乏推進力\",\"高潮來得太突然或太平淡\",\"結局過於匆忙或留下太多未解之謎\"]},\"timeline\":{\"description\":\"建立清晰的時間線，確保事件邏輯合理\",\"examples\":[\"線性時間線：按時間順序發展的故事\",\"非線性時間線：使用倒敘、插敘等技巧\"],\"qualityTips\":[\"重要事件之間要有合理的時間間隔\",\"考慮季節、天氣對故事氛圍的影響\",\"確保角色成長與時間推移相符\"],\"commonMistakes\":[\"時間跳躍過於頻繁或突兀\",\"忽略時間對角色和環境的影響\",\"前後時間設定不一致\"]}}}","estimated_duration: 1 week","created_at: 2025-07-16T13:11:56.662Z"]}
{"type":"node","name":"stage_wf_1752671516658_52nrgantj_chapter","nodeType":"stage","metadata":["workflow_id: wf_1752671516658_52nrgantj","stage_id: chapter","stage_name: 章節階段","stage_order: 2","status: pending","required_node_types: [\"scene\",\"chapter\"]","optional_node_types: [\"dialogue\",\"description\"]","completion_criteria: {\"minNodes\":5,\"requiredFields\":[\"scene_setting\",\"characters_involved\"],\"minimumQualityScore\":80,\"qualityChecks\":[{\"type\":\"scene_depth\",\"description\":\"場景需要有豐富的感官細節和情感層次\",\"validator\":\"scene_depth_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"dialogue_authenticity\",\"description\":\"對話需要符合角色性格且推動情節\",\"validator\":\"dialogue_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"pacing_control\",\"description\":\"章節節奏需要有適當的張弛變化\",\"validator\":\"chapter_pacing_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"scene\":{\"description\":\"創建生動的場景，平衡行動、對話、描述和內心獨白\",\"examples\":[\"行動場景：劍鬥 - 快節奏，短句，感官細節，情緒張力\",\"對話場景：談判 - 角色動機衝突，潛台詞，肢體語言\",\"反思場景：獨處 - 內心掙扎，回憶片段，情感深度\"],\"qualityTips\":[\"每個場景都要有明確的目的和衝突\",\"使用具體的感官細節增強沉浸感\",\"確保場景推動情節或角色發展\",\"注意場景間的過渡和連接\"],\"commonMistakes\":[\"場景過長或過短，節奏失衡\",\"缺乏具體的環境描述\",\"場景目的不明確，流於表面\",\"忽略角色在場景中的情感變化\"]},\"chapter\":{\"description\":\"構建完整的章節，有開頭鉤子、發展和結尾懸念\",\"examples\":[\"開頭鉤子：突發事件、謎團、衝突、有趣對話\",\"章節發展：推進主線、深化角色、揭示信息\",\"結尾懸念：未解問題、新的威脅、情感轉折\"],\"qualityTips\":[\"每章都要推進整體故事進程\",\"保持讀者的閱讀興趣和期待\",\"平衡不同類型的內容（行動、對話、描述）\",\"確保章節長度適中且一致\"],\"commonMistakes\":[\"章節缺乏內在結構和節奏\",\"結尾過於平淡，缺乏懸念\",\"章節間缺乏連貫性\",\"過度依賴對話或描述\"]}}}","estimated_duration: 2-3 weeks","created_at: 2025-07-16T13:11:56.662Z"]}
{"type":"node","name":"stage_wf_1752671516658_52nrgantj_generation","nodeType":"stage","metadata":["workflow_id: wf_1752671516658_52nrgantj","stage_id: generation","stage_name: 生成階段","stage_order: 3","status: pending","required_node_types: [\"chapter\"]","optional_node_types: [\"revision_notes\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"content\",\"word_count\"],\"minimumQualityScore\":85,\"qualityChecks\":[{\"type\":\"narrative_consistency\",\"description\":\"敘述風格和視角需要保持一致\",\"validator\":\"narrative_consistency_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"character_voice\",\"description\":\"角色聲音需要獨特且一致\",\"validator\":\"character_voice_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"thematic_integration\",\"description\":\"主題需要自然融入故事中\",\"validator\":\"theme_integration_validator\",\"weight\":0.2,\"required\":true},{\"type\":\"prose_quality\",\"description\":\"文筆需要流暢且富有表現力\",\"validator\":\"prose_quality_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"chapter\":{\"description\":\"完成高質量的章節內容，整合所有故事元素\",\"examples\":[\"完整章節：開頭引人入勝，中段發展充實，結尾留有懸念\",\"修訂版本：基於反饋改進的內容，提升可讀性和影響力\"],\"qualityTips\":[\"確保每章都有明確的情節進展\",\"保持敘述聲音的一致性\",\"平衡描述、對話、行動和內心獨白\",\"注意章節間的過渡和連貫性\",\"最終校對語法、拼寫和格式\"],\"commonMistakes\":[\"急於完成而忽略質量\",\"前後章節風格不一致\",\"忽略細節的打磨和完善\",\"缺乏最終的整體審視和修訂\"]}}}","estimated_duration: 4-6 weeks","created_at: 2025-07-16T13:11:56.662Z"]}
{"type":"node","name":"測試角色Alpha","nodeType":"character","metadata":["name: 測試角色Alpha","role: protagonist","status: Active","description: 專門用於BUG測試的最小化角色設定","background: 這是一個用於驗證stage_validate功能的測試角色","traits: 測試屬性","importance: Protagonist","motivation: 測試系統功能","currentLocation: 測試地點"]}
{"type":"node","name":"測試地點","nodeType":"setting","metadata":["name: 測試地點","type: Indoor","description: 用於BUG測試的最小化地點設定","status: Active","significance: Minor"]}
{"type":"node","name":"測試角色Beta","nodeType":"character","metadata":["name: 測試角色Beta","role: protagonist","status: Active","description: 專門用於BUG測試的最小化角色設定","background: 這是一個用於驗證stage_validate功能的測試角色","traits: 測試屬性","importance: Protagonist","motivation: 測試系統功能","currentLocation: 測試地點"]}
{"type":"node","name":"測試主題","nodeType":"theme","metadata":["name: 測試主題","type: Main Theme","description: 用於BUG測試的最小化主題設定","status: Developing","importance: Core","currentDevelopment: 正在測試系統功能"]}
{"type":"node","name":"塔的嗡鳴","nodeType":"event","metadata":["name: 塔的嗡鳴","type: Plot Event","description: 在一次對『京津冀一號除雲塔』的常規系統維護中，主角李昂透過他獨特的『數據直感』能力，在海量的、看似正常的系統數據流中，『聽』到了一絲微弱且不祥的『嗡鳴』——一個現有AI和所有監測儀器都無法識別的系統性異常。這個發現讓他深感不安，也為整個故事埋下了第一顆不安的種子。","importance: Plot Advancement","emotionalTone: Mysterious","participants: 李昂","location: 京津冀一號除雲塔","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"冰霜下的交易","nodeType":"event","metadata":["name: 冰霜下的交易","type: Setting Description","description: 李昂下班後，進入一個位於地下供暖管道系統中的半合法市集。他試圖用自己寶貴的能源配給，換取一些來自小型生態溫室的『奢侈品』，如天然番茄或咖啡豆。這個場景將生動地展現資源配給制度下的社會樣貌、黑市的存在，以及普通人在壓抑環境中的一點生活追求。","importance: Background/Atmosphere","emotionalTone: Melancholic","participants: 李昂","location: 地下能源黑市","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"地下能源黑市","nodeType":"setting","metadata":["name: 地下能源黑市","type: Public Space","description: 位於城市廢棄的地下供暖管道系統中，是一個半地下的交易場所。在這裡，人們可以用官方的能源配給或以物易物的方式，交換到官方渠道難以獲得的各種商品，從天然食物到違禁的舊時代娛樂數據，應有盡有。","status: Active","atmosphere: 空氣中混雜著濕氣、機油和各種食物的香料味，既有底層社會的活力，也充滿了壓抑和不信任感。"]}
{"type":"node","name":"機器中的幽靈","nodeType":"event","metadata":["name: 機器中的幽靈","type: Plot Event","description: 李昂的『數據直感』捕捉到一次前所未有的系統性異常——一個他稱之為『幽靈』的微弱但持續的『噪音』。他正式上報此事，但其報告被上級和AI的診斷報告駁回，認為是他的神經過敏或設備的傳感器誤差。這次事件標誌著他與體制的第一次正面衝突，並促使他去尋找唯一可能理解他的王教授求助。","importance: Major Turning Point","emotionalTone: Tense","participants: 李昂, 陳靜","location: 京津冀一號除雲塔","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"哈爾濱的長夜","nodeType":"event","metadata":["name: 哈爾濱的長夜","type: Historical Event","description: 一場史無前例的強烈太陽風暴，疊加了李昂之前發現的『幽靈』系統異常，導致以哈爾濱為中心的東北除雲網絡大規模、連鎖性崩潰。在缺乏人工除雲的保護下，城市氣溫在短短數小時內驟降至毀滅性的零下100攝氏度以下。這場災難透過無數的個人終端直播給全世界，成為人類進入全球變冷時代以來最慘烈的悲劇。","importance: Climactic","consequences: 大量人員傷亡, UEG的公信力受到致命打擊, 全球民眾陷入恐慌, 為『方舟計畫』的公佈埋下伏筆","emotionalTone: Tragic","location: 哈爾濱","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"哈爾濱","nodeType":"setting","metadata":["name: 哈爾濱","type: Urban","description: 位於東北地區的重工業城市。在全球變冷時代，依靠區域性的除雲網絡維持著運轉。在『哈爾濱長夜』事件中，因除雲網絡崩潰而被極低溫摧毀，成為人類末日危機的標誌性悲劇地點。","status: Destroyed","atmosphere: 災難前是充滿歷史感的北方城市，災難後則變為死寂、悲傷的冰雪紀念碑。"]}
{"type":"node","name":"方舟揭示","nodeType":"event","metadata":["name: 方舟揭示","type: Revelation","description: 在『哈爾濱長夜』災難引發的全球性壓力和質疑聲中，聯合地球政府(UEG)再也無法隱瞞真相。通過全球緊急廣播，UEG最高領導人向全人類公佈了超級AI的末日預言，以及他們已秘密進行多年的『方舟計畫』。這個消息徹底擊碎了民眾的僥倖心理，世界從此進入一個新的、更加混亂的時代。","importance: Major Turning Point","consequences: 全球秩序開始崩潰, 引發大規模的社會動盪和絕望情緒, 故事正式進入第二部『大動員』","emotionalTone: Hopeful","participants: 聯合地球政府 (UEG)","location: 聯合地球政府 (UEG)","relatedPlotArcs: 第一部：溫水煮青蛙"]}
{"type":"node","name":"第二部：大動員","nodeType":"plotarc","metadata":["name: 第二部：大動員","type: Main Plot","description: 在『方舟計畫』公佈後，全人類被動員起來，為建造星際艦隊而掙扎。本篇章將集中展現硬科幻的建造細節、因『貢獻度積分』引發的劇烈社會矛盾、來自『蓋亞的遺民』的外部威脅，並在結尾以一次災難性的技術失敗，將方舟計畫推向破產邊緣，為第三部『裂痕』的到來埋下伏筆。","status: Planning","importance: Critical","progressPercentage: 0","mainCharacters: 李昂, 陳靜, 王教授"]}
{"type":"node","name":"第三部：裂痕","nodeType":"plotarc","metadata":["name: 第三部：裂痕","type: Main Plot","description: 在船塢災難後，希望變得渺茫，社會秩序開始瓦解。本篇章將聚焦於信任的崩潰、資源的爭奪和殘酷的道德困境。主角李昂將在調查災難真相的過程中，被迫逃亡，並最終發現一個足以顛覆整個『方舟計畫』的驚天秘密，使他陷入關於『真相』與『謊言』的終極抉擇。","status: Planning","importance: Critical","progressPercentage: 0","mainCharacters: 李昂, 陳靜, 王教授"]}
{"type":"node","name":"第四部：出埃及記","nodeType":"plotarc","metadata":["name: 第四部：出埃及記","type: Main Plot","description: 在李昂做出關鍵抉擇後，故事進入最終章。本篇章將根據主角的選擇，展現人類社會在末日面前最後的掙扎、混亂與犧牲。最終，一艘小型的『種子船』將承載著人類文明的火種離開地球，駛向未知的宇宙，同時留下一個關於未來的、開放式的結局。","status: Planning","importance: Critical","progressPercentage: 0","mainCharacters: 李昂, 陳靜"]}
{"name":"故事主時間線","metadata":["type: Master Timeline","description: 記錄了『即刻作戰_全員逃離地球』故事中的關鍵事件及其發生時間，以2150年『方舟揭示』為核心基準點。","2148年: 塔的嗡鳴、機器中的幽靈","2149年: （留白）危機發酵期","2150年 冬: 哈爾濱的長夜","2150年 末: 方舟揭示，第一部結束","2151-2155年: 第二部 - 大動員，全球建艦時期","2155年: 龍骨上的裂痕，第二部結束","2156年: 第三部 - 裂痕，信任崩潰與秘密揭示","2157年: 第四部 - 出埃及記，最終結局"],"nodeType":"timeline","type":"node"}
{"type":"node","name":"龍骨船塢","nodeType":"setting","metadata":["name: 龍骨船塢","type: Industrial","description: 位於戈壁沙漠的巨型星艦總裝基地，是『方舟計畫』的核心工程所在地。無數工程師、科學家和軍人在此聚集，為了建造人類最後的希望而奮鬥。","status: Active","atmosphere: 充滿了壓倒性的工業美學、希望與絕望的混合氣息。白天是人造太陽下繁忙的景象，夜晚則是星空下鋼鐵巨獸的剪影。"]}
{"type":"node","name":"伊萬諾夫 (Ivanov)","nodeType":"character","metadata":["name: 伊萬諾夫 (Ivanov)","role: 星艦能源核心專家","status: Deceased","description: 一位經驗豐富、才華橫溢但嗜酒如命的能源核心專家。性格豪放不羈，是李昂在船塢結識的第一個朋友，也是技術上的重要夥伴。","background: 前蘇聯時代太空計畫科學家的後代，繼承了家族對星辰的渴望和對酒精的熱愛。在船塢災難中，為了手動關閉失控的反應核心而犧牲。","importance: Minor Character","motivation: 在地球毀滅前，親手點燃一艘能飛向星海的船。","currentLocation: 龍骨船塢"]}
{"type":"node","name":"佐藤博士 (Dr. Sato)","nodeType":"character","metadata":["name: 佐藤博士 (Dr. Sato)","role: 星艦生態循環專家","status: Active","description: 一位年輕、聰明且富有同情心的女科學家，負責『方舟』的生態循環系統。她常常提醒李昂和伊萬諾夫，他們的系統最終是為脆弱的生命服務的。","background: 出生於日本一個著名的生物學世家，是生態循環和小型生物圈領域的天才。她對生命的看法與工程師們的機械論截然不同。","importance: Minor Character","motivation: 建造一個能讓生命在冰冷宇宙中延續下去的『諾亞方舟』。","currentLocation: 龍骨船塢"]}
{"type":"node","name":"趙上校 (Colonel Zhao)","nodeType":"character","metadata":["name: 趙上校 (Colonel Zhao)","role: 龍骨船塢軍方監管員","status: Active","description: 一位不苟言笑、紀律嚴明的軍方監管人員，負責監督『龍骨船塢』的建造進度和安全。他將所有問題都視為對任務的威脅。","background: 在UEG成立前的國家軍隊中服役，經歷過資源戰爭的殘酷。他對文職人員的『軟弱』和『低效』抱持著深深的不信任。","importance: Minor Character","motivation: 不惜一切代價，確保至少有一艘船能按時完工，完成他的使命。","currentLocation: 龍骨船塢"]}
{"type":"node","name":"委員會主席","nodeType":"character","metadata":["name: 委員會主席","role: UEG獨立調查委員會主席","status: Active","description: 一位資深的UEG官僚，船塢災難獨立調查委員會的主席。他的目標不是尋找真相，而是為災難找到一個可以被接受的『解釋』和一個合適的『替罪羊』。","background: 在UEG的官僚體系中摸爬滾打多年，深諳政治鬥爭和權力平衡之術。他本人可能並不邪惡，但他堅信『穩定』高於一切，包括真相。","importance: Minor Character","motivation: 維護UEG的統治穩定，平息民憤，確保『方舟計畫』的權威性不受動搖。","currentLocation: 聯合地球政府 (UEG)"]}
{"type":"node","name":"黑市攤主","nodeType":"character","metadata":["name: 黑市攤主","role: 地下商人","status: Active","description: 在地下黑市經營一個小攤位的神秘老人。他用自己溫室裡種植的『奢侈品』與李昂交易，言談中充滿了對現實的嘲諷和對過去的懷念。","background: 可能是一位在『全球變冷』中失去土地和家園的老農，或是對舊時代有著深刻記憶的歷史學者。他見證了太多變遷，對官方的任何宣傳都抱持著懷疑。","importance: Minor Character","motivation: 在末日中，守住一點屬於『真實』和『自然』的東西。","currentLocation: 地下能源黑市"]}
{"nodeType":"chapter","metadata":["title: 數據的低語","part: 第一部：溫水煮青蛙","event: 塔的嗡鳴","pov: 李昂","goal: 建立李昂的日常生活和性格特點；展示2150年的科技社會面貌；首次引入李昂的『數據直感』能力；埋下核心懸念——『嗡鳴』的出現；營造一種表面平靜，實則暗流涌動的氛圍。","start: 從李昂的清晨儀式開始，展現他對秩序的追求。","end: 李昂在數據中聽到『嗡鳴』，但所有儀器都顯示正常，留下懸念。"],"name":"第一章 數據的低語","type":"node"}
{"nodeType":"chapter","metadata":["title: 冰霜下的真實","part: 第一部：溫水煮青蛙","event: 冰霜下的交易","pov: 李昂","goal: 展現地表世界與地下黑市的強烈對比；透過李昂的內心獨白，解釋新鮮食物的稀有性和黑市的存在意義；深化李昂對『真實』的渴望，以及他對『秩序』的執著與『混亂』的地下世界之間的矛盾；為後續的『機器中的幽靈』事件做鋪墊，暗示李昂內心的不安。","start: 李昂離開除雲塔，前往地下黑市。","end: 李昂成功換取番茄，品嚐真實的滋味，但內心的『嗡鳴』依然存在。"],"name":"第二章 冰霜下的真實","type":"node"}
{"type":"node","name":"林阿姨","nodeType":"character","metadata":["name: 林阿姨","role: 清潔機器人維護員","status: Active","description: 李昂公寓樓的清潔機器人維護員，一位年邁但眼神銳利的老婦人。她代表著舊時代的『人情味』和『經驗智慧』，與李昂的數據化生活形成對比。她可能會抱怨機器人或公寓系統的『小毛病』。","background: 一位在舊時代成長起來的普通市民，經歷過全球變冷初期的混亂。她對科技的依賴有著清醒的認識，但更相信人與人之間的溫情和經驗的價值。","importance: Minor Character","motivation: 在末日中，盡力維持自己和周圍人的生活秩序，並傳遞一些被數據化世界所遺忘的『人情味』。","currentLocation: 李昂的公寓樓"]}
{"type":"node","name":"李昂的公寓樓","nodeType":"setting","metadata":["name: 李昂的公寓樓","type: Building","description: 李昂居住的公寓樓，位於城市中心區，是UEG為高級技術人員提供的標準化住所。樓內一切都由AI和機器人管理，強調效率和秩序。","status: Active","atmosphere: 高度自動化、極簡、潔淨，充滿了科技的冰冷感，但偶爾會因為林阿姨的存在而透出一絲人情味。"]}
{"type":"node","name":"瘦猴","nodeType":"character","metadata":["name: 瘦猴","role: 情報販子","status: Active","description: 地下黑市的情報販子，一個機靈、消息靈通但行為鬼祟的年輕人。他代表著地下世界的『信息流動』和『生存智慧』。他對李昂的出現感到好奇，並可能主動接近他。","background: 在地下黑市中摸爬滾打多年，對各種信息和生存法則有著敏銳的嗅覺。他沒有固定的立場，只為利益服務，但有時也會被一些『有趣』的事情所吸引。","importance: Minor Character","motivation: 在混亂的末日中生存下去，並從信息交易中獲取利益。","currentLocation: 地下能源黑市"]}
{"from":"stage_wf_1752622336182_2jxmv2lpb_planning","to":"stage_wf_1752622336182_2jxmv2lpb_outline","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1752622336182_2jxmv2lpb_outline","to":"stage_wf_1752622336182_2jxmv2lpb_chapter","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1752622336182_2jxmv2lpb_chapter","to":"stage_wf_1752622336182_2jxmv2lpb_generation","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_planning","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_outline","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_chapter","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752622336182_2jxmv2lpb","to":"stage_wf_1752622336182_2jxmv2lpb_generation","edgeType":"contains_stage","weight":1,"type":"edge"}
{"type":"edge","from":"李昂","to":"京津冀一號除雲塔","edgeType":"located_in"}
{"type":"edge","from":"聯合地球政府 (UEG)","to":"第一部：溫水煮青蛙","edgeType":"involved_in"}
{"type":"edge","from":"全球資源再利用總署 (GRRA)","to":"第一部：溫水煮青蛙","edgeType":"involved_in"}
{"type":"edge","from":"蓋亞的遺民","to":"第一部：溫水煮青蛙","edgeType":"involved_in"}
{"type":"edge","from":"王教授","to":"學者之家養老社區","edgeType":"located_in"}
{"type":"edge","from":"陳靜","to":"京津冀一號除雲塔","edgeType":"located_in"}
{"type":"edge","from":"李昂與王教授的師生關係","to":"李昂","edgeType":"has_relationship_with"}
{"type":"edge","from":"李昂與王教授的師生關係","to":"王教授","edgeType":"has_relationship_with"}
{"type":"edge","from":"李昂與陳靜的對手關係","to":"李昂","edgeType":"has_relationship_with"}
{"type":"edge","from":"李昂與陳靜的對手關係","to":"陳靜","edgeType":"has_relationship_with"}
{"from":"stage_wf_1752671516658_52nrgantj_planning","to":"stage_wf_1752671516658_52nrgantj_outline","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1752671516658_52nrgantj_outline","to":"stage_wf_1752671516658_52nrgantj_chapter","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1752671516658_52nrgantj_chapter","to":"stage_wf_1752671516658_52nrgantj_generation","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752671516658_52nrgantj","to":"stage_wf_1752671516658_52nrgantj_planning","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752671516658_52nrgantj","to":"stage_wf_1752671516658_52nrgantj_outline","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752671516658_52nrgantj","to":"stage_wf_1752671516658_52nrgantj_chapter","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1752671516658_52nrgantj","to":"stage_wf_1752671516658_52nrgantj_generation","edgeType":"contains_stage","weight":1,"type":"edge"}
{"type":"edge","from":"測試角色Beta","to":"測試地點","edgeType":"located_in"}
{"type":"edge","from":"塔的嗡鳴","to":"李昂","edgeType":"participates_in"}
{"type":"edge","from":"塔的嗡鳴","to":"京津冀一號除雲塔","edgeType":"occurs_at"}
{"type":"edge","from":"塔的嗡鳴","to":"第一部：溫水煮青蛙","edgeType":"part_of"}
{"type":"edge","from":"機器中的幽靈","to":"李昂","edgeType":"participates_in"}
{"type":"edge","from":"機器中的幽靈","to":"陳靜","edgeType":"participates_in"}
{"type":"edge","from":"機器中的幽靈","to":"京津冀一號除雲塔","edgeType":"occurs_at"}
{"type":"edge","from":"機器中的幽靈","to":"第一部：溫水煮青蛙","edgeType":"part_of"}
{"type":"edge","from":"方舟揭示","to":"聯合地球政府 (UEG)","edgeType":"participates_in"}
{"type":"edge","from":"方舟揭示","to":"聯合地球政府 (UEG)","edgeType":"occurs_at"}
{"type":"edge","from":"方舟揭示","to":"第一部：溫水煮青蛙","edgeType":"part_of"}
{"type":"edge","from":"第二部：大動員","to":"李昂","edgeType":"participates_in"}
{"type":"edge","from":"第二部：大動員","to":"陳靜","edgeType":"participates_in"}
{"type":"edge","from":"第二部：大動員","to":"王教授","edgeType":"participates_in"}
{"type":"edge","from":"第三部：裂痕","to":"李昂","edgeType":"participates_in"}
{"type":"edge","from":"第三部：裂痕","to":"陳靜","edgeType":"participates_in"}
{"type":"edge","from":"第三部：裂痕","to":"王教授","edgeType":"participates_in"}
{"type":"edge","from":"第四部：出埃及記","to":"李昂","edgeType":"participates_in"}
{"type":"edge","from":"第四部：出埃及記","to":"陳靜","edgeType":"participates_in"}
{"type":"edge","from":"伊萬諾夫 (Ivanov)","to":"龍骨船塢","edgeType":"located_in"}
{"type":"edge","from":"佐藤博士 (Dr. Sato)","to":"龍骨船塢","edgeType":"located_in"}
{"type":"edge","from":"趙上校 (Colonel Zhao)","to":"龍骨船塢","edgeType":"located_in"}
{"type":"edge","from":"委員會主席","to":"聯合地球政府 (UEG)","edgeType":"located_in"}
{"type":"edge","from":"黑市攤主","to":"地下能源黑市","edgeType":"located_in"}
{"type":"edge","from":"瘦猴","to":"地下能源黑市","edgeType":"located_in"}
