/** @type {import('jest').Config} */
export default {
  preset: 'ts-jest/presets/default-esm',
  extensionsToTreatAsEsm: ['.ts'],
  testEnvironment: 'node',
  
  // 測試文件匹配模式
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.test.ts',
    '<rootDir>/tests/**/*.test.ts'
  ],
  
  // 模塊路徑映射（與tsconfig.json保持一致）
  moduleNameMapper: {
    '^@core/(.*)$': '<rootDir>/dist/core/$1',
    '^@infrastructure/(.*)$': '<rootDir>/dist/infrastructure/$1',
    '^@application/(.*)$': '<rootDir>/dist/application/$1',
    '^@integration/(.*)$': '<rootDir>/dist/integration/$1',
    '^@shared/(.*)$': '<rootDir>/dist/shared/$1',
    '^@data/(.*)$': '<rootDir>/dist/data/$1',
    '^@config/(.*)$': '<rootDir>/dist/config/$1',
    // ES模塊支持
    '^(\\.{1,2}/.*)\\.js$': '$1'
  },
  
  // 覆蓋率配置
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/__tests__/**',
    '!src/index.ts'
  ],
  
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  
  // 覆蓋率閾值
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    },
    // 工作流程相關模塊要求更高的覆蓋率
    './src/core/workflow/': {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95
    },
    './src/integration/tools/handlers/WorkflowToolHandler.ts': {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95
    },
    './src/infrastructure/storage/CachedJsonLineStorage.ts': {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },
  
  // 設置文件
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  
  // 測試超時
  testTimeout: 10000,
  
  // 轉換配置
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      useESM: true,
      tsconfig: {
        module: 'esnext'
      }
    }]
  },

  // 轉換忽略模式
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$))'
  ],
  
  // 忽略的模式
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/'
  ],
  
  // 清理模擬
  clearMocks: true,
  restoreMocks: true,
  
  // 詳細輸出
  verbose: true
};
