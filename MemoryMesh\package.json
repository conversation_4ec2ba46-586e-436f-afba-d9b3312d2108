{"name": "memorymesh", "version": "0.2.8", "description": "An MCP server that uses a knowledge graph to store and recall structured memory for AI models", "license": "MIT", "author": "CheMiguel23", "homepage": "https://github.com/CheMiguel23/memorymesh", "bugs": "https://github.com/CheMiguel23/memorymesh/issues", "type": "module", "main": "dist/index.js", "bin": {"memorymesh": "./dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && tsc-alias && shx chmod +x dist/*.js && copyfiles -u 1 src/data/schemas/*.json src/data/*.json dist/", "prepare": "npm run build", "watch": "tsc --watch", "start": "ts-node --esm src/index.ts", "start:claude": "ts-node --esm src/index.ts", "start:prod": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:workflow": "jest --testPathPattern=workflow"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0"}, "devDependencies": {"@types/node": "^20.11.24", "@types/jest": "^29.5.8", "copyfiles": "^2.4.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "shx": "^0.3.4", "ts-node": "^10.9.2", "tsc-alias": "^1.8.8", "typescript": "^5.3.3"}}