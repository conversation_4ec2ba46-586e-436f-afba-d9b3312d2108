import { JsonLineStorage } from './JsonLineStorage.js';
import type { IExtendedStorage, WorkflowState, StageState, CacheConfig, IndexConfig, BatchOperation, BatchResult, StoragePlugin, CustomQuery, MigrationResult, StorageEventHandler } from './IExtendedStorage.js';
import type { Node } from '../../core/index.js';
/**
 * 帶緩存的JSON Line存儲實現
 * 在原有JsonLineStorage基礎上添加性能優化和擴展功能
 */
export declare class CachedJsonLineStorage extends JsonLineStorage implements IExtendedStorage {
    private nodeCache;
    private workflowCache;
    private stageCache;
    private eventEmitter;
    private nodeTypeIndex;
    private workflowNodeIndex;
    private cacheTimestamps;
    private cleanupInterval;
    private memoryUsageThreshold;
    private cacheConfig;
    private indexConfig;
    private plugins;
    constructor();
    /**
     * 增量更新節點實現
     */
    updateNodesIncremental(updates: Partial<Node>[]): Promise<void>;
    /**
     * 批量操作實現
     */
    batchOperation(operations: BatchOperation[]): Promise<BatchResult>;
    /**
     * 緩存配置
     */
    configureCaching(config: CacheConfig): Promise<void>;
    /**
     * 緩存失效
     */
    invalidateCache(keys?: string[]): Promise<void>;
    saveWorkflowState(workflow: WorkflowState): Promise<void>;
    loadWorkflowState(workflowId: string): Promise<WorkflowState | null>;
    listWorkflows(): Promise<WorkflowState[]>;
    saveStageState(stage: StageState): Promise<void>;
    loadStageStates(workflowId: string): Promise<StageState[]>;
    configureIndexes(config: IndexConfig): Promise<void>;
    queryNodesByType(nodeType: string, limit?: number): Promise<Node[]>;
    queryNodesByWorkflow(workflowId: string): Promise<Node[]>;
    registerStoragePlugin(plugin: StoragePlugin): Promise<void>;
    executeCustomQuery(query: CustomQuery): Promise<any>;
    migrateData(fromVersion: string, toVersion: string): Promise<MigrationResult>;
    onStorageEvent(event: string, handler: StorageEventHandler): void;
    private initializeIndexes;
    private rebuildIndexes;
    private updateNodeIndexes;
    private removeFromIndexes;
    private extractWorkflowId;
    private emitStorageEvent;
    /**
     * 啟動定期清理計時器
     */
    private startCleanupTimer;
    /**
     * 執行緩存清理
     */
    private performCleanup;
    /**
     * 緊急清理（內存使用過高時）
     */
    private performEmergencyCleanup;
    /**
     * 驅逐最舊的緩存條目
     */
    private evictOldestEntries;
    /**
     * 獲取緩存統計信息
     */
    getCacheStats(): {
        nodeCache: number;
        workflowCache: number;
        stageCache: number;
        memoryUsage: NodeJS.MemoryUsage;
        indexSizes: Record<string, number>;
    };
    /**
     * 清理所有資源
     */
    cleanup(): void;
}
