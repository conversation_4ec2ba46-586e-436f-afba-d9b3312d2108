{"version": 3, "file": "DynamicToolHandler.js", "sourceRoot": "", "sources": ["../../../../src/integration/tools/handlers/DynamicToolHandler.ts"], "names": [], "mappings": "AAAA,2CAA2C;AAE3C,OAAO,EAAC,eAAe,EAAC,MAAM,sBAAsB,CAAC;AACrD,OAAO,EAAC,kBAAkB,EAAC,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAC,kBAAkB,EAAE,eAAe,EAAC,MAAM,kBAAkB,CAAC;AAErE,MAAM,OAAO,kBAAmB,SAAQ,eAAe;IACnD,KAAK,CAAC,UAAU,CAAC,IAAY,EAAE,IAAyB;QACpD,IAAI,CAAC;YACD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7B,MAAM,UAAU,GAAG,MAAM,kBAAkB,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAEnG,IAAI,UAAU,EAAE,UAAU,EAAE,OAAO,KAAK,SAAS,EAAE,CAAC;gBAChD,OAAO,UAAU,CAAC;YACtB,CAAC;YAED,OAAO,kBAAkB,CAAC;gBACtB,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,0BAA0B,IAAI,EAAE;aAChD,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,eAAe,CAAC;gBACnB,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;gBACxE,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAC;gBAC/B,WAAW,EAAE;oBACT,mDAAmD;oBACnD,kDAAkD;iBACrD;gBACD,aAAa,EAAE;oBACX,4DAA4D;iBAC/D;aACJ,CAAC,CAAC;QACP,CAAC;IACL,CAAC;CACJ"}