{"version": 3, "file": "GraphValidator.js", "sourceRoot": "", "sources": ["../../../src/core/graph/GraphValidator.ts"], "names": [], "mappings": "AAAA,mCAAmC;AAKnC,OAAO,EAAC,eAAe,EAAC,MAAM,sBAAsB,CAAC;AACrD,OAAO,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAC,qBAAqB,EAAC,MAAM,4BAA4B,CAAC;AAEjE;;GAEG;AACH,MAAM,OAAO,cAAc;IACvB;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAAC,IAAY;QACzC,cAAc;QACd,MAAM,eAAe,GAAG;YACpB,YAAY,EAAY,UAAU;YAClC,MAAM,EAAmB,MAAM;YAC/B,gBAAgB,EAAW,YAAY;YACvC,aAAa,EAAc,QAAQ;YACnC,UAAU,EAAiB,MAAM;YACjC,KAAK,EAAoB,MAAM;YAC/B,cAAc,CAAa,UAAU;SACxC,CAAC;QAEF,OAAO,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CAAC,QAAgB;QAChD,MAAM,cAAc,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;QAC5E,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAE/D,IAAI,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YAClD,OAAO,WAAW,CAAC;QACvB,CAAC;aAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YACvD,OAAO,SAAS,CAAC;QACrB,CAAC;aAAM,CAAC;YACJ,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IACD;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,KAAY,EAAE,QAAgB;QACpD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,wBAAwB,CAAC,KAAY,EAAE,QAAgB;QAC1D,YAAY;QACZ,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,wBAAwB,QAAQ,oCAAoC,CAAC,CAAC;QAC1F,CAAC;QAED,wBAAwB;QACxB,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzD,YAAY;QACZ,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAEvD,wBAAwB;QACxB,MAAM,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAE,CAAS,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACpF,MAAM,cAAc,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACjD,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CACtC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7E,CAAC,CAAC,CAAC,SAAS,CAAC;QAElB,mBAAmB;QACnB,MAAM,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAE1F,MAAM,UAAU,GAAG,qBAAqB,CAAC,gBAAgB,CAAC,aAAa,EAAE,QAAQ,EAAE;YAC/E,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,qBAAqB;YAC7D,UAAU,EAAE,KAAK,EAAE,iBAAiB;YACpC,WAAW,EAAE,WAAW;SAC3B,CAAC,CAAC;QAEH,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;YACrC,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,CAAC,CAAC;YAC9C,MAAM,cAAc,GAAG,UAAU,CAAC,cAAc,IAAI,EAAE,CAAC;YAEvD,MAAM,IAAI,KAAK,CACX,YAAY,QAAQ,QAAQ,UAAU,CAAC,WAAW,UAAU,CAAC,UAAU,CAAC,UAAW,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI;gBACzG,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO;gBAC7C,iBAAiB,CAAC,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;gBACtG,MAAM,CAAC,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;gBAC/F,OAAO,cAAc,EAAE,CAC1B,CAAC;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,KAAY,EAAE,IAAU;QAClD,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAC5B,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;YAC3B,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE;YACvB,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CACtC,EAAE,CAAC;YACA,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAC1F,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,IAAU;QACpC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAAC,IAAmB;QAC/C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACrE,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,SAAkB;QAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAClD,CAAC;QACD,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,IAAU;QACpC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC5B,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,KAAY,EAAE,KAAa;QACrD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAC5C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,KAAY;QACtC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;QAED,qBAAqB;QACrB,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;QAE/D,qBAAqB;QACrB,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAAC,KAAY,EAAE,YAAoB,GAAG;QAQhE,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,MAAM,eAAe,GAAG,gBAAgB,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAEnF,MAAM,eAAe,GAAG,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAClD,UAAU,EAAE,KAAK;YACjB,aAAa,EAAE,gBAAgB,CAAC,oBAAoB,CAAC,KAAK,CAAC;YAC3D,gBAAgB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CACxC,gBAAgB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CACvD;SACJ,CAAC,CAAC,CAAC;QAEJ,OAAO;YACH,eAAe;YACf,eAAe;SAClB,CAAC;IACN,CAAC;CACJ;AAED,+BAA+B;AAC/B,MAAM,CAAC,MAAM,EACT,kBAAkB,EAClB,wBAAwB,EACxB,sBAAsB,EACtB,sBAAsB,EACtB,wBAAwB,EACxB,sBAAsB,EACtB,sBAAsB,EACtB,sBAAsB,EACtB,sBAAsB,EACzB,GAAG,cAAc,CAAC"}