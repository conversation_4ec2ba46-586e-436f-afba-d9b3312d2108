import type { Node } from '../index.js';
import type { IExtendedStorage, WorkflowState, StageState } from '../../infrastructure/storage/IExtendedStorage.js';
import type { WorkflowTemplate } from './WorkflowTemplates.js';
import { EventEmitter } from 'events';
/**
 * 工作流程狀態變更事件
 */
export interface WorkflowStateChangeEvent {
    workflowId: string;
    previousState: string;
    newState: string;
    timestamp: string;
    metadata?: Record<string, any>;
}
/**
 * 階段狀態變更事件
 */
export interface StageStateChangeEvent {
    workflowId: string;
    stageId: string;
    previousState: string;
    newState: string;
    timestamp: string;
    metadata?: Record<string, any>;
}
/**
 * 工作流程驗證結果
 */
export interface WorkflowValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    suggestions: string[];
    extensions?: Record<string, any>;
}
/**
 * 下一步指導接口
 */
export interface NextStepGuidance {
    immediateActions: string[];
    suggestedTools: string[];
    contentExamples: string[];
    qualityTips: string[];
}
/**
 * 進度洞察接口
 */
export interface ProgressInsights {
    currentFocus: string;
    upcomingChallenges: string[];
    strengthAreas: string[];
    improvementAreas: string[];
}
/**
 * 質量評估結果
 */
export interface QualityAssessment {
    overallScore: number;
    passedChecks: string[];
    failedChecks: string[];
    recommendations: string[];
}
/**
 * 階段驗證結果 (增強版)
 */
export interface StageValidationResult {
    stageId: string;
    isComplete: boolean;
    requirements: {
        type: string;
        required: boolean;
        found: number;
        met: boolean;
    }[];
    missingRequirements: string[];
    nextStepGuidance?: NextStepGuidance;
    progressInsights?: ProgressInsights;
    qualityAssessment?: QualityAssessment;
    extensions?: Record<string, any>;
}
/**
 * 工作流程狀態管理器
 * 負責管理工作流程的狀態變更、驗證和事件處理
 */
export declare class WorkflowStateManager extends EventEmitter {
    private storage;
    private stateValidators;
    private stateTransitions;
    private activeWorkflows;
    private eventListenerCount;
    private maxEventListeners;
    private operationLocks;
    private pendingOperations;
    constructor(storage: IExtendedStorage);
    /**
     * 創建新的工作流程
     */
    createWorkflow(template: WorkflowTemplate, name: string, customMetadata?: Record<string, any>): Promise<{
        workflowId: string;
        nodes: Node[];
        edges: any[];
    }>;
    /**
     * 更新工作流程狀態（併發安全）
     */
    updateWorkflowState(workflowId: string, updates: Partial<WorkflowState>): Promise<WorkflowState>;
    /**
     * 推進工作流程到下一階段（併發安全）
     */
    advanceToNextStage(workflowId: string, options?: {
        force?: boolean;
        skipValidation?: boolean;
    }): Promise<{
        workflow: WorkflowState;
        nextStage?: StageState;
    }>;
    /**
     * 驗證工作流程
     */
    validateWorkflow(workflowId: string): Promise<WorkflowValidationResult>;
    /**
     * 驗證階段完成條件 (增強版)
     */
    validateStage(workflowId: string, stageId: string): Promise<StageValidationResult>;
    /**
     * 註冊自定義狀態驗證器
     */
    registerStateValidator(stateName: string, validator: (state: WorkflowState) => Promise<boolean>): void;
    /**
     * 註冊自定義狀態轉換規則
     */
    registerStateTransition(fromState: string, toStates: string[]): void;
    /**
     * 生成下一步指導
     */
    private generateNextStepGuidance;
    /**
     * 生成進度洞察
     */
    private generateProgressInsights;
    /**
     * 生成質量評估
     */
    private generateQualityAssessment;
    /**
     * 模擬質量檢查（簡化版本）
     */
    private simulateQualityCheck;
    private generateWorkflowId;
    private initializeStateTransitions;
    private isValidStateTransition;
    /**
     * 併發安全的操作鎖機制
     */
    private withLock;
    /**
     * 清理已完成的工作流程資源
     */
    cleanupCompletedWorkflows(): Promise<void>;
    /**
     * 獲取性能統計信息
     */
    getPerformanceStats(): {
        activeWorkflows: number;
        eventListeners: number;
        stateValidators: number;
        stateTransitions: number;
        memoryUsage: NodeJS.MemoryUsage;
    };
    /**
     * 清理所有資源
     */
    cleanup(): void;
}
