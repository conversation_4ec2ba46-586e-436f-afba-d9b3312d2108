// src/integration/tools/registry/workflowTools.ts
/**
 * 工作流程相關的MCP工具定義
 * 遵循開放封閉原則，支持未來功能擴展
 */
export const workflowTools = [
    {
        name: "workflow_create",
        description: "創建新的工作流程，支持多種創作類型（小說、文章、劇本等）",
        inputSchema: {
            type: "object",
            properties: {
                name: {
                    type: "string",
                    description: "工作流程名稱"
                },
                type: {
                    type: "string",
                    enum: ["novel", "article", "script", "custom"],
                    description: "工作流程類型"
                },
                templateId: {
                    type: "string",
                    description: "使用的模板ID（可選，默認使用標準模板）"
                },
                customStages: {
                    type: "array",
                    items: {
                        type: "string",
                        description: "階段名稱"
                    },
                    description: "自定義階段列表（僅當type為custom時使用）"
                },
                metadata: {
                    type: "object",
                    description: "額外的元數據信息",
                    additionalProperties: true
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            required: ["name", "type"],
            additionalProperties: false
        }
    },
    {
        name: "workflow_status",
        description: "獲取工作流程的詳細狀態信息，包括當前階段、進度和完成情況",
        inputSchema: {
            type: "object",
            properties: {
                workflowId: {
                    type: "string",
                    description: "工作流程ID"
                },
                includeStages: {
                    type: "boolean",
                    default: true,
                    description: "是否包含階段詳細信息"
                },
                includeValidation: {
                    type: "boolean",
                    default: false,
                    description: "是否包含驗證結果"
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            required: ["workflowId"],
            additionalProperties: false
        }
    },
    {
        name: "workflow_advance",
        description: "推進工作流程到下一階段，支持強制推進和驗證跳過",
        inputSchema: {
            type: "object",
            properties: {
                workflowId: {
                    type: "string",
                    description: "工作流程ID"
                },
                force: {
                    type: "boolean",
                    default: false,
                    description: "是否強制推進（忽略完成條件檢查）"
                },
                skipValidation: {
                    type: "boolean",
                    default: false,
                    description: "是否跳過驗證步驟"
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            required: ["workflowId"],
            additionalProperties: false
        }
    },
    {
        name: "workflow_list",
        description: "列出所有工作流程，支持按狀態、類型等條件過濾",
        inputSchema: {
            type: "object",
            properties: {
                status: {
                    type: "string",
                    enum: ["not_started", "in_progress", "completed", "paused"],
                    description: "按狀態過濾"
                },
                type: {
                    type: "string",
                    enum: ["novel", "article", "script", "custom"],
                    description: "按類型過濾"
                },
                limit: {
                    type: "number",
                    minimum: 1,
                    maximum: 100,
                    default: 20,
                    description: "返回結果數量限制"
                },
                offset: {
                    type: "number",
                    minimum: 0,
                    default: 0,
                    description: "分頁偏移量"
                },
                sortBy: {
                    type: "string",
                    enum: ["created_at", "updated_at", "progress", "name"],
                    default: "updated_at",
                    description: "排序字段"
                },
                sortOrder: {
                    type: "string",
                    enum: ["asc", "desc"],
                    default: "desc",
                    description: "排序順序"
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            additionalProperties: false
        }
    },
    {
        name: "stage_validate",
        description: "驗證指定階段的完成條件，檢查所需節點和要求是否滿足",
        inputSchema: {
            type: "object",
            properties: {
                workflowId: {
                    type: "string",
                    description: "工作流程ID"
                },
                stageId: {
                    type: "string",
                    description: "階段ID（可選，默認驗證當前階段）"
                },
                detailed: {
                    type: "boolean",
                    default: true,
                    description: "是否返回詳細的驗證信息"
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            required: ["workflowId"],
            additionalProperties: false
        }
    },
    {
        name: "stage_complete",
        description: "手動標記階段為完成狀態，支持強制完成",
        inputSchema: {
            type: "object",
            properties: {
                workflowId: {
                    type: "string",
                    description: "工作流程ID"
                },
                stageId: {
                    type: "string",
                    description: "階段ID"
                },
                force: {
                    type: "boolean",
                    default: false,
                    description: "是否強制完成（忽略完成條件）"
                },
                notes: {
                    type: "string",
                    description: "完成備註"
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            required: ["workflowId", "stageId"],
            additionalProperties: false
        }
    },
    {
        name: "workflow_templates",
        description: "獲取可用的工作流程模板列表",
        inputSchema: {
            type: "object",
            properties: {
                category: {
                    type: "string",
                    enum: ["novel", "article", "script", "custom"],
                    description: "按類別過濾模板"
                },
                includeCustom: {
                    type: "boolean",
                    default: true,
                    description: "是否包含自定義模板"
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            additionalProperties: false
        }
    },
    {
        name: "workflow_pause",
        description: "暫停工作流程執行",
        inputSchema: {
            type: "object",
            properties: {
                workflowId: {
                    type: "string",
                    description: "工作流程ID"
                },
                reason: {
                    type: "string",
                    description: "暫停原因"
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            required: ["workflowId"],
            additionalProperties: false
        }
    },
    {
        name: "workflow_resume",
        description: "恢復暫停的工作流程",
        inputSchema: {
            type: "object",
            properties: {
                workflowId: {
                    type: "string",
                    description: "工作流程ID"
                },
                notes: {
                    type: "string",
                    description: "恢復備註"
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            required: ["workflowId"],
            additionalProperties: false
        }
    },
    {
        name: "workflow_delete",
        description: "刪除工作流程及其相關數據",
        inputSchema: {
            type: "object",
            properties: {
                workflowId: {
                    type: "string",
                    description: "工作流程ID"
                },
                force: {
                    type: "boolean",
                    default: false,
                    description: "是否強制刪除（即使工作流程正在進行中）"
                },
                keepNodes: {
                    type: "boolean",
                    default: false,
                    description: "是否保留相關的內容節點"
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            required: ["workflowId"],
            additionalProperties: false
        }
    },
    // 🔮 預留未來工具擴展
    {
        name: "workflow_export",
        description: "導出工作流程數據（預留功能）",
        inputSchema: {
            type: "object",
            properties: {
                workflowId: {
                    type: "string",
                    description: "工作流程ID"
                },
                format: {
                    type: "string",
                    enum: ["json", "yaml", "xml"],
                    default: "json",
                    description: "導出格式"
                },
                includeContent: {
                    type: "boolean",
                    default: true,
                    description: "是否包含內容節點"
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            required: ["workflowId"],
            additionalProperties: false
        }
    },
    {
        name: "workflow_import",
        description: "導入工作流程數據（預留功能）",
        inputSchema: {
            type: "object",
            properties: {
                data: {
                    type: "string",
                    description: "要導入的工作流程數據"
                },
                format: {
                    type: "string",
                    enum: ["json", "yaml", "xml"],
                    default: "json",
                    description: "數據格式"
                },
                overwrite: {
                    type: "boolean",
                    default: false,
                    description: "是否覆蓋同名工作流程"
                },
                // 🔮 預留擴展參數
                extensions: {
                    type: "object",
                    description: "預留的擴展參數",
                    additionalProperties: true
                }
            },
            required: ["data"],
            additionalProperties: false
        }
    }
];
/**
 * 工作流程工具類別標識
 */
export const WORKFLOW_TOOL_CATEGORY = 'workflow';
/**
 * 獲取所有工作流程工具
 */
export function getAllWorkflowTools() {
    return [...workflowTools];
}
/**
 * 根據名稱獲取工作流程工具
 */
export function getWorkflowTool(name) {
    return workflowTools.find(tool => tool.name === name);
}
/**
 * 檢查是否為工作流程工具
 */
export function isWorkflowTool(toolName) {
    return workflowTools.some(tool => tool.name === toolName);
}
/**
 * 🔮 預留工具註冊接口
 * 允許運行時動態添加新的工作流程工具
 */
const customWorkflowTools = [];
export function registerCustomWorkflowTool(tool) {
    // 驗證工具名稱格式
    if (!tool.name.startsWith('workflow_') && !tool.name.startsWith('stage_')) {
        throw new Error('Custom workflow tools must start with "workflow_" or "stage_"');
    }
    // 檢查是否已存在
    if (getAllWorkflowTools().some(t => t.name === tool.name)) {
        throw new Error(`Tool already exists: ${tool.name}`);
    }
    customWorkflowTools.push(tool);
}
export function getCustomWorkflowTools() {
    return [...customWorkflowTools];
}
export function getAllWorkflowToolsIncludingCustom() {
    return [...workflowTools, ...customWorkflowTools];
}
//# sourceMappingURL=workflowTools.js.map