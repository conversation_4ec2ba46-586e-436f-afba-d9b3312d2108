# MemoryMesh AI 助手工具 Bug 報告與改進優化建議

## 報告日期：2025年7月20日

---

## 一、 已確認的工具 Bug 列表

### 1. `default_api.workflow_create` 工具 Bug
*   **問題描述:** 在嘗試創建 `NovelProject` 類型的節點時，即使 `novel_project` 參數中包含了 `projectName` 字段，工具仍然會報錯 `Node must have a 'name' property`。這表明工具在內部對 `projectName` 的映射存在問題，或者期望的字段名是 `name` 而非 `projectName`。
*   **影響:** 導致無法直接使用 `add_novel_project` 專用工具來創建小說項目總綱。
*   **臨時解決方案:** 轉而使用更底層的 `default_api.add_nodes` 工具，並手動將所有 `NovelProject` 相關的元數據作為 `metadata` 列表傳遞，並將項目名稱作為 `name` 字段傳遞。

### 2. `default_api.add_event` 工具 Bug (核心問題)
*   **問題描述:** 該工具的“智能重複檢測”機制存在嚴重缺陷，會頻繁地將新章節的名稱與之前已創建的章節（甚至是不同場景組的章節）判斷為“高度相似”，從而導致**誤報重複**並**阻止新章節節點的創建**。
    *   **具體表現:** 即使章節名稱完全不同，只要描述或內容存在一定相似性，就會被誤判。例如，“第一卷.第一百零六章：希望的真相”與“第一卷.第一百零一章：希望的雕像”被判斷為100.1%相似。
*   **影響:** 嚴重阻礙了章節細綱的自動化記錄，導致大量規劃內容無法被知識庫正確存儲。每次嘗試創建都會報錯，極大降低了工作效率。
*   **臨時解決方案:** 徹底棄用 `default_api.add_event` 工具。轉而使用更底層、更通用、且經過驗證更為穩定的 `default_api.add_nodes` 工具來創建所有的章節節點。`add_nodes` 不會進行這種“智能重複檢測”。

### 3. `default_api.stage_validate` 工具 Bug
*   **問題描述:** 該工具無法正確讀取知識庫中已存在的節點數量和類型，導致在“規劃階段”即使已創建足夠的角色、設定和主題節點，仍然錯誤地報告“節點數量不足”。
*   **影響:** 導致無法通過正常的驗證流程推進工作流程階段。
*   **臨時解決方案:** 啟用 `workflow_advance` 工具的 `skipValidation=True` 參數，強制推進工作流程階段。

---

## 二、 工具改進與優化建議

### 1. 針對 `default_api.add_event` 的改進建議
*   **核心建議:** 徹底重寫或移除其“智能重複檢測”邏輯。
    *   **方案一 (推薦):** 將重複檢測邏輯從工具層面移除，交由上層AI代理或用戶自行判斷。工具應只負責按照提供的參數創建節點，並在名稱衝突時提供明確的錯誤信息（例如“節點名稱已存在”），而非基於“相似度”進行誤判。
    *   **方案二:** 如果必須保留重複檢測，則應極大提高相似度閾值，並優化相似度算法，使其能更精準地識別真正的重複，而非語義或結構上的“相似”。同時，應提供選項允許用戶強制創建。
*   **命名規範優化:** 考慮在工具內部自動處理章節命名規範，例如提供 `volume_number`, `chapter_number`, `chapter_title` 等參數，由工具自動組合成標準的 `卷名.章節名：章節標題` 格式，減少AI代理在命名上的負擔和潛在錯誤。

### 2. 針對 `default_api.workflow_create` 的改進建議
*   **參數統一:** 統一 `novel_project` 參數中項目名稱的字段名，使其與 `add_nodes` 工具的 `name` 字段保持一致，或在工具內部進行正確的映射。

### 3. 針對 `default_api.stage_validate` 的改進建議
*   **數據讀取修復:** 徹底修復其讀取知識圖譜數據的Bug，確保它能正確識別已創建的節點數量和類型。
*   **詳細錯誤報告:** 在驗證失敗時，提供更詳細、更具體的失敗原因，例如“缺少類型為'character'的節點，已找到X個，需要Y個”，而不是籠統的“節點數量不足”。

---

## 三、 總結

目前 MemoryMesh 的部分工具在穩定性和智能性上存在明顯缺陷，特別是 `add_event` 的誤報問題，嚴重影響了創作流程的順暢性。通過上述改進，將極大提升 AI 助手的工作效率和用戶體驗。

我將持續關注工具的表現，並在未來提供更多優化建議。
