/**
 * 智能實體標準化器
 * 使用多種算法動態處理實體去重，不依賴固定對照表
 */
export declare class SmartEntityNormalizer {
    /**
     * 使用Unicode標準化進行文本正規化
     */
    static unicodeNormalize(text: string): string;
    /**
     * 語音相似度檢測（針對中文）
     * 基於聲母韻母的相似性
     */
    static phoneticSimilarity(text1: string, text2: string): number;
    /**
     * 簡化拼音近似（實際應用中可以使用 pinyin 庫）
     */
    private static toPinyinApprox;
    /**
     * 結構相似度檢測
     * 檢測姓名結構、分隔符等模式
     */
    static structuralSimilarity(text1: string, text2: string): number;
    /**
     * 提取文本結構特徵
     */
    private static extractStructure;
    /**
     * 檢查是否包含繁體字（使用統計方法）
     */
    private static hasTraditionalChars;
    /**
     * 語義距離檢測（可以集成詞向量）
     */
    static semanticSimilarity(text1: string, text2: string): number;
    /**
     * 提取語義標記
     */
    private static extractSemanticTokens;
    /**
     * 綜合相似度計算
     * 結合多種算法的加權平均
     */
    static calculateComprehensiveSimilarity(text1: string, text2: string): {
        similarity: number;
        breakdown: {
            unicode: number;
            phonetic: number;
            structural: number;
            semantic: number;
        };
        weights?: {
            unicode: number;
            phonetic: number;
            structural: number;
            semantic: number;
        };
        confidence?: number;
    };
    /**
     * 編輯距離相似度
     */
    static levenshteinSimilarity(s1: string, s2: string): number;
    /**
     * 計算編輯距離
     */
    private static levenshteinDistance;
    /**
     * 動態權重計算
     * 根據文本特徵調整權重
     */
    private static calculateDynamicWeights;
    /**
     * 計算檢測置信度
     */
    private static calculateConfidence;
    /**
     * 智能重複檢測（主入口）- 改進版
     */
    static isSmartDuplicate(existingNames: string[], newName: string, options?: {
        threshold?: number;
        strictMode?: boolean;
        contextType?: 'character' | 'setting' | 'general';
    }): {
        isDuplicate: boolean;
        matchedName?: string;
        similarity?: number;
        confidence?: number;
        analysis?: any;
        recommendation?: string;
    };
    /**
     * 根據上下文調整閾值
     */
    private static adjustThresholdByContext;
    /**
     * 生成改進建議
     */
    private static generateRecommendation;
}
