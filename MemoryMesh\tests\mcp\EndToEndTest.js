// tests/mcp/EndToEndTest.js
// 端到端完整測試

console.log('🚀 開始端到端完整測試...');

async function runEndToEndTest() {
    try {
        console.log('📦 導入模塊...');
        
        const workflowToolsModule = await import('../../dist/integration/tools/registry/workflowTools.js');
        const storageModule = await import('../../dist/infrastructure/storage/CachedJsonLineStorage.js');
        const appManagerModule = await import('../../dist/application/managers/ApplicationManager.js');
        const handlerModule = await import('../../dist/integration/tools/handlers/WorkflowToolHandler.js');
        
        console.log('✅ 所有模塊導入成功');

        const storage = new storageModule.CachedJsonLineStorage();
        const appManager = new appManagerModule.ApplicationManager(storage);
        const handler = new handlerModule.WorkflowToolHandler(appManager, storage);

        console.log('✅ 實例創建成功');

        // 生成唯一的項目名稱
        const timestamp = Date.now();
        const randomId = Math.random().toString(36).substr(2, 9);
        const uniqueId = Math.random().toString(36).substr(2, 15);
        const projectName = `MCP_E2E_Test_Fantasy_Adventure_Novel_${timestamp}_${randomId}_${uniqueId}_UNIQUE_IDENTIFIER`;

        console.log(`📝 項目名稱: ${projectName}`);

        // === 第一階段：創建工作流程 ===
        console.log('\n🎯 第一階段：創建工作流程');
        const createResult = await handler.handleTool('workflow_create', {
            name: projectName,
            type: 'novel',
            metadata: {
                author: 'MCP_E2E_Tester',
                genre: 'Fantasy_Adventure',
                targetWordCount: 75000,
                testType: 'end_to_end_validation'
            }
        });

        console.log('📄 創建結果:', JSON.stringify(createResult, null, 2));

        if (createResult.isError) {
            console.log('❌ 工作流程創建失敗');
            return false;
        }

        // 從響應中提取工作流程ID
        let workflowId = null;
        if (createResult.content && createResult.content.length > 1) {
            const dataContent = createResult.content[1].text;
            const dataMatch = dataContent.match(/"workflowId":\s*"([^"]+)"/);
            if (dataMatch) {
                workflowId = dataMatch[1];
            }
        }
        if (!workflowId) {
            console.log('❌ 無法提取工作流程ID');
            return false;
        }

        console.log(`✅ 工作流程創建成功，ID: ${workflowId}`);

        // === 第二階段：查看初始狀態 ===
        console.log('\n🔍 第二階段：查看初始狀態');
        const statusResult1 = await handler.handleTool('workflow_status', { workflowId });
        console.log('📄 初始狀態:', JSON.stringify(statusResult1, null, 2));

        // === 第三階段：添加規劃階段內容 ===
        console.log('\n📚 第三階段：添加規劃階段內容');

        // 添加主角
        const addCharacterResult = await handler.handleTool('add_character', {
            character: {
                name: '端到端測試主角',
                role: 'protagonist',
                status: 'Active',
                currentLocation: ['測試城市'],
                description: '用於端到端測試的主角角色',
                background: '一個勇敢的冒險者',
                motivation: '拯救世界',
                traits: ['勇敢', '智慧', '善良']
            }
        });
        console.log('✅ 主角添加:', addCharacterResult.isError ? '失敗' : '成功');

        // 添加設定
        const addSettingResult = await handler.handleTool('add_setting', {
            setting: {
                name: '端到端測試城市',
                type: 'Urban',
                description: '用於端到端測試的城市設定',
                status: 'Active',
                significance: 'Major',
                atmosphere: '繁華而神秘的都市',
                notableFeatures: ['高樓大廈', '古老神廟', '地下迷宮']
            }
        });
        console.log('✅ 設定添加:', addSettingResult.isError ? '失敗' : '成功');

        // 添加主題
        const addThemeResult = await handler.handleTool('add_theme', {
            theme: {
                name: '端到端測試主題',
                type: 'Main Theme',
                description: '探討勇氣與智慧的重要性',
                status: 'Introduced',
                importance: 'Core',
                currentDevelopment: '通過主角的冒險展現主題'
            }
        });
        console.log('✅ 主題添加:', addThemeResult.isError ? '失敗' : '成功');

        // === 第四階段：驗證規劃階段 ===
        console.log('\n✅ 第四階段：驗證規劃階段');
        const validateResult1 = await handler.handleTool('stage_validate', { workflowId });
        console.log('📄 規劃階段驗證:', JSON.stringify(validateResult1, null, 2));

        // === 第五階段：推進到大綱階段 ===
        console.log('\n⏭️ 第五階段：推進到大綱階段');
        const advanceResult1 = await handler.handleTool('workflow_advance', { workflowId });
        console.log('📄 推進結果:', JSON.stringify(advanceResult1, null, 2));

        // === 第六階段：添加大綱階段內容 ===
        console.log('\n📖 第六階段：添加大綱階段內容');

        // 添加情節線
        const addPlotResult = await handler.handleTool('add_plotarc', {
            plotarc: {
                name: '端到端測試主線',
                type: 'Main Plot',
                description: '主角的冒險旅程',
                status: 'Planning',
                importance: 'Critical',
                progressPercentage: '0',
                mainCharacters: ['端到端測試主角'],
                incitingIncident: '神秘事件的發生',
                climax: '最終決戰',
                resolution: '和平恢復'
            }
        });
        console.log('✅ 情節線添加:', addPlotResult.isError ? '失敗' : '成功');

        // === 第七階段：測試工作流程管理功能 ===
        console.log('\n🔧 第七階段：測試工作流程管理功能');

        // 暫停工作流程
        const pauseResult = await handler.handleTool('workflow_pause', {
            workflowId,
            reason: '端到端測試暫停'
        });
        console.log('⏸️ 暫停結果:', pauseResult.isError ? '失敗' : '成功');

        // 恢復工作流程
        const resumeResult = await handler.handleTool('workflow_resume', {
            workflowId,
            notes: '端到端測試恢復'
        });
        console.log('▶️ 恢復結果:', resumeResult.isError ? '失敗' : '成功');

        // === 第八階段：列出所有工作流程 ===
        console.log('\n📋 第八階段：列出所有工作流程');
        const listResult = await handler.handleTool('workflow_list', {});
        console.log('📄 工作流程列表:', JSON.stringify(listResult, null, 2));

        // === 第九階段：獲取最終狀態 ===
        console.log('\n🏁 第九階段：獲取最終狀態');
        const finalStatusResult = await handler.handleTool('workflow_status', { workflowId });
        console.log('📄 最終狀態:', JSON.stringify(finalStatusResult, null, 2));

        // === 第十階段：測試錯誤處理 ===
        console.log('\n❌ 第十階段：測試錯誤處理');

        // 測試不存在的工作流程
        const errorTest1 = await handler.handleTool('workflow_status', {
            workflowId: 'definitely-does-not-exist-12345'
        });
        console.log('🔍 不存在工作流程測試:', errorTest1.isError ? '正確返回錯誤' : '意外成功');

        // 測試無效參數
        const errorTest2 = await handler.handleTool('workflow_advance', {
            workflowId: 'invalid-id',
            invalidParam: 'should-be-ignored'
        });
        console.log('🔍 無效參數測試:', errorTest2.isError ? '正確返回錯誤' : '意外成功');

        // === 清理 ===
        console.log('\n🧹 清理資源...');
        if (storage && typeof storage.cleanup === 'function') {
            storage.cleanup();
            console.log('✅ 存儲已清理');
        }

        console.log('\n🎉 端到端測試完成！');
        console.log(`📊 測試項目ID: ${workflowId}`);
        
        return {
            success: true,
            workflowId,
            projectName,
            testResults: {
                workflowCreated: !createResult.isError,
                contentAdded: !addCharacterResult.isError && !addSettingResult.isError && !addThemeResult.isError,
                stageValidated: !validateResult1.isError,
                stageAdvanced: !advanceResult1.isError,
                workflowManaged: !pauseResult.isError && !resumeResult.isError,
                errorHandling: errorTest1.isError && errorTest2.isError
            }
        };

    } catch (error) {
        console.error('❌ 端到端測試過程中發生錯誤:', error);
        console.error('錯誤堆棧:', error.stack);
        return { success: false, error: error.message };
    }
}

// 運行測試
runEndToEndTest().then(result => {
    if (result.success) {
        console.log('\n✅ 端到端測試成功完成！');
        console.log('📊 測試結果摘要:');
        Object.entries(result.testResults).forEach(([test, passed]) => {
            console.log(`  ${passed ? '✅' : '❌'} ${test}`);
        });
        process.exit(0);
    } else {
        console.log('\n❌ 端到端測試失敗');
        if (result.error) {
            console.log('錯誤:', result.error);
        }
        process.exit(1);
    }
}).catch(error => {
    console.error('❌ 未捕獲的錯誤:', error);
    process.exit(1);
});
