// tests/architecture/ExtensionInterfaces.test.ts

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { CachedJsonLineStorage } from '../../src/infrastructure/storage/CachedJsonLineStorage.js';
import { WorkflowStateManager } from '../../src/core/workflow/WorkflowStateManager.js';
import { PredefinedTemplates } from '../../src/core/workflow/WorkflowTemplates.js';
import type { 
    StoragePlugin, 
    CustomQuery,
    IToolPlugin,
    IToolMiddleware,
    WorkflowTemplate 
} from '../../src/infrastructure/storage/IExtendedStorage.js';

describe('Extension Interfaces Validation', () => {
    let storage: CachedJsonLineStorage;
    let workflowManager: WorkflowStateManager;

    beforeEach(() => {
        storage = new CachedJsonLineStorage();
        workflowManager = new WorkflowStateManager(storage);
    });

    afterEach(() => {
        storage.cleanup();
        workflowManager.cleanup();
    });

    describe('存儲插件系統', () => {
        it('應該能夠註冊和使用存儲插件', async () => {
            const mockPlugin: StoragePlugin = {
                name: 'test-storage-plugin',
                version: '1.0.0',
                initialize: jest.fn().mockResolvedValue(undefined),
                extensions: {
                    customFeature: 'enabled'
                }
            };

            // 測試插件註冊
            await storage.registerStoragePlugin(mockPlugin);
            expect(mockPlugin.initialize).toHaveBeenCalledWith(storage);

            // 驗證插件擴展字段可用
            expect(mockPlugin.extensions?.customFeature).toBe('enabled');
        });

        it('應該支持自定義查詢擴展', async () => {
            const customQuery: CustomQuery = {
                type: 'workflow-analytics',
                parameters: {
                    dateRange: { start: '2024-01-01', end: '2024-12-31' },
                    workflowType: 'novel'
                },
                extensions: {
                    includeMetrics: true,
                    outputFormat: 'detailed'
                }
            };

            // 測試自定義查詢結構
            expect(customQuery.type).toBe('workflow-analytics');
            expect(customQuery.extensions?.includeMetrics).toBe(true);

            // 驗證查詢可以被序列化和反序列化
            const serialized = JSON.stringify(customQuery);
            const deserialized = JSON.parse(serialized);
            expect(deserialized.extensions.outputFormat).toBe('detailed');
        });
    });

    describe('工作流程模板擴展', () => {
        it('應該支持自定義模板註冊', () => {
            const customTemplate: WorkflowTemplate = {
                id: 'test-custom-template',
                name: 'Test Custom Template',
                description: 'A test template for validation',
                category: 'custom',
                version: '1.0.0',
                stages: [
                    {
                        id: 'test-stage',
                        name: 'Test Stage',
                        description: 'A test stage',
                        order: 0,
                        requiredNodeTypes: ['test-node'],
                        optionalNodeTypes: [],
                        completionCriteria: {
                            minNodes: 1,
                            requiredFields: ['name'],
                            extensions: {
                                customValidation: 'test-validator'
                            }
                        },
                        extensions: {
                            estimatedHours: 2,
                            difficulty: 'easy'
                        }
                    }
                ],
                extensions: {
                    targetAudience: 'developers',
                    complexity: 'low'
                }
            };

            // 測試模板註冊
            PredefinedTemplates.registerCustomTemplate(customTemplate);
            
            // 驗證模板可以被檢索
            const retrieved = PredefinedTemplates.getCustomTemplate(customTemplate.id);
            expect(retrieved).toBeDefined();
            expect(retrieved?.extensions?.targetAudience).toBe('developers');
            
            // 驗證階段擴展字段
            expect(retrieved?.stages[0].extensions?.difficulty).toBe('easy');
            expect(retrieved?.stages[0].completionCriteria.extensions?.customValidation).toBe('test-validator');
        });

        it('應該支持動態模板生成', () => {
            const userRequirements = {
                name: 'Dynamic Test Template',
                description: 'Generated from user input',
                stages: [
                    {
                        id: 'dynamic-stage-1',
                        name: 'Dynamic Stage 1',
                        requirements: ['input', 'validation'],
                        minNodes: 2
                    },
                    {
                        id: 'dynamic-stage-2', 
                        name: 'Dynamic Stage 2',
                        requirements: ['output'],
                        minNodes: 1
                    }
                ]
            };

            // 模擬動態模板生成器
            const generateTemplate = (requirements: any): WorkflowTemplate => ({
                id: `dynamic-${Date.now()}`,
                name: requirements.name,
                description: requirements.description,
                category: 'custom',
                version: '1.0.0',
                stages: requirements.stages.map((stage: any, index: number) => ({
                    id: stage.id,
                    name: stage.name,
                    description: `Generated stage: ${stage.name}`,
                    order: index,
                    requiredNodeTypes: stage.requirements || ['content'],
                    optionalNodeTypes: [],
                    completionCriteria: {
                        minNodes: stage.minNodes || 1,
                        requiredFields: ['name']
                    }
                })),
                extensions: {
                    generated: true,
                    timestamp: new Date().toISOString()
                }
            });

            const dynamicTemplate = generateTemplate(userRequirements);
            
            expect(dynamicTemplate.name).toBe('Dynamic Test Template');
            expect(dynamicTemplate.stages).toHaveLength(2);
            expect(dynamicTemplate.extensions?.generated).toBe(true);
        });
    });

    describe('狀態管理擴展', () => {
        it('應該支持自定義狀態驗證器', async () => {
            const customValidator = jest.fn().mockResolvedValue(true);
            
            // 註冊自定義驗證器
            workflowManager.registerStateValidator('custom-state', customValidator);
            
            // 創建工作流程進行測試
            const template = PredefinedTemplates.NOVEL_TEMPLATE;
            const result = await workflowManager.createWorkflow(template, 'Validator Test');
            
            // 驗證器應該可以被調用（這裡只是驗證接口可用性）
            expect(typeof customValidator).toBe('function');
        });

        it('應該支持自定義狀態轉換規則', () => {
            const customTransitions = ['custom-state-1', 'custom-state-2'];
            
            // 註冊自定義狀態轉換
            workflowManager.registerStateTransition('custom-initial', customTransitions);
            
            // 驗證轉換規則被正確存儲（通過私有方法測試）
            // 這裡主要驗證接口的可用性
            expect(customTransitions).toHaveLength(2);
        });
    });

    describe('事件系統擴展', () => {
        it('應該支持自定義事件和擴展字段', (done) => {
            const customEventData = {
                workflowId: 'test-workflow',
                customMetric: 'performance-milestone',
                extensions: {
                    processingTime: 1500,
                    memoryUsage: '45MB',
                    customData: { key: 'value' }
                }
            };

            // 監聽自定義事件
            workflowManager.once('custom-workflow-event', (event) => {
                expect(event.extensions?.processingTime).toBe(1500);
                expect(event.extensions?.customData.key).toBe('value');
                done();
            });

            // 發射自定義事件
            workflowManager.emit('custom-workflow-event', customEventData);
        });

        it('應該支持事件數據的序列化和反序列化', () => {
            const eventData = {
                type: 'workflow_performance_update',
                timestamp: new Date().toISOString(),
                workflowId: 'test-workflow-123',
                data: {
                    stage: 'processing',
                    progress: 75
                },
                extensions: {
                    metrics: {
                        cpu: '45%',
                        memory: '120MB'
                    },
                    customFields: {
                        priority: 'high',
                        tags: ['urgent', 'novel']
                    }
                }
            };

            // 測試序列化
            const serialized = JSON.stringify(eventData);
            expect(serialized).toContain('workflow_performance_update');

            // 測試反序列化
            const deserialized = JSON.parse(serialized);
            expect(deserialized.extensions.metrics.cpu).toBe('45%');
            expect(deserialized.extensions.customFields.tags).toContain('urgent');
        });
    });

    describe('數據模型擴展字段', () => {
        it('應該支持工作流程狀態的擴展字段', async () => {
            const template = PredefinedTemplates.NOVEL_TEMPLATE;
            const customMetadata = {
                author: 'Test Author',
                genre: 'Science Fiction',
                targetWordCount: 80000,
                customSettings: {
                    autoSave: true,
                    notifications: ['email', 'push']
                }
            };

            const result = await workflowManager.createWorkflow(
                template, 
                'Extension Test', 
                customMetadata
            );

            // 驗證自定義元數據被正確存儲
            const workflow = await storage.loadWorkflowState(result.workflowId);
            expect(workflow?.metadata.author).toBe('Test Author');
            expect(workflow?.metadata.customSettings.autoSave).toBe(true);
        });

        it('應該支持階段狀態的擴展字段', async () => {
            const template = PredefinedTemplates.NOVEL_TEMPLATE;
            const result = await workflowManager.createWorkflow(template, 'Stage Extension Test');

            const stages = await storage.loadStageStates(result.workflowId);
            const firstStage = stages[0];

            // 模擬添加擴展字段
            firstStage.extensions = {
                estimatedHours: 8,
                difficulty: 'medium',
                resources: ['character-sheet', 'world-map'],
                customValidation: {
                    rules: ['min-characters', 'setting-defined'],
                    strictMode: true
                }
            };

            await storage.saveStageState(firstStage);

            // 驗證擴展字段被正確保存和檢索
            const updatedStages = await storage.loadStageStates(result.workflowId);
            const updatedStage = updatedStages.find(s => s.stageId === firstStage.stageId);
            
            expect(updatedStage?.extensions?.difficulty).toBe('medium');
            expect(updatedStage?.extensions?.customValidation.strictMode).toBe(true);
        });
    });

    describe('向後兼容性', () => {
        it('應該能夠處理缺少擴展字段的舊數據', async () => {
            // 模擬舊版本的工作流程數據（沒有擴展字段）
            const legacyWorkflowData = {
                workflowId: 'legacy-workflow-123',
                name: 'Legacy Workflow',
                status: 'in_progress',
                currentStage: 1,
                totalStages: 3,
                progress: 33,
                metadata: {
                    type: 'novel'
                    // 注意：沒有 extensions 字段
                },
                createdAt: '2024-01-01T00:00:00.000Z',
                updatedAt: '2024-01-01T00:00:00.000Z'
            };

            // 保存舊數據
            await storage.saveWorkflowState(legacyWorkflowData);

            // 驗證可以正常讀取
            const retrieved = await storage.loadWorkflowState('legacy-workflow-123');
            expect(retrieved).toBeDefined();
            expect(retrieved?.name).toBe('Legacy Workflow');
            
            // 驗證擴展字段的默認處理
            expect(retrieved?.extensions).toBeUndefined(); // 舊數據沒有這個字段
        });

        it('應該能夠安全地添加新的擴展字段', async () => {
            const template = PredefinedTemplates.NOVEL_TEMPLATE;
            const result = await workflowManager.createWorkflow(template, 'Compatibility Test');

            // 獲取原始工作流程
            const originalWorkflow = await storage.loadWorkflowState(result.workflowId);
            
            // 添加新的擴展字段
            const updatedWorkflow = {
                ...originalWorkflow!,
                extensions: {
                    newFeature: 'enabled',
                    version: '2.0.0',
                    migrationData: {
                        from: '1.0.0',
                        timestamp: new Date().toISOString()
                    }
                }
            };

            await storage.saveWorkflowState(updatedWorkflow);

            // 驗證新字段被正確保存
            const retrieved = await storage.loadWorkflowState(result.workflowId);
            expect(retrieved?.extensions?.newFeature).toBe('enabled');
            expect(retrieved?.extensions?.migrationData.from).toBe('1.0.0');
        });
    });
});
