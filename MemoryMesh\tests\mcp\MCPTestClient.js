// tests/mcp/MCPTestClient.js
// MCP客戶端測試腳本

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class MCPTestClient {
    constructor() {
        this.serverProcess = null;
        this.requestId = 1;
        this.responses = new Map();
        this.testResults = [];
    }

    async startServer() {
        console.log('🚀 啟動MemoryMesh MCP服務器...');
        
        const serverPath = path.join(__dirname, '../../dist/index.js');
        this.serverProcess = spawn('node', [serverPath], {
            stdio: ['pipe', 'pipe', 'pipe'],
            cwd: path.join(__dirname, '../..')
        });

        // 等待服務器啟動
        await new Promise((resolve) => {
            this.serverProcess.stdout.on('data', (data) => {
                const output = data.toString();
                console.log('服務器輸出:', output.trim());
                if (output.includes('Knowledge Graph MCP Server running')) {
                    resolve();
                }
            });
        });

        // 設置響應處理
        this.serverProcess.stdout.on('data', (data) => {
            const lines = data.toString().split('\n').filter(line => line.trim());
            for (const line of lines) {
                try {
                    const response = JSON.parse(line);
                    if (response.id) {
                        this.responses.set(response.id, response);
                    }
                } catch (e) {
                    // 忽略非JSON輸出
                }
            }
        });

        console.log('✅ MCP服務器已啟動');
    }

    async sendRequest(method, params = {}) {
        const id = this.requestId++;
        const request = {
            jsonrpc: "2.0",
            id,
            method,
            params
        };

        console.log(`📤 發送請求: ${method}`, params);
        
        this.serverProcess.stdin.write(JSON.stringify(request) + '\n');

        // 等待響應
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error(`請求超時: ${method}`));
            }, 10000);

            const checkResponse = () => {
                if (this.responses.has(id)) {
                    clearTimeout(timeout);
                    const response = this.responses.get(id);
                    this.responses.delete(id);
                    resolve(response);
                } else {
                    setTimeout(checkResponse, 100);
                }
            };
            checkResponse();
        });
    }

    async testTool(toolName, params = {}) {
        console.log(`\n🧪 測試工具: ${toolName}`);
        
        try {
            const response = await this.sendRequest('tools/call', {
                name: toolName,
                arguments: params
            });

            console.log(`📥 響應:`, response);

            const result = {
                tool: toolName,
                params,
                success: !response.error,
                response: response.result || response.error,
                timestamp: new Date().toISOString()
            };

            this.testResults.push(result);

            if (response.error) {
                console.log(`❌ 工具調用失敗: ${response.error.message}`);
                return { success: false, error: response.error };
            } else {
                console.log(`✅ 工具調用成功`);
                return { success: true, result: response.result };
            }
        } catch (error) {
            console.log(`❌ 測試失敗: ${error.message}`);
            this.testResults.push({
                tool: toolName,
                params,
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
            return { success: false, error: error.message };
        }
    }

    async listTools() {
        console.log('\n📋 獲取工具列表...');
        try {
            const response = await this.sendRequest('tools/list');
            if (response.result && response.result.tools) {
                console.log(`✅ 發現 ${response.result.tools.length} 個工具`);
                return response.result.tools;
            }
        } catch (error) {
            console.log(`❌ 獲取工具列表失敗: ${error.message}`);
        }
        return [];
    }

    async runWorkflowTests() {
        console.log('\n🔄 開始工作流程工具測試...');

        // 測試1: 獲取工作流程模板
        await this.testTool('workflow_templates', {});

        // 測試2: 創建小說工作流程
        const createResult = await this.testTool('workflow_create', {
            name: 'MCP測試小說項目',
            type: 'novel',
            metadata: {
                author: 'MCP測試',
                genre: '科幻'
            }
        });

        let workflowId = null;
        if (createResult.success && createResult.result.content) {
            // 從響應中提取工作流程ID
            const content = createResult.result.content.find(c => c.text.includes('工作流程ID'));
            if (content) {
                const match = content.text.match(/工作流程ID[：:]\s*([^\s]+)/);
                if (match) {
                    workflowId = match[1];
                    console.log(`📝 提取到工作流程ID: ${workflowId}`);
                }
            }
        }

        if (workflowId) {
            // 測試3: 查看工作流程狀態
            await this.testTool('workflow_status', { workflowId });

            // 測試4: 驗證當前階段
            await this.testTool('stage_validate', { workflowId });

            // 測試5: 列出所有工作流程
            await this.testTool('workflow_list', {});

            // 測試6: 暫停工作流程
            await this.testTool('workflow_pause', {
                workflowId,
                reason: 'MCP測試暫停'
            });

            // 測試7: 恢復工作流程
            await this.testTool('workflow_resume', {
                workflowId,
                notes: 'MCP測試恢復'
            });

            // 測試8: 強制推進階段
            await this.testTool('workflow_advance', {
                workflowId,
                force: true
            });

            // 測試9: 再次查看狀態
            await this.testTool('workflow_status', { workflowId });
        }

        // 測試10: 錯誤處理 - 不存在的工作流程
        await this.testTool('workflow_status', {
            workflowId: 'non-existent-workflow-123'
        });

        // 測試11: 錯誤處理 - 無效參數
        await this.testTool('workflow_create', {
            // 缺少必需的name參數
            type: 'novel'
        });
    }

    async runIntegrationTests() {
        console.log('\n🔗 開始集成測試...');

        // 創建工作流程
        const createResult = await this.testTool('workflow_create', {
            name: '集成測試項目',
            type: 'novel'
        });

        let workflowId = null;
        if (createResult.success && createResult.result.content) {
            const content = createResult.result.content.find(c => c.text.includes('工作流程ID'));
            if (content) {
                const match = content.text.match(/工作流程ID[：:]\s*([^\s]+)/);
                if (match) {
                    workflowId = match[1];
                }
            }
        }

        if (workflowId) {
            // 添加角色節點
            await this.testTool('add_character', {
                character: {
                    name: 'MCP測試角色',
                    role: 'protagonist',
                    status: 'Active',
                    currentLocation: ['測試城市'],
                    description: '用於MCP測試的角色'
                }
            });

            // 添加設定節點
            await this.testTool('add_setting', {
                setting: {
                    name: '測試城市',
                    type: 'Urban',
                    description: '用於MCP測試的城市設定',
                    status: 'Active'
                }
            });

            // 驗證階段（應該有足夠內容推進）
            await this.testTool('stage_validate', { workflowId });

            // 嘗試推進階段
            await this.testTool('workflow_advance', { workflowId });
        }
    }

    generateReport() {
        console.log('\n📊 生成測試報告...');
        
        const totalTests = this.testResults.length;
        const successfulTests = this.testResults.filter(r => r.success).length;
        const failedTests = totalTests - successfulTests;
        const successRate = ((successfulTests / totalTests) * 100).toFixed(1);

        console.log('\n' + '='.repeat(60));
        console.log('📋 MCP工具測試報告');
        console.log('='.repeat(60));
        console.log(`總測試數: ${totalTests}`);
        console.log(`成功: ${successfulTests}`);
        console.log(`失敗: ${failedTests}`);
        console.log(`成功率: ${successRate}%`);
        console.log('='.repeat(60));

        console.log('\n📝 詳細結果:');
        this.testResults.forEach((result, index) => {
            const status = result.success ? '✅' : '❌';
            console.log(`${index + 1}. ${status} ${result.tool}`);
            if (!result.success) {
                console.log(`   錯誤: ${result.error || result.response?.message || '未知錯誤'}`);
            }
        });

        return {
            total: totalTests,
            successful: successfulTests,
            failed: failedTests,
            successRate: parseFloat(successRate),
            results: this.testResults
        };
    }

    async cleanup() {
        console.log('\n🧹 清理資源...');
        if (this.serverProcess) {
            this.serverProcess.kill();
            console.log('✅ 服務器已停止');
        }
    }
}

// 主測試函數
async function runMCPTests() {
    const client = new MCPTestClient();
    
    try {
        await client.startServer();
        
        // 列出所有工具
        const tools = await client.listTools();
        const workflowTools = tools.filter(t => t.name.startsWith('workflow_') || t.name.startsWith('stage_'));
        console.log(`\n🔄 發現 ${workflowTools.length} 個工作流程工具:`);
        workflowTools.forEach(tool => console.log(`  - ${tool.name}`));

        // 運行工作流程測試
        await client.runWorkflowTests();

        // 運行集成測試
        await client.runIntegrationTests();

        // 生成報告
        const report = client.generateReport();
        
        // 保存報告到文件
        const reportData = {
            timestamp: new Date().toISOString(),
            summary: {
                total: report.total,
                successful: report.successful,
                failed: report.failed,
                successRate: report.successRate
            },
            results: report.results
        };

        console.log('\n💾 保存測試報告...');
        // 這裡可以保存到文件，但在當前環境中我們只輸出到控制台

        return report;
        
    } catch (error) {
        console.error('❌ 測試過程中發生錯誤:', error);
        return null;
    } finally {
        await client.cleanup();
    }
}

// 如果直接運行此腳本
if (import.meta.url === `file://${process.argv[1]}`) {
    runMCPTests().then(report => {
        if (report) {
            console.log(`\n🎉 測試完成！成功率: ${report.successRate}%`);
            process.exit(report.failed > 0 ? 1 : 0);
        } else {
            console.log('\n❌ 測試失敗');
            process.exit(1);
        }
    });
}

export { MCPTestClient, runMCPTests };
