import type { Node } from '../index.js';
/**
 * 工作流程模板接口
 * 定義不同類型創作工作流程的標準結構
 */
export interface WorkflowTemplate {
    readonly id: string;
    readonly name: string;
    readonly description: string;
    readonly category: 'novel' | 'article' | 'script' | 'academic' | 'technical' | 'creative' | 'custom';
    readonly version: string;
    readonly stages: StageTemplate[];
    readonly extensions?: Record<string, any>;
    readonly customFields?: Record<string, any>;
}
/**
 * 階段模板接口
 */
export interface StageTemplate {
    readonly id: string;
    readonly name: string;
    readonly description: string;
    readonly order: number;
    readonly requiredNodeTypes: string[];
    readonly optionalNodeTypes: string[];
    readonly completionCriteria: CompletionCriteria;
    readonly estimatedDuration?: string;
    readonly extensions?: Record<string, any>;
}
/**
 * 質量檢查項目接口
 */
export interface QualityCheck {
    readonly type: string;
    readonly description: string;
    readonly validator: string;
    readonly weight: number;
    readonly required: boolean;
}
/**
 * 內容指導接口
 */
export interface ContentGuidelines {
    readonly [nodeType: string]: {
        readonly description: string;
        readonly examples: string[];
        readonly qualityTips: string[];
        readonly commonMistakes: string[];
    };
}
/**
 * 完成條件接口 (增強版)
 */
export interface CompletionCriteria {
    readonly minNodes: number;
    readonly requiredFields: string[];
    readonly customValidation?: string;
    readonly qualityChecks?: QualityCheck[];
    readonly contentGuidelines?: ContentGuidelines;
    readonly minimumQualityScore?: number;
    readonly extensions?: Record<string, any>;
}
/**
 * 工作流程節點生成器
 * 根據模板生成標準的工作流程節點
 */
export declare class WorkflowNodeGenerator {
    /**
     * 根據模板創建工作流程節點
     */
    static createWorkflowNode(template: WorkflowTemplate, workflowId: string, customMetadata?: Record<string, any>): Node;
    /**
     * 創建階段節點
     */
    static createStageNodes(template: WorkflowTemplate, workflowId: string): Node[];
    /**
     * 創建階段依賴邊
     */
    static createStageDependencyEdges(template: WorkflowTemplate, workflowId: string): Array<{
        from: string;
        to: string;
        edgeType: string;
        weight?: number;
    }>;
}
/**
 * 預定義的工作流程模板
 */
export declare class PredefinedTemplates {
    /**
     * 小說創作工作流程模板
     */
    static readonly NOVEL_TEMPLATE: WorkflowTemplate;
    /**
     * 文章創作工作流程模板
     */
    static readonly ARTICLE_TEMPLATE: WorkflowTemplate;
    /**
     * 劇本創作工作流程模板
     */
    static readonly SCRIPT_TEMPLATE: WorkflowTemplate;
    /**
     * 學術論文工作流程模板
     */
    static readonly ACADEMIC_TEMPLATE: WorkflowTemplate;
    /**
     * 技術文檔工作流程模板
     */
    static readonly TECHNICAL_TEMPLATE: WorkflowTemplate;
    /**
     * 創意寫作工作流程模板
     */
    static readonly CREATIVE_TEMPLATE: WorkflowTemplate;
    /**
     * 獲取所有預定義模板
     */
    static getAllTemplates(): WorkflowTemplate[];
    /**
     * 根據類別獲取模板
     */
    static getTemplatesByCategory(category: string): WorkflowTemplate[];
    /**
     * 根據ID獲取特定模板
     */
    static getTemplateById(id: string): WorkflowTemplate | null;
    /**
     * 解析時長字符串為週數
     */
    private static parseDuration;
    /**
     * 獲取適用受眾
     */
    private static getSuitableAudience;
    /**
     * 獲取模板預覽信息
     */
    static getTemplatePreview(id: string): {
        id: string;
        name: string;
        description: string;
        category: string;
        stageCount: number;
        estimatedDuration: string;
        complexity: 'Simple' | 'Moderate' | 'Complex';
        suitableFor: string[];
    } | null;
    private static customTemplates;
    /**
     * 註冊自定義模板
     */
    static registerCustomTemplate(template: WorkflowTemplate): void;
    /**
     * 獲取自定義模板
     */
    static getCustomTemplate(id: string): WorkflowTemplate | null;
    /**
     * 獲取所有模板（包括自定義）
     */
    static getAllTemplatesIncludingCustom(): WorkflowTemplate[];
    /**
     * 模板版本控制和升級機制
     */
    static getTemplateVersions(): Record<string, string[]>;
    /**
     * 檢查模板是否需要升級
     */
    static checkForUpgrade(templateId: string, currentVersion: string): {
        needsUpgrade: boolean;
        latestVersion: string;
        upgradeNotes?: string;
    };
    /**
     * 比較版本號
     */
    private static compareVersions;
    /**
     * 獲取升級說明
     */
    private static getUpgradeNotes;
}
