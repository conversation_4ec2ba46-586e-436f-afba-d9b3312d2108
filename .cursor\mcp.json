{"mcpServers": {"memorymesh": {"command": "node", "args": ["./MemoryMesh/dist/index.js"], "cwd": "/mnt/d/project/06", "description": "MemoryMesh知識圖譜服務", "env": {}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/mnt/d/project/04"], "description": "文件系統管理", "env": {}}, "novel-analyzer": {"command": "D:\\project\\04\\.venv\\Scripts\\python.exe", "args": ["D:\\project\\04\\MCP_Tools\\src\\novel_analyzer_mcp.py"], "cwd": "D:\\project\\04\\MCP_Tools", "description": "小說分析器 MCP 工具", "env": {"PYTHONIOENCODING": "utf-8", "PYTHONPATH": "D:\\project\\04\\MCP_Tools\\src"}}}}