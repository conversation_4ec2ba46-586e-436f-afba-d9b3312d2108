// src/integration/tools/interfaces/IExtendedToolSystem.ts

import type { Tool, ToolResponse } from '@shared/index.js';
import type { ApplicationManager } from '@application/index.js';

/**
 * 工具插件接口
 * 支持第三方工具的插件化擴展
 */
export interface IToolPlugin {
    readonly name: string;
    readonly version: string;
    readonly description: string;
    readonly category: string; // 'workflow' | 'analysis' | 'export' | 'custom'
    
    /**
     * 插件初始化
     */
    initialize(manager: ApplicationManager): Promise<void>;
    
    /**
     * 獲取插件提供的工具列表
     */
    getTools(): Tool[];
    
    /**
     * 處理工具調用
     */
    handleToolCall(toolName: string, args: Record<string, any>): Promise<ToolResponse>;
    
    /**
     * 插件清理
     */
    cleanup(): Promise<void>;
    
    // 🔮 預留擴展字段
    extensions?: Record<string, any>;
}

/**
 * 工具註冊器接口
 * 支持動態註冊和管理工具
 */
export interface IToolRegistry {
    /**
     * 註冊工具插件
     */
    registerPlugin(plugin: IToolPlugin): Promise<void>;
    
    /**
     * 註銷工具插件
     */
    unregisterPlugin(pluginName: string): Promise<void>;
    
    /**
     * 獲取所有已註冊的插件
     */
    getRegisteredPlugins(): IToolPlugin[];
    
    /**
     * 根據類別獲取工具
     */
    getToolsByCategory(category: string): Tool[];
    
    /**
     * 檢查工具是否存在
     */
    hasToolInCategory(toolName: string, category: string): boolean;
    
    /**
     * 路由工具調用到對應的插件
     */
    routeToolCall(toolName: string, args: Record<string, any>): Promise<ToolResponse>;
    
    // 🔮 預留擴展接口
    onPluginRegistered?(plugin: IToolPlugin): void;
    onPluginUnregistered?(pluginName: string): void;
}

/**
 * 工作流程工具接口
 * 專門用於工作流程相關的工具定義
 */
export interface IWorkflowTool extends Tool {
    readonly workflowType: string; // 'novel' | 'article' | 'script' | 'custom'
    readonly stage?: string; // 適用的階段
    readonly dependencies?: string[]; // 依賴的其他工具
    
    // 🔮 預留工作流程特定擴展
    workflowExtensions?: Record<string, any>;
}

/**
 * 工具執行上下文
 * 提供工具執行時的環境信息
 */
export interface IToolExecutionContext {
    readonly toolName: string;
    readonly category: string;
    readonly workflowId?: string;
    readonly stageId?: string;
    readonly userId?: string;
    readonly sessionId?: string;
    readonly metadata: Record<string, any>;
    
    // 🔮 預留上下文擴展
    extensions?: Record<string, any>;
}

/**
 * 工具執行結果擴展接口
 */
export interface IExtendedToolResponse extends ToolResponse {
    readonly executionTime?: number;
    readonly resourceUsage?: {
        memory: number;
        cpu: number;
    };
    readonly context?: IToolExecutionContext;
    
    // 🔮 預留響應擴展
    extensions?: Record<string, any>;
}

/**
 * 工具中間件接口
 * 支持工具執行前後的攔截處理
 */
export interface IToolMiddleware {
    readonly name: string;
    readonly priority: number; // 執行優先級，數字越小優先級越高
    
    /**
     * 工具執行前的處理
     */
    beforeExecution?(
        toolName: string, 
        args: Record<string, any>, 
        context: IToolExecutionContext
    ): Promise<{ toolName: string; args: Record<string, any>; context: IToolExecutionContext }>;
    
    /**
     * 工具執行後的處理
     */
    afterExecution?(
        toolName: string, 
        result: IExtendedToolResponse, 
        context: IToolExecutionContext
    ): Promise<IExtendedToolResponse>;
    
    /**
     * 錯誤處理
     */
    onError?(
        toolName: string, 
        error: Error, 
        context: IToolExecutionContext
    ): Promise<IExtendedToolResponse | void>;
}

/**
 * 工具配置接口
 */
export interface IToolConfig {
    readonly enabled: boolean;
    readonly timeout: number; // 執行超時時間（毫秒）
    readonly retryCount: number; // 重試次數
    readonly rateLimiting?: {
        maxCalls: number;
        windowMs: number;
    };
    readonly permissions?: string[]; // 所需權限
    
    // 🔮 預留配置擴展
    extensions?: Record<string, any>;
}

/**
 * 工具管理器接口
 * 統一管理所有工具相關功能
 */
export interface IToolManager {
    /**
     * 工具註冊器
     */
    readonly registry: IToolRegistry;
    
    /**
     * 註冊中間件
     */
    registerMiddleware(middleware: IToolMiddleware): void;
    
    /**
     * 配置工具
     */
    configureTools(configs: Record<string, IToolConfig>): void;
    
    /**
     * 執行工具（帶中間件支持）
     */
    executeTool(
        toolName: string, 
        args: Record<string, any>, 
        context?: Partial<IToolExecutionContext>
    ): Promise<IExtendedToolResponse>;
    
    /**
     * 獲取工具統計信息
     */
    getToolStats(): ToolStats;
    
    /**
     * 健康檢查
     */
    healthCheck(): Promise<HealthCheckResult>;
    
    // 🔮 預留管理器擴展
    extensions?: Record<string, any>;
}

// === 支持類型定義 ===

export interface ToolStats {
    totalTools: number;
    totalPlugins: number;
    executionCount: Record<string, number>;
    errorCount: Record<string, number>;
    averageExecutionTime: Record<string, number>;
    // 🔮 預留統計擴展
    extensions?: Record<string, any>;
}

export interface HealthCheckResult {
    status: 'healthy' | 'degraded' | 'unhealthy';
    checks: {
        name: string;
        status: 'pass' | 'fail';
        message?: string;
    }[];
    timestamp: string;
    // 🔮 預留健康檢查擴展
    extensions?: Record<string, any>;
}

/**
 * 工具事件接口
 */
export interface IToolEvent {
    readonly type: 'tool_registered' | 'tool_executed' | 'tool_error' | 'plugin_loaded';
    readonly timestamp: string;
    readonly toolName?: string;
    readonly pluginName?: string;
    readonly data: any;
    
    // 🔮 預留事件擴展
    extensions?: Record<string, any>;
}

/**
 * 工具事件監聽器
 */
export type ToolEventListener = (event: IToolEvent) => void;

/**
 * 工具事件發射器接口
 */
export interface IToolEventEmitter {
    on(eventType: string, listener: ToolEventListener): void;
    off(eventType: string, listener: ToolEventListener): void;
    emit(event: IToolEvent): void;
}
