// src/integration/tools/handlers/WorkflowToolHandler.ts
import { BaseToolHandler } from './BaseToolHandler.js';
import { formatToolResponse, formatToolError } from '../../../shared/index.js';
import { WorkflowStateManager } from '../../../core/workflow/WorkflowStateManager.js';
import { PredefinedTemplates } from '../../../core/workflow/WorkflowTemplates.js';
/**
 * 工作流程工具處理器
 * 專門處理工作流程相關的工具調用
 */
export class WorkflowToolHandler extends BaseToolHandler {
    extendedStorage;
    workflowStateManager;
    constructor(knowledgeGraphManager, extendedStorage) {
        super(knowledgeGraphManager);
        this.extendedStorage = extendedStorage;
        this.workflowStateManager = new WorkflowStateManager(extendedStorage);
    }
    async handleTool(name, args) {
        const startTime = Date.now();
        try {
            this.validateArguments(args);
            let result;
            switch (name) {
                case "workflow_create":
                    result = await this.createWorkflow(args);
                    break;
                case "workflow_status":
                    result = await this.getWorkflowStatus(args);
                    break;
                case "workflow_advance":
                    result = await this.advanceWorkflow(args);
                    break;
                case "workflow_list":
                    result = await this.listWorkflows(args);
                    break;
                case "stage_validate":
                    result = await this.validateStage(args);
                    break;
                case "stage_complete":
                    result = await this.completeStage(args);
                    break;
                case "workflow_templates":
                    result = await this.getWorkflowTemplates(args);
                    break;
                case "workflow_pause":
                    result = await this.pauseWorkflow(args);
                    break;
                case "workflow_resume":
                    result = await this.resumeWorkflow(args);
                    break;
                case "workflow_delete":
                    result = await this.deleteWorkflow(args);
                    break;
                // 🔮 預留未來工具擴展點
                default:
                    // 檢查是否為插件提供的工具
                    result = await this.handlePluginTool(name, args);
            }
            const executionTime = Date.now() - startTime;
            return this.createExtendedResponse(result, {
                executionTime,
                context: this.createExecutionContext(name, args)
            });
        }
        catch (error) {
            return formatToolError({
                operation: name,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                context: { args },
                suggestions: [
                    "Check workflow ID exists",
                    "Verify stage requirements are met",
                    "Ensure workflow is in correct state"
                ],
                recoverySteps: [
                    "Use workflow_list to check available workflows",
                    "Use workflow_status to check current state",
                    "Use stage_validate to check stage requirements"
                ]
            });
        }
    }
    // === 核心工作流程操作 ===
    /**
     * 創建新的工作流程
     */
    async createWorkflow(args) {
        // 獲取模板
        let template;
        if (args.templateId) {
            template = PredefinedTemplates.getTemplateById(args.templateId) ||
                PredefinedTemplates.getCustomTemplate(args.templateId);
            if (!template) {
                throw new Error(`Template not found: ${args.templateId}`);
            }
        }
        else if (args.type === 'custom' && args.customStages) {
            // 創建自定義模板
            template = this.createCustomTemplate(args.name, args.customStages);
        }
        else {
            // 使用預定義模板
            template = PredefinedTemplates.getTemplatesByCategory(args.type)[0];
            if (!template) {
                throw new Error(`No template found for type: ${args.type}`);
            }
        }
        // 使用WorkflowStateManager創建工作流程
        const result = await this.workflowStateManager.createWorkflow(template, args.name, args.metadata);
        // 將節點和邊添加到知識圖譜
        await this.knowledgeGraphManager.addNodes(result.nodes);
        // 添加邊（如果有的話）
        if (result.edges && result.edges.length > 0) {
            const edges = result.edges.map(edge => ({
                ...edge,
                type: 'edge'
            }));
            // 使用現有的addEdges方法
            try {
                await this.knowledgeGraphManager.addEdges(edges);
            }
            catch (error) {
                // 如果addEdges方法不存在，記錄警告但不中斷流程
                console.warn('[WorkflowToolHandler] Edge addition failed, continuing without edges:', error);
            }
        }
        return {
            workflowId: result.workflowId,
            template: template.id,
            nodes: result.nodes.length,
            edges: result.edges.length,
            message: `Created workflow "${args.name}" using template "${template.name}"`
        };
    }
    /**
     * 獲取工作流程狀態
     */
    async getWorkflowStatus(args) {
        const workflow = await this.extendedStorage.loadWorkflowState(args.workflowId);
        if (!workflow) {
            throw new Error(`Workflow not found: ${args.workflowId}`);
        }
        const stages = await this.extendedStorage.loadStageStates(args.workflowId);
        return {
            workflow,
            stages,
            summary: {
                currentStage: workflow.currentStage,
                totalStages: workflow.totalStages,
                progress: workflow.progress,
                status: workflow.status
            }
        };
    }
    /**
     * 推進工作流程到下一階段
     */
    async advanceWorkflow(args) {
        const result = await this.workflowStateManager.advanceToNextStage(args.workflowId, {
            force: args.force,
            skipValidation: args.skipValidation
        });
        return {
            workflow: result.workflow,
            nextStage: result.nextStage,
            message: result.workflow.status === 'completed'
                ? 'Workflow completed successfully'
                : `Advanced to stage ${result.workflow.currentStage + 1}${result.nextStage ? ': ' + result.nextStage.name : ''}`
        };
    }
    /**
     * 列出所有工作流程
     */
    async listWorkflows(args) {
        let workflows = await this.extendedStorage.listWorkflows();
        // 過濾
        if (args.status) {
            workflows = workflows.filter(w => w.status === args.status);
        }
        if (args.type) {
            workflows = workflows.filter(w => w.metadata.type === args.type);
        }
        // 限制數量
        if (args.limit && args.limit > 0) {
            workflows = workflows.slice(0, args.limit);
        }
        return {
            workflows,
            count: workflows.length,
            summary: {
                byStatus: this.groupBy(workflows, 'status'),
                byType: this.groupBy(workflows, w => w.metadata.type)
            }
        };
    }
    /**
     * 驗證階段完成條件
     */
    async validateStage(args) {
        const stageId = args.stageId || `${args.workflowId}_stage_current`;
        const validation = await this.workflowStateManager.validateStage(args.workflowId, stageId);
        if (args.detailed) {
            const workflowValidation = await this.workflowStateManager.validateWorkflow(args.workflowId);
            return {
                stage: validation,
                workflow: workflowValidation,
                summary: {
                    canAdvance: validation.isComplete,
                    missingCount: validation.missingRequirements.length,
                    totalRequirements: validation.requirements.length
                }
            };
        }
        return validation;
    }
    /**
     * 完成階段
     */
    async completeStage(args) {
        const stages = await this.extendedStorage.loadStageStates(args.workflowId);
        const stage = stages.find(s => s.stageId === args.stageId);
        if (!stage) {
            throw new Error(`Stage not found: ${args.stageId}`);
        }
        if (!args.force) {
            const validation = await this.workflowStateManager.validateStage(args.workflowId, args.stageId);
            if (!validation.isComplete) {
                throw new Error(`Stage requirements not met: ${validation.missingRequirements.join(', ')}. Use force=true to override.`);
            }
        }
        stage.status = 'completed';
        if (args.notes) {
            stage.extensions = { ...stage.extensions, completionNotes: args.notes };
        }
        await this.extendedStorage.saveStageState(stage);
        return {
            stage,
            message: `Stage "${stage.name}" marked as completed${args.notes ? ' with notes' : ''}`
        };
    }
    /**
     * 獲取工作流程模板
     */
    async getWorkflowTemplates(args) {
        let templates;
        if (args.category) {
            templates = PredefinedTemplates.getTemplatesByCategory(args.category);
        }
        else {
            templates = PredefinedTemplates.getAllTemplates();
        }
        if (args.includeCustom !== false) {
            const customTemplates = Array.from(PredefinedTemplates.getAllTemplatesIncludingCustom())
                .filter(t => !templates.find((pt) => pt.id === t.id));
            templates = [...templates, ...customTemplates];
        }
        return {
            templates,
            count: templates.length,
            categories: [...new Set(templates.map(t => t.category))]
        };
    }
    /**
     * 暫停工作流程
     */
    async pauseWorkflow(args) {
        const workflow = await this.workflowStateManager.updateWorkflowState(args.workflowId, {
            status: 'paused',
            metadata: {
                pauseReason: args.reason,
                pausedAt: new Date().toISOString()
            }
        });
        return {
            workflow,
            message: `Workflow paused${args.reason ? ': ' + args.reason : ''}`
        };
    }
    /**
     * 恢復工作流程
     */
    async resumeWorkflow(args) {
        const workflow = await this.workflowStateManager.updateWorkflowState(args.workflowId, {
            status: 'in_progress',
            metadata: {
                resumeNotes: args.notes,
                resumedAt: new Date().toISOString()
            }
        });
        return {
            workflow,
            message: `Workflow resumed${args.notes ? ' with notes' : ''}`
        };
    }
    /**
     * 刪除工作流程
     */
    async deleteWorkflow(args) {
        const workflow = await this.extendedStorage.loadWorkflowState(args.workflowId);
        if (!workflow) {
            throw new Error(`Workflow not found: ${args.workflowId}`);
        }
        if (!args.force && workflow.status === 'in_progress') {
            throw new Error('Cannot delete active workflow. Use force=true to override.');
        }
        // 刪除階段狀態
        const stages = await this.extendedStorage.loadStageStates(args.workflowId);
        for (const stage of stages) {
            // 這裡需要實現階段狀態刪除邏輯
        }
        // 刪除工作流程狀態
        // 這裡需要實現工作流程狀態刪除邏輯
        if (!args.keepNodes) {
            // 刪除相關節點
            const workflowNodes = await this.extendedStorage.queryNodesByWorkflow(args.workflowId);
            const nodeNames = workflowNodes.map(n => n.name);
            if (nodeNames.length > 0) {
                await this.knowledgeGraphManager.deleteNodes(nodeNames);
            }
        }
        return {
            workflowId: args.workflowId,
            deletedNodes: args.keepNodes ? 0 : workflow.totalStages + 1,
            message: `Workflow deleted${args.keepNodes ? ' (nodes preserved)' : ''}`
        };
    }
    // === 🔮 插件工具處理預留接口 ===
    async handlePluginTool(name, args) {
        // 這裡可以實現插件工具的路由邏輯
        // 目前返回未實現錯誤
        throw new Error(`Plugin tool not implemented: ${name}`);
    }
    // === 輔助方法 ===
    createCustomTemplate(name, stages) {
        return {
            id: `custom_${Date.now()}`,
            name: `Custom: ${name}`,
            description: 'User-defined custom workflow',
            category: 'custom',
            version: '1.0.0',
            stages: stages.map((stageName, index) => ({
                id: stageName.toLowerCase().replace(/\s+/g, '_'),
                name: stageName,
                description: `Custom stage: ${stageName}`,
                order: index,
                requiredNodeTypes: ['content'],
                optionalNodeTypes: [],
                completionCriteria: {
                    minNodes: 1,
                    requiredFields: ['name']
                }
            }))
        };
    }
    getStageRequirements(stageName) {
        const requirements = {
            'planning': ['character', 'setting', 'theme'],
            'outline': ['plotarc', 'timeline'],
            'chapter': ['scene', 'dialogue'],
            'generation': ['chapter']
        };
        return requirements[stageName] || [];
    }
    getCompletionCriteria(stageName) {
        const criteria = {
            'planning': { minNodes: 3, requiredTypes: ['character', 'setting'] },
            'outline': { minNodes: 1, requiredTypes: ['plotarc'] },
            'chapter': { minNodes: 5, requiredTypes: ['scene'] }
        };
        return criteria[stageName] || { minNodes: 1 };
    }
    async isStageComplete(stage) {
        // 檢查階段完成條件
        const nodes = await this.extendedStorage.queryNodesByWorkflow(stage.workflowId);
        const stageNodes = nodes.filter(node => stage.requiredNodeTypes.some(type => node.nodeType === type));
        const minNodes = stage.completionCriteria.minNodes || 1;
        return stageNodes.length >= minNodes;
    }
    async checkStageRequirements(stage) {
        const nodes = await this.extendedStorage.queryNodesByWorkflow(stage.workflowId);
        const requirements = stage.requiredNodeTypes.map(type => {
            const typeNodes = nodes.filter(n => n.nodeType === type);
            return {
                type,
                required: true,
                found: typeNodes.length,
                met: typeNodes.length > 0
            };
        });
        return {
            requirements,
            allMet: requirements.every(req => req.met)
        };
    }
    createExtendedResponse(result, extensions) {
        return {
            ...formatToolResponse({
                data: result,
                actionTaken: `Workflow operation completed`
            }),
            ...extensions
        };
    }
    createExecutionContext(toolName, args) {
        return {
            toolName,
            category: 'workflow',
            workflowId: args.workflowId,
            metadata: args
        };
    }
    groupBy(array, keyFn) {
        const result = {};
        array.forEach(item => {
            const key = typeof keyFn === 'string' ? item[keyFn] : keyFn(item);
            result[key] = (result[key] || 0) + 1;
        });
        return result;
    }
}
//# sourceMappingURL=WorkflowToolHandler.js.map