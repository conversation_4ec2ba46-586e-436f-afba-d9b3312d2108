# 🤖 MemoryMesh AI Agent 使用指南 v0.3.0

**專為AI助手設計的完整MCP工具調用指南** - 涵蓋所有12個工作流程管理工具的詳細使用方法、最佳實踐和故障排除

[![Version](https://img.shields.io/badge/version-0.3.0-blue.svg)](README.md)
[![MCP](https://img.shields.io/badge/MCP-Compatible-purple.svg)](https://modelcontextprotocol.io/)
[![AI](https://img.shields.io/badge/AI-Optimized-green.svg)](https://github.com/CheMiguel23/MemoryMesh)

---

## 📚 **目錄**

1. [快速開始](#-快速開始)
2. [核心工具詳解](#-核心工具詳解)
3. [完整使用場景](#-完整使用場景)
4. [最佳實踐](#-最佳實踐)
5. [錯誤處理](#-錯誤處理)
6. [性能優化](#-性能優化)
7. [集成指導](#-集成指導)

---

## 🚀 **快速開始**

### **基本概念**
MemoryMesh v0.3.0 提供 **12個MCP工具** 用於完整的工作流程管理：

#### **工具分類**
- **基礎管理** (4個): `workflow_create`, `workflow_list`, `workflow_status`, `workflow_templates`
- **流程控制** (3個): `workflow_advance`, `workflow_pause`, `workflow_resume`
- **階段管理** (2個): `stage_validate`, `stage_complete`
- **高級功能** (3個): `workflow_delete`, `workflow_export`, `workflow_import`

#### **第一個工作流程**
```typescript
// 1. 查看可用模板
const templates = await workflow_templates();

// 2. 創建工作流程
const workflow = await workflow_create({
  name: "我的第一個小說",
  type: "novel",
  metadata: {
    genre: "奇幻",
    targetAudience: "青少年"
  }
});

// 3. 檢查狀態
const status = await workflow_status({
  workflowId: workflow.workflowId,
  includeStages: true
});

// 4. 推進階段
const advance = await workflow_advance({
  workflowId: workflow.workflowId
});
```

---

## 🛠️ **核心工具詳解**

### **1. workflow_create - 創建工作流程**

#### **功能描述**
創建新的工作流程，支援6種專業模板。

#### **參數說明**
```typescript
interface WorkflowCreateParams {
  name: string;                    // 工作流程名稱 (必填)
  type: WorkflowType;             // 模板類型 (必填)
  templateId?: string;            // 自定義模板ID (可選)
  customStages?: string[];        // 自定義階段 (type=custom時)
  metadata?: Record<string, any>; // 額外元數據 (可選)
  extensions?: Record<string, any>; // 擴展參數 (可選)
}

type WorkflowType = 'novel' | 'article' | 'script' | 'academic' | 'technical' | 'creative' | 'custom';
```

#### **使用示例**
```typescript
// 小說創作工作流程
const novelWorkflow = await workflow_create({
  name: "龍族傳說_第一部",
  type: "novel",
  metadata: {
    genre: "奇幻",
    targetAudience: "青少年",
    estimatedLength: "80000字",
    language: "繁體中文"
  }
});

// 學術論文工作流程
const academicWorkflow = await workflow_create({
  name: "AI在教育中的應用研究",
  type: "academic",
  metadata: {
    field: "教育技術",
    researchType: "實證研究",
    targetJournal: "教育科技期刊",
    deadline: "2024-12-31"
  }
});

// 技術文檔工作流程
const techDocWorkflow = await workflow_create({
  name: "REST API開發者指南",
  type: "technical",
  metadata: {
    docType: "API文檔",
    targetAudience: "開發者",
    complexity: "中級",
    platform: "Web"
  }
});
```

#### **返回值**
```typescript
interface WorkflowCreateResponse {
  workflowId: string;      // 工作流程唯一ID
  template: string;        // 使用的模板ID
  nodes: number;          // 創建的節點數量
  edges: number;          // 創建的邊數量
  message: string;        // 成功消息
}
```

#### **錯誤處理**
```typescript
try {
  const workflow = await workflow_create({
    name: "測試工作流程",
    type: "novel"
  });
} catch (error) {
  if (error.message.includes("Smart duplicate detection")) {
    // 處理重複檢測錯誤
    console.log("檢測到相似工作流程，建議使用不同名稱");
  } else if (error.message.includes("Invalid template")) {
    // 處理模板錯誤
    console.log("模板類型無效，請檢查支援的類型");
  }
}
```

---

### **2. workflow_status - 查看工作流程狀態**

#### **功能描述**
獲取工作流程的詳細狀態信息，包括當前階段、進度和完成情況。

#### **參數說明**
```typescript
interface WorkflowStatusParams {
  workflowId: string;           // 工作流程ID (必填)
  includeStages?: boolean;      // 是否包含階段詳情 (默認: true)
  includeValidation?: boolean;  // 是否包含驗證結果 (默認: false)
  extensions?: Record<string, any>; // 擴展參數 (可選)
}
```

#### **使用示例**
```typescript
// 基本狀態查詢
const basicStatus = await workflow_status({
  workflowId: "wf_1234567890_abcdef"
});

// 詳細狀態查詢 (包含階段和驗證)
const detailedStatus = await workflow_status({
  workflowId: "wf_1234567890_abcdef",
  includeStages: true,
  includeValidation: true
});

// 檢查特定工作流程的進度
const checkProgress = async (workflowId: string) => {
  const status = await workflow_status({ workflowId });

  console.log(`工作流程: ${status.workflow.name}`);
  console.log(`當前階段: ${status.workflow.currentStage + 1}/${status.workflow.totalStages}`);
  console.log(`完成度: ${status.workflow.progress}%`);
  console.log(`狀態: ${status.workflow.status}`);

  return status;
};
```

#### **返回值結構**
```typescript
interface WorkflowStatusResponse {
  workflow: {
    workflowId: string;
    name: string;
    status: 'not_started' | 'in_progress' | 'completed' | 'paused';
    currentStage: number;
    totalStages: number;
    progress: number;        // 0-100
    metadata: Record<string, any>;
    createdAt: string;
    updatedAt: string;
  };
  stages?: Array<{
    stageId: string;
    workflowId: string;
    name: string;
    order: number;
    status: 'pending' | 'active' | 'completed';
    requiredNodeTypes: string[];
    completionCriteria: {
      minNodes: number;
      requiredFields: string[];
    };
  }>;
  summary: {
    currentStage: number;
    totalStages: number;
    progress: number;
    status: string;
  };
}
```

---

### **3. workflow_advance - 推進工作流程**

#### **功能描述**
將工作流程推進到下一個階段，支援強制推進和驗證跳過。

#### **參數說明**
```typescript
interface WorkflowAdvanceParams {
  workflowId: string;           // 工作流程ID (必填)
  force?: boolean;              // 是否強制推進 (默認: false)
  skipValidation?: boolean;     // 是否跳過驗證 (默認: false)
  extensions?: Record<string, any>; // 擴展參數 (可選)
}
```

#### **使用示例**
```typescript
// 正常推進 (會檢查完成條件)
const normalAdvance = await workflow_advance({
  workflowId: "wf_1234567890_abcdef"
});

// 強制推進 (忽略完成條件)
const forceAdvance = await workflow_advance({
  workflowId: "wf_1234567890_abcdef",
  force: true
});

// 跳過驗證推進
const skipValidationAdvance = await workflow_advance({
  workflowId: "wf_1234567890_abcdef",
  skipValidation: true
});

// 智能推進函數
const smartAdvance = async (workflowId: string) => {
  try {
    // 先嘗試正常推進
    return await workflow_advance({ workflowId });
  } catch (error) {
    if (error.message.includes("completion criteria")) {
      console.log("階段未完成，是否要強制推進？");
      // 根據用戶選擇決定是否強制推進
      return await workflow_advance({ workflowId, force: true });
    }
    throw error;
  }
};
```

#### **返回值**
```typescript
interface WorkflowAdvanceResponse {
  workflow: {
    workflowId: string;
    name: string;
    status: string;
    currentStage: number;
    totalStages: number;
    progress: number;
    // ... 其他工作流程信息
  };
  nextStage?: {
    stageId: string;
    name: string;
    order: number;
    status: string;
    requiredNodeTypes: string[];
    completionCriteria: object;
  };
  message: string;
}
```

---

### **4. workflow_list - 列出工作流程**

#### **功能描述**
列出所有工作流程，支援按狀態、類型等條件過濾和排序。

#### **參數說明**
```typescript
interface WorkflowListParams {
  status?: 'not_started' | 'in_progress' | 'completed' | 'paused'; // 狀態過濾
  type?: WorkflowType;          // 類型過濾
  limit?: number;               // 返回數量限制 (默認: 20, 最大: 100)
  offset?: number;              // 分頁偏移量 (默認: 0)
  sortBy?: 'created_at' | 'updated_at' | 'progress' | 'name'; // 排序字段
  sortOrder?: 'asc' | 'desc';   // 排序順序 (默認: desc)
  extensions?: Record<string, any>; // 擴展參數
}
```

#### **使用示例**
```typescript
// 列出所有工作流程
const allWorkflows = await workflow_list({});

// 列出進行中的小說工作流程
const novelInProgress = await workflow_list({
  status: "in_progress",
  type: "novel",
  sortBy: "updated_at"
});

// 分頁查詢
const pagedWorkflows = await workflow_list({
  limit: 10,
  offset: 20,
  sortBy: "progress",
  sortOrder: "desc"
});

// 查找特定類型的工作流程
const findWorkflowsByType = async (type: WorkflowType) => {
  const result = await workflow_list({ type });
  return result.workflows.map(w => ({
    id: w.workflowId,
    name: w.name,
    progress: w.progress,
    status: w.status
  }));
};
```

---

### **5. stage_validate - 驗證階段完成條件**

#### **功能描述**
檢查指定階段的完成條件，確認所需節點和要求是否滿足。

#### **參數說明**
```typescript
interface StageValidateParams {
  workflowId: string;           // 工作流程ID (必填)
  stageId?: string;             // 階段ID (可選，默認當前階段)
  detailed?: boolean;           // 是否返回詳細信息 (默認: true)
  extensions?: Record<string, any>; // 擴展參數
}
```

#### **使用示例**
```typescript
// 驗證當前階段
const currentStageValidation = await stage_validate({
  workflowId: "wf_1234567890_abcdef"
});

// 驗證特定階段
const specificStageValidation = await stage_validate({
  workflowId: "wf_1234567890_abcdef",
  stageId: "stage_planning",
  detailed: true
});

// 智能驗證函數
const smartValidate = async (workflowId: string) => {
  const validation = await stage_validate({ workflowId, detailed: true });

  if (validation.isValid) {
    console.log("✅ 階段完成條件已滿足，可以推進");
    return true;
  } else {
    console.log("❌ 階段未完成，缺少以下要求：");
    validation.missingRequirements?.forEach(req => {
      console.log(`  - ${req}`);
    });
    return false;
  }
};
```

---

### **6. workflow_pause / workflow_resume - 暫停和恢復**

#### **暫停工作流程**
```typescript
interface WorkflowPauseParams {
  workflowId: string;           // 工作流程ID (必填)
  reason?: string;              // 暫停原因 (可選)
  extensions?: Record<string, any>; // 擴展參數
}

// 使用示例
const pauseWorkflow = await workflow_pause({
  workflowId: "wf_1234567890_abcdef",
  reason: "需要更多研究時間"
});
```

#### **恢復工作流程**
```typescript
interface WorkflowResumeParams {
  workflowId: string;           // 工作流程ID (必填)
  notes?: string;               // 恢復備註 (可選)
  extensions?: Record<string, any>; // 擴展參數
}

// 使用示例
const resumeWorkflow = await workflow_resume({
  workflowId: "wf_1234567890_abcdef",
  notes: "研究完成，繼續創作"
});
```

---

### **7. workflow_templates - 獲取模板**

#### **功能描述**
獲取所有可用的工作流程模板，支援按類別過濾。

#### **參數說明**
```typescript
interface WorkflowTemplatesParams {
  category?: 'novel' | 'article' | 'script' | 'academic' | 'technical' | 'creative' | 'custom';
  includeCustom?: boolean;      // 是否包含自定義模板 (默認: true)
  extensions?: Record<string, any>; // 擴展參數
}
```

#### **使用示例**
```typescript
// 獲取所有模板
const allTemplates = await workflow_templates({});

// 獲取特定類別模板
const novelTemplates = await workflow_templates({
  category: "novel"
});

// 模板選擇助手
const selectTemplate = async (userPreference: string) => {
  const templates = await workflow_templates({});

  // 根據用戶偏好推薦模板
  const recommendations = templates.templates.filter(template =>
    template.description.toLowerCase().includes(userPreference.toLowerCase())
  );

  return recommendations.length > 0 ? recommendations[0] : templates.templates[0];
};
```

---

## 🎯 **完整使用場景**

### **場景1: 小說創作完整流程 (v0.3.1 增強版)**

```typescript
async function createEnhancedNovelWorkflow() {
  // 1. 創建小說工作流程
  const workflow = await workflow_create({
    name: "奇幻冒險_龍族傳說",
    type: "novel",
    metadata: {
      genre: "奇幻",
      targetAudience: "青少年",
      estimatedLength: "80000字",
      setting: "中世紀奇幻世界",
      themes: ["成長", "友誼", "責任"],
      writingStyle: "第三人稱全知視角"
    }
  });

  console.log(`✅ 創建工作流程: ${workflow.workflowId}`);

  // 2. 規劃階段 - 創建深度角色
  await add_character({
    character: {
      name: "艾莉亞·星辰",
      role: "protagonist",
      status: "Active",
      currentLocation: ["龍騎士學院"],
      description: "年輕的龍騎士學徒，擁有罕見的星辰魔法",
      traits: ["勇敢", "好奇", "有責任感", "過度自信"], // 包含缺陷
      background: "來自偏遠村莊的農家女孩，渴望證明自己的價值",
      motivation: "成為最偉大的龍騎士，保護無辜的人們",
      internalConflict: "自信與自我懷疑的掙扎",
      characterArc: "從衝動的學徒成長為智慧的領導者"
    }
  });

  // 3. 添加豐富的設定描述
  await add_setting({
    setting: {
      name: "龍騎士學院",
      type: "Building",
      description: "坐落在雲霧繚繞的山峰上，古老的石塔直插雲霄，訓練場上回響著劍刃碰撞的聲音",
      status: "Active",
      significance: "Major",
      atmosphere: "神秘而莊嚴，充滿古老魔法的氣息",
      sensoryDetails: "石頭的冰冷觸感，龍鱗的金屬光澤，訓練時的汗水味道",
      culturalContext: "千年傳統的騎士精神，嚴格的等級制度"
    }
  });

  // 4. 添加明確的主題
  await add_nodes([{
    name: "成長主題",
    nodeType: "theme",
    metadata: [
      "核心概念: 從幼稚到成熟的蛻變過程",
      "體現方式: 通過面對失敗、學習謙遜、承擔責任來展現",
      "象徵元素: 龍蛋孵化、星辰魔法覺醒、師父的教導",
      "情節整合: 每個重大事件都推動角色的內在成長"
    ]
  }]);

  // 5. 使用增強的階段驗證
  const validation = await stage_validate({
    workflowId: workflow.workflowId,
    detailed: true
  });

  console.log("📊 階段驗證結果:");
  console.log(`完成狀態: ${validation.isComplete ? '✅' : '❌'}`);
  console.log(`質量分數: ${validation.qualityAssessment?.overallScore}/100`);

  // 6. 處理智能反饋
  if (!validation.isComplete) {
    console.log("\n🎯 下一步指導:");
    validation.nextStepGuidance?.immediateActions.forEach(action => {
      console.log(`  • ${action}`);
    });

    console.log("\n💡 質量提升建議:");
    validation.nextStepGuidance?.qualityTips.forEach(tip => {
      console.log(`  • ${tip}`);
    });

    console.log("\n📝 內容示例:");
    validation.nextStepGuidance?.contentExamples.forEach(example => {
      console.log(`  • ${example}`);
    });
  }

  // 7. 顯示進度洞察
  if (validation.progressInsights) {
    console.log("\n🔍 進度洞察:");
    console.log(`當前重點: ${validation.progressInsights.currentFocus}`);

    if (validation.progressInsights.strengthAreas.length > 0) {
      console.log("優勢領域:");
      validation.progressInsights.strengthAreas.forEach(area => {
        console.log(`  ✅ ${area}`);
      });
    }

    if (validation.progressInsights.improvementAreas.length > 0) {
      console.log("改進領域:");
      validation.progressInsights.improvementAreas.forEach(area => {
        console.log(`  🔧 ${area}`);
      });
    }
  }

  // 8. 智能推進決策
  if (validation.isComplete && validation.qualityAssessment?.overallScore >= 70) {
    const advance = await workflow_advance({
      workflowId: workflow.workflowId
    });

    console.log(`\n🚀 成功推進到: ${advance.nextStage?.name}`);

    // 預覽下一階段的挑戰
    if (validation.progressInsights?.upcomingChallenges) {
      console.log("即將面臨的挑戰:");
      validation.progressInsights.upcomingChallenges.forEach(challenge => {
        console.log(`  ⚠️ ${challenge}`);
      });
    }
  } else {
    console.log("\n⏸️ 建議完善當前階段後再推進");
  }

  return workflow.workflowId;
}
```

#### **v0.3.1 新增功能說明**

**1. 增強的質量檢查**
- 每個階段都有具體的質量標準和評分機制
- 自動評估內容深度、一致性和完整性
- 提供量化的質量分數 (0-100)

**2. 智能指導系統**
- 基於當前進度提供具體的行動建議
- 推薦合適的工具和操作步驟
- 提供實際的內容示例和質量提升技巧

**3. 進度洞察分析**
- 識別當前階段的重點和挑戰
- 分析已完成工作的優勢領域
- 預測下一階段可能遇到的困難

**4. 內容指導原則**
```typescript
// 角色創建的新標準
const characterGuidelines = {
  depth: "三維角色：外在目標 + 內在需求 + 致命缺陷",
  voice: "獨特的說話方式和行為模式",
  arc: "明確的成長軌跡和變化過程",
  motivation: "合理且引人共鳴的動機驅動"
};

// 設定描述的新要求
const settingGuidelines = {
  sensory: "使用五感描述增強沉浸感",
  emotional: "環境反映角色內心狀態",
  cultural: "包含文化背景和社會結構",
  functional: "設定對情節發展的影響"
};

// 主題整合的新方法
const themeGuidelines = {
  subtlety: "通過故事自然流露，避免說教",
  integration: "與角色弧線和情節發展一致",
  depth: "多層次的主題探索和表達",
  resonance: "與目標讀者產生情感共鳴"
};
```

### **場景2: 學術論文研究流程**

```typescript
async function createAcademicWorkflow() {
  // 1. 創建學術論文工作流程
  const workflow = await workflow_create({
    name: "AI在個性化學習中的應用效果研究",
    type: "academic",
    metadata: {
      field: "教育技術",
      researchType: "實證研究",
      methodology: "混合方法",
      targetJournal: "教育科技期刊",
      deadline: "2024-12-31"
    }
  });

  // 2. 文獻回顧階段 - 添加文獻
  await add_nodes([{
    name: "Smith2023_PersonalizedLearning",
    nodeType: "literature",
    metadata: [
      "作者: Smith, J. et al.",
      "年份: 2023",
      "標題: Personalized Learning with AI: A Systematic Review",
      "期刊: Educational Technology Research",
      "關鍵發現: AI個性化學習可提升學習效果15-25%"
    ]
  }]);

  // 3. 檢查進度並推進
  const status = await workflow_status({
    workflowId: workflow.workflowId,
    includeStages: true
  });

  console.log(`當前階段: ${status.stages?.[status.workflow.currentStage]?.name}`);

  return workflow.workflowId;
}
```

### **場景3: 技術文檔創建流程**

```typescript
async function createTechnicalDocWorkflow() {
  // 1. 創建技術文檔工作流程
  const workflow = await workflow_create({
    name: "RESTful API 開發者指南 v2.0",
    type: "technical",
    metadata: {
      docType: "API文檔",
      targetAudience: "後端開發者",
      complexity: "中級",
      apiVersion: "2.0",
      platform: "Node.js"
    }
  });

  // 2. 需求分析階段 - 定義目標受眾
  await add_nodes([{
    name: "目標受眾_後端開發者",
    nodeType: "audience",
    metadata: [
      "經驗水平: 中級 (2-5年)",
      "技術背景: Node.js, Express.js",
      "主要需求: 快速上手API使用",
      "痛點: 缺乏實際代碼示例"
    ]
  }]);

  // 3. 定義文檔範圍
  await add_nodes([{
    name: "文檔範圍_API_v2",
    nodeType: "scope",
    metadata: [
      "包含: 所有v2.0端點",
      "認證: JWT Token",
      "格式: JSON",
      "示例: 完整代碼示例",
      "語言: JavaScript, Python, cURL"
    ]
  }]);

  return workflow.workflowId;
}
```

---

## 💡 **最佳實踐**

### **1. 工作流程命名規範**
```typescript
// ✅ 好的命名
"科幻小說_星際探索_第一部"
"學術論文_AI教育應用_2024"
"技術文檔_REST_API_v2.0"

// ❌ 避免的命名
"我的項目"
"新文檔"
"test123"
```

### **2. 元數據最佳實踐**
```typescript
// ✅ 豐富的元數據
const workflow = await workflow_create({
  name: "奇幻小說_龍族傳說",
  type: "novel",
  metadata: {
    // 基本信息
    genre: "奇幻",
    subGenre: "史詩奇幻",
    targetAudience: "青少年",

    // 技術規格
    estimatedLength: "80000字",
    language: "繁體中文",
    format: "電子書",

    // 項目管理
    deadline: "2024-12-31",
    priority: "高",
    collaborators: ["作者", "編輯"],

    // 創作設定
    setting: "中世紀奇幻世界",
    themes: ["成長", "友誼", "責任"],
    inspirations: ["托爾金", "羅琳"]
  }
});
```

### **3. 錯誤處理模式**
```typescript
async function robustWorkflowOperation(workflowId: string) {
  try {
    // 1. 先檢查工作流程是否存在
    const status = await workflow_status({ workflowId });

    // 2. 檢查是否可以推進
    const validation = await stage_validate({ workflowId });

    if (!validation.isValid) {
      console.log("階段未完成，需要添加更多內容");
      return { success: false, reason: "incomplete_stage" };
    }

    // 3. 推進工作流程
    const result = await workflow_advance({ workflowId });

    return { success: true, result };

  } catch (error) {
    // 4. 詳細錯誤處理
    if (error.message.includes("not found")) {
      return { success: false, reason: "workflow_not_found" };
    } else if (error.message.includes("duplicate")) {
      return { success: false, reason: "duplicate_detected" };
    } else {
      console.error("未知錯誤:", error);
      return { success: false, reason: "unknown_error", error };
    }
  }
}
```

### **4. 小說創作專業最佳實踐 (v0.3.1 新增)**

#### **4.1 階段式質量控制**
```typescript
async function novelQualityControl(workflowId: string) {
  // 1. 獲取當前階段狀態
  const status = await workflow_status({
    workflowId,
    includeStages: true,
    includeValidation: true
  });

  const currentStage = status.stages?.[status.workflow.currentStage];

  // 2. 執行深度質量檢查
  const validation = await stage_validate({
    workflowId,
    detailed: true
  });

  // 3. 基於階段類型的專業建議
  const stageSpecificGuidance = getStageSpecificGuidance(currentStage?.id, validation);

  console.log(`📚 ${currentStage?.name} 專業指導:`);
  stageSpecificGuidance.forEach(guidance => {
    console.log(`  • ${guidance}`);
  });

  // 4. 質量分數分析
  if (validation.qualityAssessment) {
    const score = validation.qualityAssessment.overallScore;
    const level = getQualityLevel(score);

    console.log(`\n📊 質量評估: ${score}/100 (${level})`);

    if (score < 80) {
      console.log("🔧 重點改進建議:");
      validation.qualityAssessment.recommendations.forEach(rec => {
        console.log(`  • ${rec}`);
      });
    }
  }

  return validation;
}

function getStageSpecificGuidance(stageId: string, validation: any): string[] {
  const guidance: string[] = [];

  switch (stageId) {
    case 'planning':
      guidance.push("確保每個角色都有明確的動機和成長弧線");
      guidance.push("設定描述要包含感官細節和文化背景");
      guidance.push("主題應該具體且能通過故事元素體現");
      if (validation.qualityAssessment?.overallScore < 70) {
        guidance.push("考慮添加更多的角色背景故事和內心衝突");
        guidance.push("豐富世界觀設定，增加獨特的文化元素");
      }
      break;

    case 'outline':
      guidance.push("確保情節結構有清晰的起承轉合");
      guidance.push("注意節奏安排，避免中段拖沓");
      guidance.push("每個情節點都要推動故事前進");
      if (validation.qualityAssessment?.overallScore < 75) {
        guidance.push("檢查衝突升級是否合理且引人入勝");
        guidance.push("確保高潮是情感和行動的雙重頂點");
      }
      break;

    case 'chapter':
      guidance.push("每個場景都要有明確的目的和衝突");
      guidance.push("平衡行動、對話、描述和內心獨白");
      guidance.push("確保章節有開頭鉤子和結尾懸念");
      if (validation.qualityAssessment?.overallScore < 80) {
        guidance.push("增加感官細節，提升場景的沉浸感");
        guidance.push("檢查對話是否符合角色性格且推動情節");
      }
      break;

    case 'generation':
      guidance.push("保持敘述風格和視角的一致性");
      guidance.push("確保角色聲音獨特且一致");
      guidance.push("主題要自然融入故事中");
      if (validation.qualityAssessment?.overallScore < 85) {
        guidance.push("進行全面的文筆潤色和語言優化");
        guidance.push("檢查整體結構的邏輯性和完整性");
      }
      break;
  }

  return guidance;
}

function getQualityLevel(score: number): string {
  if (score >= 90) return "優秀";
  if (score >= 80) return "良好";
  if (score >= 70) return "合格";
  if (score >= 60) return "需改進";
  return "不合格";
}
```

#### **4.2 智能內容生成助手**
```typescript
async function intelligentContentGeneration(workflowId: string, contentType: string) {
  // 1. 分析當前上下文
  const status = await workflow_status({ workflowId, includeStages: true });
  const validation = await stage_validate({ workflowId, detailed: true });

  // 2. 基於上下文生成內容建議
  const suggestions = generateContentSuggestions(contentType, status, validation);

  console.log(`💡 ${contentType} 創作建議:`);
  suggestions.forEach((suggestion, index) => {
    console.log(`${index + 1}. ${suggestion.title}`);
    console.log(`   描述: ${suggestion.description}`);
    console.log(`   示例: ${suggestion.example}`);
    console.log(`   技巧: ${suggestion.tips.join(', ')}`);
    console.log('');
  });

  return suggestions;
}

function generateContentSuggestions(contentType: string, status: any, validation: any) {
  const suggestions = [];

  switch (contentType) {
    case 'character':
      suggestions.push({
        title: "三維角色構建法",
        description: "創建具有外在目標、內在需求和致命缺陷的立體角色",
        example: "主角：外在目標是拯救王國，內在需求是獲得父親認可，致命缺陷是過度自信",
        tips: ["避免完美角色", "確保缺陷與情節相關", "給角色獨特的說話方式"]
      });

      suggestions.push({
        title: "角色關係網絡",
        description: "建立角色間的複雜關係，創造衝突和張力",
        example: "主角與導師的師生關係，與反派的宿敵關係，與愛人的情感關係",
        tips: ["每種關係都要有發展弧線", "利用關係推動情節", "避免單一化的關係模式"]
      });
      break;

    case 'scene':
      suggestions.push({
        title: "場景構建四要素",
        description: "目標、衝突、災難、反應的完整循環",
        example: "目標：主角要說服國王；衝突：國王不信任；災難：被驅逐出宮；反應：決心證明自己",
        tips: ["每個場景都要推進故事", "使用感官細節", "平衡對話和行動"]
      });

      suggestions.push({
        title: "情感節拍控制",
        description: "在場景中創造情感的起伏變化",
        example: "開始：緊張 → 發展：希望 → 轉折：絕望 → 結尾：決心",
        tips: ["避免情感平淡", "使用肢體語言", "內心獨白要適度"]
      });
      break;
  }

  return suggestions;
}
```

#### **4.3 批量工作流程管理**
```typescript
async function batchNovelWorkflowManagement() {
  // 1. 獲取所有小說工作流程
  const novelWorkflows = await workflow_list({
    type: "novel",
    status: "in_progress",
    limit: 50
  });

  // 2. 批量質量檢查
  const qualityReports = await Promise.allSettled(
    novelWorkflows.workflows.map(async (workflow) => {
      const validation = await stage_validate({
        workflowId: workflow.workflowId,
        detailed: true
      });

      return {
        workflowId: workflow.workflowId,
        name: workflow.name,
        currentStage: workflow.currentStage,
        qualityScore: validation.qualityAssessment?.overallScore || 0,
        canAdvance: validation.isComplete && validation.qualityAssessment?.overallScore >= 70,
        recommendations: validation.qualityAssessment?.recommendations || []
      };
    })
  );

  // 3. 生成批量報告
  console.log("📊 小說工作流程質量報告:");
  console.log("=" .repeat(50));

  qualityReports.forEach((result, index) => {
    if (result.status === "fulfilled") {
      const report = result.value;
      console.log(`\n📖 ${report.name}`);
      console.log(`   階段: ${report.currentStage + 1}/4`);
      console.log(`   質量: ${report.qualityScore}/100`);
      console.log(`   狀態: ${report.canAdvance ? '✅ 可推進' : '⏸️ 需改進'}`);

      if (report.recommendations.length > 0) {
        console.log(`   建議: ${report.recommendations.slice(0, 2).join('; ')}`);
      }
    }
  });

  // 4. 自動推進高質量工作流程
  const advanceable = qualityReports
    .filter(r => r.status === "fulfilled" && r.value.canAdvance)
    .map(r => r.value);

  if (advanceable.length > 0) {
    console.log(`\n🚀 自動推進 ${advanceable.length} 個高質量工作流程...`);

    for (const workflow of advanceable) {
      try {
        await workflow_advance({ workflowId: workflow.workflowId });
        console.log(`✅ ${workflow.name} 已推進到下一階段`);
      } catch (error) {
        console.log(`❌ ${workflow.name} 推進失敗: ${error.message}`);
      }
    }
  }

  return qualityReports;
}
```

---

## ⚠️ **錯誤處理**

### **常見錯誤類型**

#### **1. 重複檢測錯誤**
```typescript
// 錯誤示例
Error: 智能重複檢測: "新工作流程" 與 "現有工作流程" 相似度為 85.3% (置信度: 88.0%)

// 處理方法
try {
  const workflow = await workflow_create({
    name: "科幻小說創作",
    type: "novel"
  });
} catch (error) {
  if (error.message.includes("智能重複檢測")) {
    // 提取相似度信息
    const similarityMatch = error.message.match(/相似度為 ([\d.]+)%/);
    const similarity = similarityMatch ? parseFloat(similarityMatch[1]) : 0;

    if (similarity > 90) {
      console.log("高度相似，建議使用現有工作流程");
    } else {
      console.log("中等相似，建議修改名稱");
      // 自動添加時間戳
      const newName = `科幻小說創作_${new Date().toISOString().split('T')[0]}`;
      return await workflow_create({ name: newName, type: "novel" });
    }
  }
}
```

#### **2. 工作流程不存在錯誤**
```typescript
// 錯誤處理包裝器
async function safeWorkflowOperation<T>(
  workflowId: string,
  operation: (id: string) => Promise<T>
): Promise<T | null> {
  try {
    return await operation(workflowId);
  } catch (error) {
    if (error.message.includes("not found") || error.message.includes("不存在")) {
      console.log(`工作流程 ${workflowId} 不存在`);
      return null;
    }
    throw error; // 重新拋出其他錯誤
  }
}

// 使用示例
const status = await safeWorkflowOperation(workflowId, (id) =>
  workflow_status({ workflowId: id })
);

if (status) {
  console.log("工作流程狀態:", status.workflow.status);
} else {
  console.log("工作流程不存在，需要重新創建");
}
```

#### **3. 階段驗證失敗**
```typescript
async function handleStageValidation(workflowId: string) {
  try {
    const validation = await stage_validate({ workflowId, detailed: true });

    if (!validation.isValid) {
      console.log("階段驗證失敗，缺少以下要求：");

      // 處理缺少的節點類型
      if (validation.missingNodeTypes) {
        validation.missingNodeTypes.forEach(nodeType => {
          console.log(`  - 需要添加 ${nodeType} 類型的節點`);
        });
      }

      // 處理缺少的字段
      if (validation.missingFields) {
        validation.missingFields.forEach(field => {
          console.log(`  - 需要完善 ${field} 字段`);
        });
      }

      return false;
    }

    return true;
  } catch (error) {
    console.error("驗證過程出錯:", error.message);
    return false;
  }
}
```

### **錯誤恢復策略**

#### **自動重試機制**
```typescript
async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }

      console.log(`嘗試 ${attempt} 失敗，${delay}ms 後重試...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 2; // 指數退避
    }
  }

  throw new Error("重試次數已用盡");
}

// 使用示例
const workflow = await retryOperation(() =>
  workflow_create({
    name: "重要項目",
    type: "novel"
  })
);
```

---

## ⚡ **性能優化**

### **1. 批量操作優化**
```typescript
// ❌ 低效的逐個操作
async function inefficientBatch(workflowIds: string[]) {
  const results = [];
  for (const id of workflowIds) {
    const status = await workflow_status({ workflowId: id });
    results.push(status);
  }
  return results;
}

// ✅ 高效的並行操作
async function efficientBatch(workflowIds: string[]) {
  const promises = workflowIds.map(id =>
    workflow_status({ workflowId: id }).catch(error => ({
      error: true,
      workflowId: id,
      message: error.message
    }))
  );

  return await Promise.all(promises);
}
```

### **2. 緩存策略**
```typescript
class WorkflowCache {
  private cache = new Map<string, any>();
  private ttl = 30000; // 30秒TTL

  async getWorkflowStatus(workflowId: string) {
    const cacheKey = `status_${workflowId}`;
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.data;
    }

    const status = await workflow_status({ workflowId });
    this.cache.set(cacheKey, {
      data: status,
      timestamp: Date.now()
    });

    return status;
  }

  invalidate(workflowId: string) {
    this.cache.delete(`status_${workflowId}`);
  }
}

const cache = new WorkflowCache();
```

### **3. 分頁處理**
```typescript
async function getAllWorkflows() {
  const allWorkflows = [];
  let offset = 0;
  const limit = 50;

  while (true) {
    const batch = await workflow_list({
      limit,
      offset,
      sortBy: "updated_at"
    });

    allWorkflows.push(...batch.workflows);

    if (batch.workflows.length < limit) {
      break; // 已獲取所有數據
    }

    offset += limit;
  }

  return allWorkflows;
}
```

### **4. 內存管理**
```typescript
class WorkflowManager {
  private activeOperations = new Set<string>();

  async safeOperation<T>(
    operationId: string,
    operation: () => Promise<T>
  ): Promise<T> {
    if (this.activeOperations.has(operationId)) {
      throw new Error(`操作 ${operationId} 已在進行中`);
    }

    this.activeOperations.add(operationId);

    try {
      return await operation();
    } finally {
      this.activeOperations.delete(operationId);
    }
  }

  getActiveOperationsCount(): number {
    return this.activeOperations.size;
  }
}
```

---

## 🔌 **集成指導**

### **1. Claude Desktop 集成**

#### **基本配置**
```json
{
  "mcpServers": {
    "memorymesh": {
      "command": "node",
      "args": ["/path/to/memorymesh/dist/index.js"],
      "env": {
        "MEMORYMESH_VERSION": "0.3.0",
        "WORKFLOW_ENABLED": "true",
        "CACHE_ENABLED": "true",
        "LOG_LEVEL": "info"
      }
    }
  }
}
```

#### **自定義指令示例**
```
你是一個專業的創作助手，擁有完整的MemoryMesh v0.3.0工作流程管理能力。

當用戶提到創作項目時：
1. 首先使用 workflow_templates 查看可用模板
2. 根據用戶需求選擇合適的模板類型
3. 使用 workflow_create 創建工作流程
4. 定期使用 workflow_status 檢查進度
5. 在適當時機使用 workflow_advance 推進階段

你可以使用以下12個工具：
- workflow_create, workflow_list, workflow_status, workflow_templates
- workflow_advance, workflow_pause, workflow_resume
- stage_validate, stage_complete
- workflow_delete, workflow_export, workflow_import

始終提供友好的中文回應，並解釋每個操作的目的。
```

### **2. 其他AI助手集成**

#### **OpenAI GPT集成**
```typescript
// 使用OpenAI Function Calling
const functions = [
  {
    name: "workflow_create",
    description: "創建新的工作流程",
    parameters: {
      type: "object",
      properties: {
        name: { type: "string", description: "工作流程名稱" },
        type: {
          type: "string",
          enum: ["novel", "article", "script", "academic", "technical", "creative"],
          description: "工作流程類型"
        },
        metadata: { type: "object", description: "額外元數據" }
      },
      required: ["name", "type"]
    }
  }
  // ... 其他工具定義
];
```

#### **自定義MCP客戶端**
```typescript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

class MemoryMeshClient {
  private client: Client;

  async connect() {
    const transport = new StdioClientTransport({
      command: 'node',
      args: ['/path/to/memorymesh/dist/index.js']
    });

    this.client = new Client({
      name: "memorymesh-client",
      version: "1.0.0"
    }, {
      capabilities: {}
    });

    await this.client.connect(transport);
  }

  async createWorkflow(name: string, type: string, metadata?: any) {
    return await this.client.callTool({
      name: "workflow_create",
      arguments: { name, type, metadata }
    });
  }
}
```

### **3. Web應用集成**

#### **REST API包裝器**
```typescript
// Express.js 包裝器示例
import express from 'express';
import { MemoryMeshClient } from './memorymesh-client';

const app = express();
const client = new MemoryMeshClient();

app.post('/api/workflows', async (req, res) => {
  try {
    const { name, type, metadata } = req.body;
    const result = await client.createWorkflow(name, type, metadata);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/workflows', async (req, res) => {
  try {
    const { status, type, limit } = req.query;
    const result = await client.listWorkflows({ status, type, limit });
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

### **4. 監控和日誌**

#### **操作日誌**
```typescript
class WorkflowLogger {
  private logs: Array<{
    timestamp: string;
    operation: string;
    workflowId?: string;
    success: boolean;
    duration: number;
    error?: string;
  }> = [];

  async logOperation<T>(
    operation: string,
    workflowId: string | undefined,
    fn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();

    try {
      const result = await fn();

      this.logs.push({
        timestamp: new Date().toISOString(),
        operation,
        workflowId,
        success: true,
        duration: Date.now() - startTime
      });

      return result;
    } catch (error) {
      this.logs.push({
        timestamp: new Date().toISOString(),
        operation,
        workflowId,
        success: false,
        duration: Date.now() - startTime,
        error: error.message
      });

      throw error;
    }
  }

  getStats() {
    const total = this.logs.length;
    const successful = this.logs.filter(log => log.success).length;
    const avgDuration = this.logs.reduce((sum, log) => sum + log.duration, 0) / total;

    return {
      total,
      successful,
      successRate: (successful / total) * 100,
      avgDuration: Math.round(avgDuration)
    };
  }
}
```

---

## 🎯 **總結**

MemoryMesh v0.3.0 提供了完整的工作流程管理生態系統，通過12個專業MCP工具支援從創意構思到最終完成的整個創作過程。

### **關鍵優勢**
- ✅ **6種專業模板** - 覆蓋主要創作領域
- ✅ **智能階段管理** - 自動驗證和推進
- ✅ **強大的錯誤處理** - 友好的中文提示
- ✅ **高性能設計** - 支援大型項目
- ✅ **靈活集成** - 支援多種AI助手

### **最佳實踐要點**
1. **合理命名** - 使用描述性的工作流程名稱
2. **豐富元數據** - 提供詳細的項目信息
3. **錯誤處理** - 實施完整的錯誤恢復機制
4. **性能優化** - 使用批量操作和緩存
5. **監控日誌** - 追蹤操作和性能指標

通過遵循本指南的建議和示例，AI助手可以充分利用MemoryMesh的強大功能，為用戶提供專業級的創作工作流程管理體驗。

---

**📚 相關資源:**
- [README.md](README.md) - 項目概述和安裝指南
- [WORKFLOW_USAGE_EXAMPLES.md](WORKFLOW_USAGE_EXAMPLES.md) - 詳細使用示例
- [GitHub Issues](https://github.com/CheMiguel23/MemoryMesh/issues) - 問題報告和功能請求
```