# 🤖 MemoryMesh AI Agent 使用指南 v0.3.0

**專為AI助手設計的完整MCP工具調用指南** - 涵蓋所有12個工作流程管理工具的詳細使用方法、最佳實踐和故障排除

[![Version](https://img.shields.io/badge/version-0.3.0-blue.svg)](README.md)
[![MCP](https://img.shields.io/badge/MCP-Compatible-purple.svg)](https://modelcontextprotocol.io/)
[![AI](https://img.shields.io/badge/AI-Optimized-green.svg)](https://github.com/CheMiguel23/MemoryMesh)

---

## 📚 **目錄**

1. [快速開始](#-快速開始)
2. [核心工具詳解](#-核心工具詳解)
3. [完整使用場景](#-完整使用場景)
4. [最佳實踐](#-最佳實踐)
5. [錯誤處理](#-錯誤處理)
6. [性能優化](#-性能優化)
7. [集成指導](#-集成指導)

---

## 🚀 **快速開始**

### **基本概念**
MemoryMesh v0.3.0 提供 **12個MCP工具** 用於完整的工作流程管理：

#### **工具分類**
- **基礎管理** (4個): `workflow_create`, `workflow_list`, `workflow_status`, `workflow_templates`
- **流程控制** (3個): `workflow_advance`, `workflow_pause`, `workflow_resume`
- **階段管理** (2個): `stage_validate`, `stage_complete`
- **高級功能** (3個): `workflow_delete`, `workflow_export`, `workflow_import`

#### **第一個工作流程**
```typescript
// 1. 查看可用模板
const templates = await workflow_templates();

// 2. 創建工作流程
const workflow = await workflow_create({
  name: "我的第一個小說",
  type: "novel",
  metadata: {
    genre: "奇幻",
    targetAudience: "青少年"
  }
});

// 3. 檢查狀態
const status = await workflow_status({
  workflowId: workflow.workflowId,
  includeStages: true
});

// 4. 推進階段
const advance = await workflow_advance({
  workflowId: workflow.workflowId
});
```

---

## 🛠️ **核心工具詳解**

### **1. workflow_create - 創建工作流程**

#### **功能描述**
創建新的工作流程，支援6種專業模板。

#### **參數說明**
```typescript
interface WorkflowCreateParams {
  name: string;                    // 工作流程名稱 (必填)
  type: WorkflowType;             // 模板類型 (必填)
  templateId?: string;            // 自定義模板ID (可選)
  customStages?: string[];        // 自定義階段 (type=custom時)
  metadata?: Record<string, any>; // 額外元數據 (可選)
  extensions?: Record<string, any>; // 擴展參數 (可選)
}

type WorkflowType = 'novel' | 'article' | 'script' | 'academic' | 'technical' | 'creative' | 'custom';
```

#### **使用示例**
```typescript
// 小說創作工作流程
const novelWorkflow = await workflow_create({
  name: "龍族傳說_第一部",
  type: "novel",
  metadata: {
    genre: "奇幻",
    targetAudience: "青少年",
    estimatedLength: "80000字",
    language: "繁體中文"
  }
});

// 學術論文工作流程
const academicWorkflow = await workflow_create({
  name: "AI在教育中的應用研究",
  type: "academic",
  metadata: {
    field: "教育技術",
    researchType: "實證研究",
    targetJournal: "教育科技期刊",
    deadline: "2024-12-31"
  }
});

// 技術文檔工作流程
const techDocWorkflow = await workflow_create({
  name: "REST API開發者指南",
  type: "technical",
  metadata: {
    docType: "API文檔",
    targetAudience: "開發者",
    complexity: "中級",
    platform: "Web"
  }
});
```

#### **返回值**
```typescript
interface WorkflowCreateResponse {
  workflowId: string;      // 工作流程唯一ID
  template: string;        // 使用的模板ID
  nodes: number;          // 創建的節點數量
  edges: number;          // 創建的邊數量
  message: string;        // 成功消息
}
```

#### **錯誤處理**
```typescript
try {
  const workflow = await workflow_create({
    name: "測試工作流程",
    type: "novel"
  });
} catch (error) {
  if (error.message.includes("Smart duplicate detection")) {
    // 處理重複檢測錯誤
    console.log("檢測到相似工作流程，建議使用不同名稱");
  } else if (error.message.includes("Invalid template")) {
    // 處理模板錯誤
    console.log("模板類型無效，請檢查支援的類型");
  }
}
```

---

### **2. workflow_status - 查看工作流程狀態**

#### **功能描述**
獲取工作流程的詳細狀態信息，包括當前階段、進度和完成情況。

#### **參數說明**
```typescript
interface WorkflowStatusParams {
  workflowId: string;           // 工作流程ID (必填)
  includeStages?: boolean;      // 是否包含階段詳情 (默認: true)
  includeValidation?: boolean;  // 是否包含驗證結果 (默認: false)
  extensions?: Record<string, any>; // 擴展參數 (可選)
}
```

#### **使用示例**
```typescript
// 基本狀態查詢
const basicStatus = await workflow_status({
  workflowId: "wf_1234567890_abcdef"
});

// 詳細狀態查詢 (包含階段和驗證)
const detailedStatus = await workflow_status({
  workflowId: "wf_1234567890_abcdef",
  includeStages: true,
  includeValidation: true
});

// 檢查特定工作流程的進度
const checkProgress = async (workflowId: string) => {
  const status = await workflow_status({ workflowId });

  console.log(`工作流程: ${status.workflow.name}`);
  console.log(`當前階段: ${status.workflow.currentStage + 1}/${status.workflow.totalStages}`);
  console.log(`完成度: ${status.workflow.progress}%`);
  console.log(`狀態: ${status.workflow.status}`);

  return status;
};
```

#### **返回值結構**
```typescript
interface WorkflowStatusResponse {
  workflow: {
    workflowId: string;
    name: string;
    status: 'not_started' | 'in_progress' | 'completed' | 'paused';
    currentStage: number;
    totalStages: number;
    progress: number;        // 0-100
    metadata: Record<string, any>;
    createdAt: string;
    updatedAt: string;
  };
  stages?: Array<{
    stageId: string;
    workflowId: string;
    name: string;
    order: number;
    status: 'pending' | 'active' | 'completed';
    requiredNodeTypes: string[];
    completionCriteria: {
      minNodes: number;
      requiredFields: string[];
    };
  }>;
  summary: {
    currentStage: number;
    totalStages: number;
    progress: number;
    status: string;
  };
}
```

---

### **3. workflow_advance - 推進工作流程**

#### **功能描述**
將工作流程推進到下一個階段，支援強制推進和驗證跳過。

#### **參數說明**
```typescript
interface WorkflowAdvanceParams {
  workflowId: string;           // 工作流程ID (必填)
  force?: boolean;              // 是否強制推進 (默認: false)
  skipValidation?: boolean;     // 是否跳過驗證 (默認: false)
  extensions?: Record<string, any>; // 擴展參數 (可選)
}
```

#### **使用示例**
```typescript
// 正常推進 (會檢查完成條件)
const normalAdvance = await workflow_advance({
  workflowId: "wf_1234567890_abcdef"
});

// 強制推進 (忽略完成條件)
const forceAdvance = await workflow_advance({
  workflowId: "wf_1234567890_abcdef",
  force: true
});

// 跳過驗證推進
const skipValidationAdvance = await workflow_advance({
  workflowId: "wf_1234567890_abcdef",
  skipValidation: true
});

// 智能推進函數
const smartAdvance = async (workflowId: string) => {
  try {
    // 先嘗試正常推進
    return await workflow_advance({ workflowId });
  } catch (error) {
    if (error.message.includes("completion criteria")) {
      console.log("階段未完成，是否要強制推進？");
      // 根據用戶選擇決定是否強制推進
      return await workflow_advance({ workflowId, force: true });
    }
    throw error;
  }
};
```

#### **返回值**
```typescript
interface WorkflowAdvanceResponse {
  workflow: {
    workflowId: string;
    name: string;
    status: string;
    currentStage: number;
    totalStages: number;
    progress: number;
    // ... 其他工作流程信息
  };
  nextStage?: {
    stageId: string;
    name: string;
    order: number;
    status: string;
    requiredNodeTypes: string[];
    completionCriteria: object;
  };
  message: string;
}
```

---

### **4. workflow_list - 列出工作流程**

#### **功能描述**
列出所有工作流程，支援按狀態、類型等條件過濾和排序。

#### **參數說明**
```typescript
interface WorkflowListParams {
  status?: 'not_started' | 'in_progress' | 'completed' | 'paused'; // 狀態過濾
  type?: WorkflowType;          // 類型過濾
  limit?: number;               // 返回數量限制 (默認: 20, 最大: 100)
  offset?: number;              // 分頁偏移量 (默認: 0)
  sortBy?: 'created_at' | 'updated_at' | 'progress' | 'name'; // 排序字段
  sortOrder?: 'asc' | 'desc';   // 排序順序 (默認: desc)
  extensions?: Record<string, any>; // 擴展參數
}
```

#### **使用示例**
```typescript
// 列出所有工作流程
const allWorkflows = await workflow_list({});

// 列出進行中的小說工作流程
const novelInProgress = await workflow_list({
  status: "in_progress",
  type: "novel",
  sortBy: "updated_at"
});

// 分頁查詢
const pagedWorkflows = await workflow_list({
  limit: 10,
  offset: 20,
  sortBy: "progress",
  sortOrder: "desc"
});

// 查找特定類型的工作流程
const findWorkflowsByType = async (type: WorkflowType) => {
  const result = await workflow_list({ type });
  return result.workflows.map(w => ({
    id: w.workflowId,
    name: w.name,
    progress: w.progress,
    status: w.status
  }));
};
```

---

### **5. stage_validate - 驗證階段完成條件**

#### **功能描述**
檢查指定階段的完成條件，確認所需節點和要求是否滿足。

#### **參數說明**
```typescript
interface StageValidateParams {
  workflowId: string;           // 工作流程ID (必填)
  stageId?: string;             // 階段ID (可選，默認當前階段)
  detailed?: boolean;           // 是否返回詳細信息 (默認: true)
  extensions?: Record<string, any>; // 擴展參數
}
```

#### **使用示例**
```typescript
// 驗證當前階段
const currentStageValidation = await stage_validate({
  workflowId: "wf_1234567890_abcdef"
});

// 驗證特定階段
const specificStageValidation = await stage_validate({
  workflowId: "wf_1234567890_abcdef",
  stageId: "stage_planning",
  detailed: true
});

// 智能驗證函數
const smartValidate = async (workflowId: string) => {
  const validation = await stage_validate({ workflowId, detailed: true });

  if (validation.isValid) {
    console.log("✅ 階段完成條件已滿足，可以推進");
    return true;
  } else {
    console.log("❌ 階段未完成，缺少以下要求：");
    validation.missingRequirements?.forEach(req => {
      console.log(`  - ${req}`);
    });
    return false;
  }
};
```

---

### **6. workflow_pause / workflow_resume - 暫停和恢復**

#### **暫停工作流程**
```typescript
interface WorkflowPauseParams {
  workflowId: string;           // 工作流程ID (必填)
  reason?: string;              // 暫停原因 (可選)
  extensions?: Record<string, any>; // 擴展參數
}

// 使用示例
const pauseWorkflow = await workflow_pause({
  workflowId: "wf_1234567890_abcdef",
  reason: "需要更多研究時間"
});
```

#### **恢復工作流程**
```typescript
interface WorkflowResumeParams {
  workflowId: string;           // 工作流程ID (必填)
  notes?: string;               // 恢復備註 (可選)
  extensions?: Record<string, any>; // 擴展參數
}

// 使用示例
const resumeWorkflow = await workflow_resume({
  workflowId: "wf_1234567890_abcdef",
  notes: "研究完成，繼續創作"
});
```

---

### **7. workflow_templates - 獲取模板**

#### **功能描述**
獲取所有可用的工作流程模板，支援按類別過濾。

#### **參數說明**
```typescript
interface WorkflowTemplatesParams {
  category?: 'novel' | 'article' | 'script' | 'academic' | 'technical' | 'creative' | 'custom';
  includeCustom?: boolean;      // 是否包含自定義模板 (默認: true)
  extensions?: Record<string, any>; // 擴展參數
}
```

#### **使用示例**
```typescript
// 獲取所有模板
const allTemplates = await workflow_templates({});

// 獲取特定類別模板
const novelTemplates = await workflow_templates({
  category: "novel"
});

// 模板選擇助手
const selectTemplate = async (userPreference: string) => {
  const templates = await workflow_templates({});

  // 根據用戶偏好推薦模板
  const recommendations = templates.templates.filter(template =>
    template.description.toLowerCase().includes(userPreference.toLowerCase())
  );

  return recommendations.length > 0 ? recommendations[0] : templates.templates[0];
};
```

---

## 🎯 **完整使用場景**

### **場景1: 小說創作完整流程**

```typescript
async function createNovelWorkflow() {
  // 1. 創建小說工作流程
  const workflow = await workflow_create({
    name: "奇幻冒險_龍族傳說",
    type: "novel",
    metadata: {
      genre: "奇幻",
      targetAudience: "青少年",
      estimatedLength: "80000字",
      setting: "中世紀奇幻世界"
    }
  });

  console.log(`✅ 創建工作流程: ${workflow.workflowId}`);

  // 2. 添加初始角色 (規劃階段)
  await add_character({
    character: {
      name: "艾莉亞·星辰",
      role: "protagonist",
      status: "Active",
      currentLocation: ["龍騎士學院"],
      description: "年輕的龍騎士學徒，擁有罕見的星辰魔法",
      traits: ["勇敢", "好奇", "有責任感"],
      background: "來自偏遠村莊的農家女孩"
    }
  });

  // 3. 添加設定
  await add_setting({
    setting: {
      name: "龍騎士學院",
      type: "Building",
      description: "培養龍騎士的古老學院",
      status: "Active",
      significance: "Major",
      atmosphere: "神秘而莊嚴"
    }
  });

  // 4. 驗證規劃階段
  const validation = await stage_validate({
    workflowId: workflow.workflowId
  });

  if (validation.isValid) {
    // 5. 推進到大綱階段
    const advance = await workflow_advance({
      workflowId: workflow.workflowId
    });

    console.log(`✅ 推進到: ${advance.nextStage?.name}`);
  }

  return workflow.workflowId;
}
```

### **場景2: 學術論文研究流程**

```typescript
async function createAcademicWorkflow() {
  // 1. 創建學術論文工作流程
  const workflow = await workflow_create({
    name: "AI在個性化學習中的應用效果研究",
    type: "academic",
    metadata: {
      field: "教育技術",
      researchType: "實證研究",
      methodology: "混合方法",
      targetJournal: "教育科技期刊",
      deadline: "2024-12-31"
    }
  });

  // 2. 文獻回顧階段 - 添加文獻
  await add_nodes([{
    name: "Smith2023_PersonalizedLearning",
    nodeType: "literature",
    metadata: [
      "作者: Smith, J. et al.",
      "年份: 2023",
      "標題: Personalized Learning with AI: A Systematic Review",
      "期刊: Educational Technology Research",
      "關鍵發現: AI個性化學習可提升學習效果15-25%"
    ]
  }]);

  // 3. 檢查進度並推進
  const status = await workflow_status({
    workflowId: workflow.workflowId,
    includeStages: true
  });

  console.log(`當前階段: ${status.stages?.[status.workflow.currentStage]?.name}`);

  return workflow.workflowId;
}
```

### **場景3: 技術文檔創建流程**

```typescript
async function createTechnicalDocWorkflow() {
  // 1. 創建技術文檔工作流程
  const workflow = await workflow_create({
    name: "RESTful API 開發者指南 v2.0",
    type: "technical",
    metadata: {
      docType: "API文檔",
      targetAudience: "後端開發者",
      complexity: "中級",
      apiVersion: "2.0",
      platform: "Node.js"
    }
  });

  // 2. 需求分析階段 - 定義目標受眾
  await add_nodes([{
    name: "目標受眾_後端開發者",
    nodeType: "audience",
    metadata: [
      "經驗水平: 中級 (2-5年)",
      "技術背景: Node.js, Express.js",
      "主要需求: 快速上手API使用",
      "痛點: 缺乏實際代碼示例"
    ]
  }]);

  // 3. 定義文檔範圍
  await add_nodes([{
    name: "文檔範圍_API_v2",
    nodeType: "scope",
    metadata: [
      "包含: 所有v2.0端點",
      "認證: JWT Token",
      "格式: JSON",
      "示例: 完整代碼示例",
      "語言: JavaScript, Python, cURL"
    ]
  }]);

  return workflow.workflowId;
}
```

---

## 💡 **最佳實踐**

### **1. 工作流程命名規範**
```typescript
// ✅ 好的命名
"科幻小說_星際探索_第一部"
"學術論文_AI教育應用_2024"
"技術文檔_REST_API_v2.0"

// ❌ 避免的命名
"我的項目"
"新文檔"
"test123"
```

### **2. 元數據最佳實踐**
```typescript
// ✅ 豐富的元數據
const workflow = await workflow_create({
  name: "奇幻小說_龍族傳說",
  type: "novel",
  metadata: {
    // 基本信息
    genre: "奇幻",
    subGenre: "史詩奇幻",
    targetAudience: "青少年",

    // 技術規格
    estimatedLength: "80000字",
    language: "繁體中文",
    format: "電子書",

    // 項目管理
    deadline: "2024-12-31",
    priority: "高",
    collaborators: ["作者", "編輯"],

    // 創作設定
    setting: "中世紀奇幻世界",
    themes: ["成長", "友誼", "責任"],
    inspirations: ["托爾金", "羅琳"]
  }
});
```

### **3. 錯誤處理模式**
```typescript
async function robustWorkflowOperation(workflowId: string) {
  try {
    // 1. 先檢查工作流程是否存在
    const status = await workflow_status({ workflowId });

    // 2. 檢查是否可以推進
    const validation = await stage_validate({ workflowId });

    if (!validation.isValid) {
      console.log("階段未完成，需要添加更多內容");
      return { success: false, reason: "incomplete_stage" };
    }

    // 3. 推進工作流程
    const result = await workflow_advance({ workflowId });

    return { success: true, result };

  } catch (error) {
    // 4. 詳細錯誤處理
    if (error.message.includes("not found")) {
      return { success: false, reason: "workflow_not_found" };
    } else if (error.message.includes("duplicate")) {
      return { success: false, reason: "duplicate_detected" };
    } else {
      console.error("未知錯誤:", error);
      return { success: false, reason: "unknown_error", error };
    }
  }
}
```

### **4. 批量操作模式**
```typescript
async function batchWorkflowManagement() {
  // 1. 獲取所有進行中的工作流程
  const activeWorkflows = await workflow_list({
    status: "in_progress",
    limit: 50
  });

  // 2. 批量檢查和更新
  const results = await Promise.allSettled(
    activeWorkflows.workflows.map(async (workflow) => {
      const validation = await stage_validate({
        workflowId: workflow.workflowId
      });

      if (validation.isValid) {
        return await workflow_advance({
          workflowId: workflow.workflowId
        });
      }

      return { skipped: true, reason: "incomplete" };
    })
  );

  // 3. 處理結果
  results.forEach((result, index) => {
    const workflow = activeWorkflows.workflows[index];
    if (result.status === "fulfilled") {
      console.log(`✅ ${workflow.name}: 成功推進`);
    } else {
      console.log(`❌ ${workflow.name}: ${result.reason}`);
    }
  });
}
```

---

## ⚠️ **錯誤處理**

### **常見錯誤類型**

#### **1. 重複檢測錯誤**
```typescript
// 錯誤示例
Error: 智能重複檢測: "新工作流程" 與 "現有工作流程" 相似度為 85.3% (置信度: 88.0%)

// 處理方法
try {
  const workflow = await workflow_create({
    name: "科幻小說創作",
    type: "novel"
  });
} catch (error) {
  if (error.message.includes("智能重複檢測")) {
    // 提取相似度信息
    const similarityMatch = error.message.match(/相似度為 ([\d.]+)%/);
    const similarity = similarityMatch ? parseFloat(similarityMatch[1]) : 0;

    if (similarity > 90) {
      console.log("高度相似，建議使用現有工作流程");
    } else {
      console.log("中等相似，建議修改名稱");
      // 自動添加時間戳
      const newName = `科幻小說創作_${new Date().toISOString().split('T')[0]}`;
      return await workflow_create({ name: newName, type: "novel" });
    }
  }
}
```

#### **2. 工作流程不存在錯誤**
```typescript
// 錯誤處理包裝器
async function safeWorkflowOperation<T>(
  workflowId: string,
  operation: (id: string) => Promise<T>
): Promise<T | null> {
  try {
    return await operation(workflowId);
  } catch (error) {
    if (error.message.includes("not found") || error.message.includes("不存在")) {
      console.log(`工作流程 ${workflowId} 不存在`);
      return null;
    }
    throw error; // 重新拋出其他錯誤
  }
}

// 使用示例
const status = await safeWorkflowOperation(workflowId, (id) =>
  workflow_status({ workflowId: id })
);

if (status) {
  console.log("工作流程狀態:", status.workflow.status);
} else {
  console.log("工作流程不存在，需要重新創建");
}
```

#### **3. 階段驗證失敗**
```typescript
async function handleStageValidation(workflowId: string) {
  try {
    const validation = await stage_validate({ workflowId, detailed: true });

    if (!validation.isValid) {
      console.log("階段驗證失敗，缺少以下要求：");

      // 處理缺少的節點類型
      if (validation.missingNodeTypes) {
        validation.missingNodeTypes.forEach(nodeType => {
          console.log(`  - 需要添加 ${nodeType} 類型的節點`);
        });
      }

      // 處理缺少的字段
      if (validation.missingFields) {
        validation.missingFields.forEach(field => {
          console.log(`  - 需要完善 ${field} 字段`);
        });
      }

      return false;
    }

    return true;
  } catch (error) {
    console.error("驗證過程出錯:", error.message);
    return false;
  }
}
```

### **錯誤恢復策略**

#### **自動重試機制**
```typescript
async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }

      console.log(`嘗試 ${attempt} 失敗，${delay}ms 後重試...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 2; // 指數退避
    }
  }

  throw new Error("重試次數已用盡");
}

// 使用示例
const workflow = await retryOperation(() =>
  workflow_create({
    name: "重要項目",
    type: "novel"
  })
);
```

---

## ⚡ **性能優化**

### **1. 批量操作優化**
```typescript
// ❌ 低效的逐個操作
async function inefficientBatch(workflowIds: string[]) {
  const results = [];
  for (const id of workflowIds) {
    const status = await workflow_status({ workflowId: id });
    results.push(status);
  }
  return results;
}

// ✅ 高效的並行操作
async function efficientBatch(workflowIds: string[]) {
  const promises = workflowIds.map(id =>
    workflow_status({ workflowId: id }).catch(error => ({
      error: true,
      workflowId: id,
      message: error.message
    }))
  );

  return await Promise.all(promises);
}
```

### **2. 緩存策略**
```typescript
class WorkflowCache {
  private cache = new Map<string, any>();
  private ttl = 30000; // 30秒TTL

  async getWorkflowStatus(workflowId: string) {
    const cacheKey = `status_${workflowId}`;
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.data;
    }

    const status = await workflow_status({ workflowId });
    this.cache.set(cacheKey, {
      data: status,
      timestamp: Date.now()
    });

    return status;
  }

  invalidate(workflowId: string) {
    this.cache.delete(`status_${workflowId}`);
  }
}

const cache = new WorkflowCache();
```

### **3. 分頁處理**
```typescript
async function getAllWorkflows() {
  const allWorkflows = [];
  let offset = 0;
  const limit = 50;

  while (true) {
    const batch = await workflow_list({
      limit,
      offset,
      sortBy: "updated_at"
    });

    allWorkflows.push(...batch.workflows);

    if (batch.workflows.length < limit) {
      break; // 已獲取所有數據
    }

    offset += limit;
  }

  return allWorkflows;
}
```

### **4. 內存管理**
```typescript
class WorkflowManager {
  private activeOperations = new Set<string>();

  async safeOperation<T>(
    operationId: string,
    operation: () => Promise<T>
  ): Promise<T> {
    if (this.activeOperations.has(operationId)) {
      throw new Error(`操作 ${operationId} 已在進行中`);
    }

    this.activeOperations.add(operationId);

    try {
      return await operation();
    } finally {
      this.activeOperations.delete(operationId);
    }
  }

  getActiveOperationsCount(): number {
    return this.activeOperations.size;
  }
}
```

---

## 🔌 **集成指導**

### **1. Claude Desktop 集成**

#### **基本配置**
```json
{
  "mcpServers": {
    "memorymesh": {
      "command": "node",
      "args": ["/path/to/memorymesh/dist/index.js"],
      "env": {
        "MEMORYMESH_VERSION": "0.3.0",
        "WORKFLOW_ENABLED": "true",
        "CACHE_ENABLED": "true",
        "LOG_LEVEL": "info"
      }
    }
  }
}
```

#### **自定義指令示例**
```
你是一個專業的創作助手，擁有完整的MemoryMesh v0.3.0工作流程管理能力。

當用戶提到創作項目時：
1. 首先使用 workflow_templates 查看可用模板
2. 根據用戶需求選擇合適的模板類型
3. 使用 workflow_create 創建工作流程
4. 定期使用 workflow_status 檢查進度
5. 在適當時機使用 workflow_advance 推進階段

你可以使用以下12個工具：
- workflow_create, workflow_list, workflow_status, workflow_templates
- workflow_advance, workflow_pause, workflow_resume
- stage_validate, stage_complete
- workflow_delete, workflow_export, workflow_import

始終提供友好的中文回應，並解釋每個操作的目的。
```

### **2. 其他AI助手集成**

#### **OpenAI GPT集成**
```typescript
// 使用OpenAI Function Calling
const functions = [
  {
    name: "workflow_create",
    description: "創建新的工作流程",
    parameters: {
      type: "object",
      properties: {
        name: { type: "string", description: "工作流程名稱" },
        type: {
          type: "string",
          enum: ["novel", "article", "script", "academic", "technical", "creative"],
          description: "工作流程類型"
        },
        metadata: { type: "object", description: "額外元數據" }
      },
      required: ["name", "type"]
    }
  }
  // ... 其他工具定義
];
```

#### **自定義MCP客戶端**
```typescript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

class MemoryMeshClient {
  private client: Client;

  async connect() {
    const transport = new StdioClientTransport({
      command: 'node',
      args: ['/path/to/memorymesh/dist/index.js']
    });

    this.client = new Client({
      name: "memorymesh-client",
      version: "1.0.0"
    }, {
      capabilities: {}
    });

    await this.client.connect(transport);
  }

  async createWorkflow(name: string, type: string, metadata?: any) {
    return await this.client.callTool({
      name: "workflow_create",
      arguments: { name, type, metadata }
    });
  }
}
```

### **3. Web應用集成**

#### **REST API包裝器**
```typescript
// Express.js 包裝器示例
import express from 'express';
import { MemoryMeshClient } from './memorymesh-client';

const app = express();
const client = new MemoryMeshClient();

app.post('/api/workflows', async (req, res) => {
  try {
    const { name, type, metadata } = req.body;
    const result = await client.createWorkflow(name, type, metadata);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/workflows', async (req, res) => {
  try {
    const { status, type, limit } = req.query;
    const result = await client.listWorkflows({ status, type, limit });
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

### **4. 監控和日誌**

#### **操作日誌**
```typescript
class WorkflowLogger {
  private logs: Array<{
    timestamp: string;
    operation: string;
    workflowId?: string;
    success: boolean;
    duration: number;
    error?: string;
  }> = [];

  async logOperation<T>(
    operation: string,
    workflowId: string | undefined,
    fn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();

    try {
      const result = await fn();

      this.logs.push({
        timestamp: new Date().toISOString(),
        operation,
        workflowId,
        success: true,
        duration: Date.now() - startTime
      });

      return result;
    } catch (error) {
      this.logs.push({
        timestamp: new Date().toISOString(),
        operation,
        workflowId,
        success: false,
        duration: Date.now() - startTime,
        error: error.message
      });

      throw error;
    }
  }

  getStats() {
    const total = this.logs.length;
    const successful = this.logs.filter(log => log.success).length;
    const avgDuration = this.logs.reduce((sum, log) => sum + log.duration, 0) / total;

    return {
      total,
      successful,
      successRate: (successful / total) * 100,
      avgDuration: Math.round(avgDuration)
    };
  }
}
```

---

## 🎯 **總結**

MemoryMesh v0.3.0 提供了完整的工作流程管理生態系統，通過12個專業MCP工具支援從創意構思到最終完成的整個創作過程。

### **關鍵優勢**
- ✅ **6種專業模板** - 覆蓋主要創作領域
- ✅ **智能階段管理** - 自動驗證和推進
- ✅ **強大的錯誤處理** - 友好的中文提示
- ✅ **高性能設計** - 支援大型項目
- ✅ **靈活集成** - 支援多種AI助手

### **最佳實踐要點**
1. **合理命名** - 使用描述性的工作流程名稱
2. **豐富元數據** - 提供詳細的項目信息
3. **錯誤處理** - 實施完整的錯誤恢復機制
4. **性能優化** - 使用批量操作和緩存
5. **監控日誌** - 追蹤操作和性能指標

通過遵循本指南的建議和示例，AI助手可以充分利用MemoryMesh的強大功能，為用戶提供專業級的創作工作流程管理體驗。

---

**📚 相關資源:**
- [README.md](README.md) - 項目概述和安裝指南
- [WORKFLOW_USAGE_EXAMPLES.md](WORKFLOW_USAGE_EXAMPLES.md) - 詳細使用示例
- [GitHub Issues](https://github.com/CheMiguel23/MemoryMesh/issues) - 問題報告和功能請求
```