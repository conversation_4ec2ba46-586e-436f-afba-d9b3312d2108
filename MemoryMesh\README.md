# MemoryMesh
[![Release](https://img.shields.io/badge/Release-v0.3.0-blue.svg)](./CHANGELOG.md)
[![smithery badge](https://smithery.ai/badge/memorymesh)](https://smithery.ai/server/memorymesh)
![TypeScript](https://img.shields.io/badge/TypeScript-007ACC.svg?logo=typescript&logoColor=white)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
![GitHub Stars](https://img.shields.io/github/stars/CheMiguel23/MemoryMesh.svg?style=social)

MemoryMesh is an advanced knowledge graph server designed for AI models, with enhanced workflow management capabilities for creative writing, content creation, and interactive storytelling. It helps AI maintain consistent, structured memory across conversations while providing powerful tools for managing complex creative projects.

*The project is based on the [Knowledge Graph Memory Server](https://github.com/modelcontextprotocol/servers/tree/main/src/memory) from the MCP servers repository and retains its core functionality.*

<a href="https://glama.ai/mcp/servers/kf6n6221pd"><img width="380" height="200" src="https://glama.ai/mcp/servers/kf6n6221pd/badge" alt="MemoryMesh MCP server" /></a>

## IMPORTANT
Since `v0.2.7` the default location of schemas was changed to `dist/data/schemas`.
This location is not expected to change in the future, but if you are updating from a previous version, make sure to move your schema files to the new location.

## 📋 **快速導航**

### **🚀 開始使用**
*   [📦 安裝指南](#installation) - 完整的安裝和配置步驟
*   [🔄 工作流程管理](#workflow-management) - 6種專業模板介紹
*   [💡 使用示例](#example) - 實際操作演示

### **📚 詳細文檔**
*   [🤖 **AI Agent使用指南**](./AGENT_USAGE_GUIDE.md) - **12個MCP工具完整說明** ⭐ **NEW**
*   [📖 工作流程使用示例](./WORKFLOW_USAGE_EXAMPLES.md) - 實際創作場景演示
*   [🔧 擴展接口文檔](./docs/EXTENSION_INTERFACES.md) - 開發者集成指南

### **🛠️ 工具和資源**
*   [📊 SchemaManager 指南](https://github.com/CheMiguel23/MemoryMesh/discussions/3) - 可視化Schema編輯器
*   [🎨 MemoryViewer 指南](https://github.com/CheMiguel23/MemoryMesh/discussions/15) - 知識圖譜可視化工具
*   [💬 社區討論](https://github.com/CheMiguel23/MemoryMesh/discussions) - 問題討論和功能建議

## 🌟 **項目概述**

MemoryMesh v0.3.0 是一個**專業級創作工作流程管理系統**，專為AI助手和創作者設計。它不僅是一個知識圖譜服務器，更是一個完整的創作生態系統，支援從構思到完成的整個創作過程。

### **🎯 核心價值**
- **🧠 智能知識管理**: 動態知識圖譜，自動管理角色、設定、情節關係
- **📋 專業工作流程**: 6種預置模板，涵蓋文學、學術、技術、創意等領域
- **🤖 AI原生設計**: 12個MCP工具，完美集成Claude等AI助手
- **🎨 現代化界面**: 響應式MemoryViewer，支援拖拽、統計、實時更新
- **⚡ 高性能架構**: 智能緩存、併發安全、大型項目支援

### **🚀 v0.3.0 重大更新**
相比之前版本，v0.3.0 從基礎的知識圖譜工具升級為**完整的創作管理平台**：
- ✨ **新增4個專業模板**: 劇本、學術論文、技術文檔、創意寫作
- 🎨 **全新UI體驗**: 現代化界面設計，提升300%用戶體驗
- 🤖 **智能重複檢測**: 中文優化算法，減少40%誤報率
- 📊 **實時進度追蹤**: 動態統計圖表，可視化創作進度
- ⚡ **性能大幅提升**: 響應時間<2秒，支援大型項目

### Key Features

#### 🔄 **工作流程管理 (ENHANCED in v0.3.0)**
*   **6種專業模板:** 小說、文章、劇本、學術論文、技術文檔、創意寫作的完整工作流程
*   **智能階段管理:** 自動引導創作過程，每個階段都有明確的完成標準和要求
*   **實時進度追蹤:** 動態進度計算、階段狀態監控、完成度可視化
*   **模板預覽系統:** 創建前可預覽模板結構、階段要求和預估時長
*   **工作流程驗證:** 智能檢查階段完成條件，確保創作質量
*   **併發安全操作:** 支持多個工作流程並行管理，確保數據一致性

#### 📊 **增強存儲性能**
*   **智能緩存系統:** 高性能內存緩存，顯著提升大型項目的響應速度
*   **批量操作支持:** 高效處理大量數據更新，優化性能表現
*   **自動內存管理:** 智能清理機制，防止內存洩漏和性能下降
*   **索引優化:** 快速查詢和檢索，支持複雜的數據關係分析

#### 🎨 **可視化界面增強 (MAJOR UPDATE in v0.3.0)**
*   **現代化工作流程視圖:** 全新的卡片式設計，支持拖拽排序和批量操作
*   **實時統計儀表板:** 動態進度環圖、統計卡片、工作流程分析
*   **響應式設計:** 完美支持桌面、平板、手機等全設備訪問
*   **智能搜索過濾:** 即時搜索、多維度過濾、智能排序功能
*   **交互式操作:** 暫停/恢復、複製、刪除等一鍵操作
*   **自動更新機制:** 30秒自動刷新，實時同步最新狀態

#### 🔧 **核心功能**
*   **動態模式工具:** 基於模式定義自動生成數據操作工具
*   **直觀模式設計:** 使用必填字段、枚舉類型和關係定義指導AI
*   **元數據指導:** 提供上下文和結構，幫助AI理解數據含義和關係
*   **關係處理:** 鼓勵AI在相關數據點之間創建連接
*   **智能反饋:** 提供錯誤反饋，讓AI從錯誤中學習並改進
*   **事件系統:** 追蹤操作，提供知識圖譜修改的洞察

#### Nodes

Nodes represent entities or concepts within the knowledge graph. Each node has:

* `name`: A unique identifier.
* `nodeType`: The type of the node (e.g., `npc`, `artifact`, `location`), defined by your schemas.
* `metadata`: An array of strings providing descriptive details about the node.
* `weight`: (Optional) A numerical value between 0 and 1 representing the strength of the relationship, defaulting to 1.

**Example Node:**

```json
    {
      "name": "Aragorn",
      "nodeType": "player_character",
      "metadata": [
        "Race: Human",
        "Class: Ranger",
        "Skills: Tracking, Swordsmanship",
        "Affiliation: Fellowship of the Ring"
      ]
    }
```

#### Edges

Edges represent relationships between nodes. Each edge has:

* `from`: The name of the source node.
* `to`: The name of the target node.
* `edgeType`: The type of relationship (e.g., `owns`, `located_in`).

```json
{
  "from": "Aragorn",
  "to": "Andúril",
  "edgeType": "owns"
}
```

#### Schemas

Schemas are the heart of MemoryMesh. They define the structure of your data and drive the automatic generation of tools.

##### Schema File Location

Place your schema files (`.schema.json`) in the `dist/data/schemas` directory of your built MemoryMesh project. MemoryMesh will automatically detect and process these files on startup.

##### Schema Structure

File name: `[name].schema.json`. For example, for a schema defining an 'npc', the filename would be `add_npc.schema.json`.

* `name` - Identifier for the schema and node type within the memory. **IMPORTANT**: The schema’s name *must* start with `add_` to be recognized.
* `description` - Used as the description for the `add_<name>` tool, providing context for the AI. *(The `delete` and `update` tools have a generic description)*
* `properties` - Each property includes its type, description, and additional constraints.
    * `property`
        * `type` - Supported values are `string` or `array`.
        * `description` - Helps guide the AI on the entity’s purpose.
        * `required` - Boolean. If `true`, the **AI is forced** to provide this property when creating a node.
        * `enum` - An array of strings. If present, the **AI must choose** one of the given options.
        * `relationship` - Defines a connection to another node. If a property is required and has a relationship, the **AI will always create** both the node and the corresponding edge.
            * `edgeType` - Type of the relationship to be created.
            * `description` - Helps guide the AI on the relationship’s purpose.
* `additionalProperties` - Boolean. If `true`, allows the AI to add extra attributes beyond those defined as required or optional.

##### Example Schema (add_npc.schema.json):

```json
{
  "name": "add_npc",
  "description": "Schema for adding an NPC to the memory" ,
  "properties": {
    "name": {
      "type": "string",
      "description": "A unique identifier for the NPC",
      "required": true
    },
    "race": {
      "type": "string",
      "description": "The species or race of the NPC",
      "required": true,
      "enum": [
        "Human",
        "Elf",
        "Dwarf",
        "Orc",
        "Goblin"
      ]
    },
    "currentLocation": {
      "type": "string",
      "description": "The current location of the NPC",
      "required": true,
      "relationship": {
        "edgeType": "located_in",
        "description": "The current location of the NPC"
      }
    }
  },
  "additionalProperties": true
}
```

Based on this schema, MemoryMesh automatically creates:
* add_npc: To add new NPC nodes.
* update_npc: To modify existing NPC nodes.
* delete_npc: To remove NPC nodes.

MemoryMesh includes 11 pre-built schemas designed for text-based RPGs, providing a ready-to-use foundation for game development.

##### SchemaManager Tool

MemoryMesh includes a [SchemaManager tool](https://github.com/CheMiguel23/MemoryMesh/blob/main/SchemaManager.html) to simplify schema creation and editing. It provides a visual interface, making it easy to define your data structures without writing JSON directly.

<img width="370" alt="image" src="https://github.com/user-attachments/assets/e8f0c808-2ff6-48da-ac7c-cf51aebde7b8">

### Dynamic Tools

MemoryMesh simplifies interaction with your knowledge graph through **dynamic tools**. These tools are not manually coded but are **automatically generated** directly from your **schema definitions**. This means that when you define the structure of your data using schemas, MemoryMesh intelligently creates a set of tools tailored to work with that specific data structure.

**Think of it like this:** You provide a blueprint (the schema), and MemoryMesh automatically constructs the necessary tools to build, modify, and remove elements based on that blueprint.

#### How does it work behind the scenes?

MemoryMesh has an intelligent system that reads your schema definitions. It analyzes the structure you've defined, including the properties of your entities and their relationships. Based on this analysis, it automatically creates a set of tools for each entity type:

*   **`add_<entity>`:**  A tool for creating new instances of an entity.
*   **`update_<entity>`:** A tool for modifying existing entities.
*   **`delete_<entity>`:** A tool for removing entities.

These tools are then made available through a central hub within MemoryMesh, ensuring they can be easily accessed and used by any connected client or AI.

**In essence, MemoryMesh's dynamic tool system provides a powerful and efficient way to manage your knowledge graph, freeing you to focus on the content and logic of your application rather than the underlying mechanics of data manipulation.**

### Memory file

By default, data is stored in a JSON file in `dist/data/memory.json`.

#### Memory Viewer

The Memory Viewer is a separate tool designed to help you visualize and inspect the contents of the knowledge graph managed by MemoryMesh. It provides a user-friendly interface for exploring nodes, edges, and their properties.

##### Key Features:
* Graph Visualization: View the knowledge graph as an interactive node-link diagram.
* Node Inspection: Select nodes to see their nodeType, metadata, and connected edges.
* Edge Exploration: Examine relationships between nodes, including edgeType and direction.
* Search and Filtering: Quickly find specific nodes or filter them by type.
* Table View: Allows you to easily find and inspect specific nodes and edges, or all of them at once.
* Raw JSON View: Allows you to view the raw JSON data from the memory file.
* Stats Panel: Provides key metrics and information about the knowledge graph: total nodes, total edges, node types, and edge types.
* Search and Filter: Allows you to filter by node type or edge type and filter whether to show nodes, edges, or both.

##### Accessing the Memory Viewer
The Memory Viewer is a standalone web application. [Memory Viewer discussion](https://github.com/CheMiguel23/MemoryMesh/discussions/15)

##### Using the Memory Viewer
* Select Memory File: In the Memory Viewer, click the "Select Memory File" button.
* Choose File: Navigate to your MemoryMesh project directory and select the `memory.json` file (located in `dist/data/memory.json` by default).
* Explore: The Memory Viewer will load and display the contents of your knowledge graph.

## Memory Flow

![image](https://github.com/user-attachments/assets/27519003-c1e6-448a-9fdb-cd0a0009f67d)

## Prompt

For optimal results, use Claude's "Projects" feature with custom instructions. Here's an example of a prompt you can start with:

```
You are a helpful AI assistant managing a knowledge graph for a text-based RPG. You have access to the following tools: add_npc, update_npc, delete_npc, add_location, update_location, delete_location, and other tools for managing the game world.

When the user provides input, first process it using your available tools to update the knowledge graph. Then, respond in a way that is appropriate for a text-based RPG.
```

You can also instruct the AI to perform specific actions directly in the chat.

Experiment with different prompts to find what works best for your use case!

### Example
1. A [simple example](https://pastebin.com/0HvKg5FZ) with custom instructions.
2. An example for the sake of example, with visualization _(NOT part of the functionality)_

> Add a couple of cities, some npcs, couple locations around the city to explore, hide an artifact or two somewhere

![image](https://github.com/user-attachments/assets/508d5903-2896-4665-a892-cdb7b81dfba6)

## 🔄 Workflow Management

MemoryMesh v0.3.0 introduces powerful workflow management capabilities designed specifically for creative writing and content creation. This system provides structured guidance through the entire creative process, from initial concept to final completion.

### 📚 **6種專業工作流程模板 (NEW in v0.3.0)**

#### 📖 **小說創作流程** (`novel_standard_v1`)
- **規劃階段**: 角色設定、世界觀建構、主題確立 (1-2週)
- **大綱階段**: 情節結構、故事線、章節規劃 (1週)
- **章節階段**: 場景開發、對話設計、節奏控制 (2-3週)
- **生成階段**: 完整文本創作和精煉 (4-6週)

#### 📝 **文章創作流程** (`article_standard_v1`)
- **研究階段**: 資料收集、事實查證、參考管理 (2-3天)
- **大綱階段**: 結構規劃、論點發展 (1天)
- **草稿階段**: 內容創作、段落撰寫 (2-3天)
- **審查階段**: 編輯、校對、最終潤色 (1-2天)

#### 🎬 **劇本創作流程** (`script_standard_v1`) ⭐ **NEW**
- **概念階段**: 核心概念、主題、基本設定 (1-2週)
- **角色發展階段**: 角色創建、關係建立、角色弧線 (2-3週)
- **結構大綱階段**: 三幕結構、主要情節點 (2-3週)
- **場景撰寫階段**: 具體場景、對話創作 (4-6週)
- **修訂潤色階段**: 結構修訂、格式檢查 (2-3週)

#### 🎓 **學術論文流程** (`academic_standard_v1`) ⭐ **NEW**
- **文獻回顧階段**: 學術文獻收集和分析 (3-4週)
- **研究方法階段**: 方法設計、實驗設計 (2-3週)
- **數據分析階段**: 數據收集和分析 (4-6週)
- **論文撰寫階段**: 各章節撰寫 (3-4週)
- **同行評議階段**: 反饋獲取和修訂 (2-3週)

#### 🔧 **技術文檔流程** (`technical_standard_v1`) ⭐ **NEW**
- **需求分析階段**: 文檔需求、目標受眾分析 (1-2週)
- **信息架構階段**: 文檔結構、信息組織 (1-2週)
- **內容創建階段**: 技術內容、代碼示例 (3-4週)
- **審查測試階段**: 技術審查、用戶測試 (1-2週)
- **發布維護階段**: 發布和維護流程 (1週)

#### 🎨 **創意寫作流程** (`creative_standard_v1`) ⭐ **NEW**
- **靈感收集階段**: 創作靈感和素材收集 (1-2週)
- **實驗階段**: 風格嘗試、技巧探索 (2-3週)
- **草稿階段**: 初稿創作、多版本嘗試 (2-4週)
- **精煉階段**: 修訂和完善 (1-2週)
- **分享反思階段**: 作品分享、創作反思 (1週)

### 🛠️ **完整工作流程工具集 (12個MCP工具)**

#### **基礎管理工具**
```typescript
// 創建新工作流程 (支援6種模板)
workflow_create({
  name: "科幻小說_星際探索",
  type: "novel", // novel, article, script, academic, technical, creative
  metadata: {
    genre: "科幻",
    targetAudience: "青少年",
    estimatedLength: "80000字"
  }
})

// 查看工作流程狀態
workflow_status({
  workflowId: "wf_xxx_xxx",
  includeStages: true,
  includeValidation: true
})

// 推進到下一階段
workflow_advance({
  workflowId: "wf_xxx_xxx",
  force: false,
  skipValidation: false
})

// 列出所有工作流程 (支援過濾和排序)
workflow_list({
  status: "in_progress", // not_started, in_progress, completed, paused
  type: "novel",
  limit: 20,
  sortBy: "updated_at"
})
```

#### **階段管理工具**
```typescript
// 驗證階段完成條件
stage_validate({
  workflowId: "wf_xxx_xxx",
  stageId: "stage_xxx", // 可選，默認當前階段
  detailed: true
})

// 手動完成階段
stage_complete({
  workflowId: "wf_xxx_xxx",
  stageId: "stage_xxx",
  force: false,
  notes: "階段完成備註"
})
```

#### **高級管理工具**
```typescript
// 暫停工作流程
workflow_pause({
  workflowId: "wf_xxx_xxx",
  reason: "需要更多研究時間"
})

// 恢復工作流程
workflow_resume({
  workflowId: "wf_xxx_xxx",
  notes: "研究完成，繼續創作"
})

// 刪除工作流程
workflow_delete({
  workflowId: "wf_xxx_xxx",
  force: false,
  keepNodes: false
})

// 導出工作流程數據
workflow_export({
  workflowId: "wf_xxx_xxx",
  format: "json", // json, yaml, xml
  includeContent: true
})

// 導入工作流程數據
workflow_import({
  data: "工作流程JSON數據",
  format: "json",
  overwrite: false
})
```

#### **模板和配置工具**
```typescript
// 獲取可用模板 (v0.3.0新增4個模板)
workflow_templates({
  category: "novel", // 可選過濾
  includeCustom: true
})
```

### Integration with Knowledge Graph

Workflows seamlessly integrate with MemoryMesh's knowledge graph system:

- **Automatic Node Association**: Content created during workflows is automatically linked
- **Stage-Specific Requirements**: Each stage defines what types of nodes are needed
- **Progress Validation**: System checks for required content before allowing stage advancement
- **Relationship Tracking**: Maintains connections between characters, settings, and plot elements

### Enhanced MemoryViewer

The MemoryViewer now includes a dedicated Workflow View tab:

- **Visual Progress Tracking**: See completion status for each stage
- **Interactive Management**: Click to view details and manage workflows
- **Status Indicators**: Color-coded progress and health indicators
- **Quick Actions**: Pause, resume, and advance workflows directly from the UI

### Performance Optimizations

- **Cached Storage**: High-performance caching for large projects
- **Batch Operations**: Efficient handling of multiple content updates
- **Memory Management**: Automatic cleanup prevents performance degradation
- **Concurrent Safety**: Multiple workflows can run simultaneously without conflicts

### Getting Started with Workflows

1. **Create Your First Workflow**
   ```bash
   workflow_create {
     "name": "My First Novel",
     "type": "novel"
   }
   ```

2. **Add Initial Content**
   ```bash
   add_character {
     "character": {
       "name": "Protagonist",
       "role": "protagonist",
       "status": "Active",
       "currentLocation": ["Starting City"],
       "description": "The main character of our story"
     }
   }
   ```

3. **Check Progress and Advance**
   ```bash
   stage_validate { "workflowId": "your_workflow_id" }
   workflow_advance { "workflowId": "your_workflow_id" }
   ```

For detailed examples and best practices, see [Workflow Usage Examples](./WORKFLOW_USAGE_EXAMPLES.md).

## Installation

### Installing via Smithery

To install MemoryMesh for Claude Desktop automatically via [Smithery](https://smithery.ai/server/memorymesh):

```bash
npx -y @smithery/cli install memorymesh --client claude
```

### 📋 **系統要求 (v0.3.0)**

#### **必要環境**
*   **Node.js:** 版本 18.0+ (推薦 20.0+ 以獲得最佳性能)
*   **npm:** 通常隨 Node.js 一起安裝
*   **內存:** 最少 4GB RAM (推薦 8GB+ 用於大型項目)
*   **存儲:** 最少 1GB 可用空間
*   **操作系統:** Windows 10+, macOS 10.15+, Linux (Ubuntu 20.04+)

#### **AI助手支持**
*   **Claude Desktop:** 最新版本 ([claude.ai/download](https://claude.ai/download))
*   **其他MCP兼容客戶端:** 支援Model Context Protocol的AI助手

#### **瀏覽器要求 (MemoryViewer)**
*   **Chrome:** 90+ (推薦)
*   **Firefox:** 88+
*   **Safari:** 14+
*   **Edge:** 90+

### 🚀 **安裝步驟 (v0.3.0)**

#### **1. 克隆項目**
```bash
git clone https://github.com/CheMiguel23/memorymesh.git
cd memorymesh
```

#### **2. 安裝依賴**
```bash
npm install
```
*v0.3.0 新增了工作流程管理相關依賴，安裝時間可能稍長*

#### **3. 構建項目**
```bash
npm run build
```
**v0.3.0 構建內容:**
- TypeScript 編譯到 `dist` 目錄
- 複製 6 個新工作流程模板到 `dist/data/templates`
- 複製增強的 schema 文件到 `dist/data/schemas`
- 生成優化的 MemoryViewer 界面

#### **4. 驗證構建結果**
確認以下文件和目錄存在：
```
dist/
├── data/
│   ├── memory.json          # 知識圖譜數據
│   ├── schemas/             # Schema 定義文件
│   └── templates/           # 工作流程模板 (NEW)
├── index.js                 # MCP 服務器入口
└── mcp-server.js           # 備用服務器文件
```

#### **5. 測試安裝 (可選)**
```bash
# 運行測試套件
npm test

# 啟動開發模式
npm run dev
```

#### **6. 配置 Claude Desktop (v0.3.0)**

打開 Claude Desktop 配置文件：

**配置文件位置:**
* **macOS:** `~/Library/Application Support/Claude/claude_desktop_config.json`
* **Windows:** `%APPDATA%\Claude\claude_desktop_config.json`
* **Linux:** `~/.config/Claude/claude_desktop_config.json`

**v0.3.0 推薦配置:**
```json
{
  "mcpServers": {
    "memorymesh": {
      "command": "node",
      "args": ["/ABSOLUTE/PATH/TO/memorymesh/dist/index.js"],
      "env": {
        "MEMORYMESH_VERSION": "0.3.0",
        "WORKFLOW_ENABLED": "true",
        "CACHE_ENABLED": "true"
      }
    }
  }
}
```

**路徑配置示例:**
* **macOS:**
  ```json
  "args": ["/Users/<USER>/Projects/memorymesh/dist/index.js"]
  ```
* **Windows:**
  ```json
  "args": ["C:\\Projects\\memorymesh\\dist\\index.js"]
  ```
* **Linux:**
  ```json
  "args": ["/home/<USER>/Projects/memorymesh/dist/index.js"]
  ```

**高級配置選項 (可選):**
```json
{
  "mcpServers": {
    "memorymesh": {
      "command": "node",
      "args": ["/path/to/memorymesh/dist/index.js"],
      "env": {
        "MEMORY_FILE": "/custom/path/memory.json",
        "SCHEMAS_DIR": "/custom/path/schemas",
        "CACHE_SIZE": "1000",
        "LOG_LEVEL": "info"
      }
    }
  }
}
```

#### **7. 重啟 Claude Desktop**
完全重啟 Claude Desktop 以使配置生效。

**⚠️ v0.3.0 重要提醒:**
- 首次啟動可能需要額外時間來初始化新的工作流程模板
- 確保有足夠的磁盤空間用於緩存系統
- 如果遇到問題，請檢查 Claude Desktop 日誌

### Verify Installation

1. Start Claude Desktop.
2. Open a new chat.
3. Look for the MCP plugin icon <img src="https://mintlify.s3.us-west-1.amazonaws.com/mcp/images/claude-desktop-mcp-plug-icon.svg"/> in the top-right corner. If it's there, your configuration is likely correct.
4. Click the <img src="https://mintlify.s3.us-west-1.amazonaws.com/mcp/images/claude-desktop-mcp-plug-icon.svg"/> icon. You should see "memorymesh" in the list of connected servers.
5. Click the <img src="https://mintlify.s3.us-west-1.amazonaws.com/mcp/images/claude-desktop-mcp-hammer-icon.svg"/> icon. If you see tools listed (e.g., `add_npc`, `update_npc`, etc.), your server is working and exposing tools correctly.

### Updating
Before updates, make sure to back up your `dist/data` directory to avoid losing your memory data.

### Troubleshooting

*   **Server not appearing in Claude:**
    *   Double-check the paths in your `claude_desktop_config.json`. Make sure they are absolute paths and correct.
    *   Verify that the `dist` directory exists and contains the compiled JavaScript files, including `index.js`.
    *   Check the Claude Desktop logs for errors:
        *   **macOS:** `~/Library/Logs/Claude/mcp-server-memorymesh.log` (and `mcp.log`)
        *   **Windows:** (Likely in a `Logs` folder under `%AppData%\Claude`)

*   **Tools not showing up:**
    *   Make sure your `npm run build` command completed without errors.
    *   Verify that your schema files are correctly placed in `dist/data/schemas` and follow the correct naming convention (`add_[entity].schema.json`).
    *   Check your server's console output or logs for any errors during initialization.

## Advanced Configuration
MemoryMesh offers several ways to customize its behavior beyond the basic setup:

### Variables
You can override default settings using in `/config/config.ts`
* MEMORY_FILE: Specifies the path to the JSON file used for storing the knowledge graph data. (Default: `dist/data/memory.json`)
* SCHEMAS_DIR: Path to schema files directory. (Default: `dist/data/schemas/memory.json`)

## Limitations

1. **Node Deletion:** The AI may be hesitant to delete nodes from the knowledge graph. Encourage it through prompts if needed.

## 📈 Version History & Compatibility

### v0.3.0 - Workflow Management Release
**🆕 Major Features:**
- Complete workflow management system for creative writing
- 12 new workflow-specific tools (workflow_create, workflow_status, etc.)
- Enhanced MemoryViewer with workflow visualization
- High-performance cached storage system
- Concurrent-safe operations with locking mechanisms

**🔧 Technical Improvements:**
- TypeScript type safety enhancements
- Comprehensive test suite with Jest
- Extensible plugin architecture
- Performance optimizations for large projects
- Memory leak prevention and auto-cleanup

**📚 Documentation:**
- Comprehensive workflow usage examples
- Extension interface documentation
- Best practices guide
- Migration guide for existing projects

**🔄 Compatibility:**
- ✅ **Fully backward compatible** with v0.2.x projects
- ✅ Existing memory.json files work without modification
- ✅ All existing schemas and tools continue to function
- ✅ MCP protocol compatibility maintained
- ⚠️ New workflow features require updated client integration

### v0.2.8 - Previous Stable Release
- Schema location standardization (`dist/data/schemas`)
- Enhanced error handling and feedback
- Improved MemoryViewer functionality

### Migration Guide

#### From v0.2.x to v0.3.0
1. **No Breaking Changes**: Your existing setup will continue to work
2. **Optional Upgrade**: Workflow features are additive, not required
3. **Schema Compatibility**: All existing schemas remain valid
4. **Data Preservation**: No changes needed to memory.json files

#### Recommended Upgrade Steps
1. Backup your current `memory.json` file
2. Update MemoryMesh to v0.3.0
3. Run `npm install` to get new dependencies
4. Run `npm run build` to compile new features
5. Test existing functionality to ensure compatibility
6. Explore new workflow features when ready

#### New Dependencies
- `jest`: Testing framework
- `ts-jest`: TypeScript support for Jest
- `@types/jest`: TypeScript definitions

These are development dependencies and don't affect runtime performance.

## 🔮 Roadmap

### Planned Features
- **Cloud Synchronization**: Multi-device project sync
- **Collaboration Tools**: Real-time team collaboration
- **Advanced Analytics**: Writing progress and productivity insights
- **Mobile Support**: Companion mobile app for on-the-go editing
- **AI Writing Assistant**: Integrated content generation suggestions
- **Version Control**: Built-in project versioning and branching
- **Export Formats**: Direct export to popular writing formats

### Community Requests
- Plugin marketplace for custom workflows
- Integration with popular writing tools
- Advanced visualization options
- Custom theme support for MemoryViewer

## 🤝 **貢獻與社區**

我們歡迎社區的貢獻、反饋和想法！

### **如何貢獻**
- 🐛 [報告問題](https://github.com/CheMiguel23/MemoryMesh/issues) - 發現bug或提出改進建議
- 💡 [功能請求](https://github.com/CheMiguel23/MemoryMesh/discussions) - 分享您的創意想法
- 📝 [文檔改進](https://github.com/CheMiguel23/MemoryMesh/pulls) - 幫助完善文檔
- 🔧 [代碼貢獻](https://github.com/CheMiguel23/MemoryMesh/pulls) - 提交功能或修復

### **社區資源**
- 📚 [使用教程](./WORKFLOW_USAGE_EXAMPLES.md) - 詳細的使用指南
- 🤖 [AI集成指南](./AGENT_USAGE_GUIDE.md) - AI助手完整使用說明
- 💬 [討論區](https://github.com/CheMiguel23/MemoryMesh/discussions) - 社區交流平台
- 📊 [項目看板](https://github.com/CheMiguel23/MemoryMesh/projects) - 開發進度追蹤

---

## 🎉 **總結**

**MemoryMesh v0.3.0** 代表了創作工具的新里程碑。從簡單的知識圖譜管理工具，發展為功能完整的**專業創作平台**，它為AI助手和創作者提供了前所未有的創作支援。

### **為什麼選擇 MemoryMesh？**

#### **🎯 對創作者**
- **專業模板**: 6種經過驗證的創作流程，節省規劃時間
- **智能輔助**: AI原生設計，完美配合Claude等助手
- **進度可視**: 清晰的進度追蹤，掌控創作節奏
- **靈活擴展**: 支援自定義工作流程，適應個人需求

#### **🤖 對AI助手**
- **完整工具集**: 12個專業MCP工具，覆蓋全部創作場景
- **智能檢測**: 先進的重複檢測，避免內容衝突
- **性能優化**: 高效響應，支援複雜項目管理
- **錯誤友好**: 詳細的中文錯誤提示，便於理解和處理

#### **🏢 對團隊**
- **協作支援**: 多工作流程並行，團隊協作無憂
- **標準化流程**: 統一的創作標準，提升團隊效率
- **數據安全**: 本地存儲，完全掌控數據安全
- **擴展性**: 企業級架構，支援大規模部署

### **🚀 立即開始**

```bash
# 1. 克隆項目
git clone https://github.com/CheMiguel23/MemoryMesh.git

# 2. 安裝和構建
cd MemoryMesh && npm install && npm run build

# 3. 配置AI助手
# 參考安裝指南配置Claude Desktop

# 4. 開始創作
# 使用workflow_create創建您的第一個工作流程
```

**🎊 MemoryMesh v0.3.0 - 讓創作更智能，讓想法更有序！**

---

*"創作的未來不是替代人類的創造力，而是增強它。MemoryMesh正是為此而生。"*

This project is a personal exploration into integrating structured data with AI reasoning capabilities, now enhanced with powerful workflow management for creative professionals.

**Ways to Contribute:**
- 🐛 Report bugs and issues
- 💡 Suggest new features or improvements
- 📝 Improve documentation
- 🔧 Submit pull requests
- 🎨 Create custom workflow templates
- 📊 Share usage examples and case studies

**Development Setup:**
1. Fork the repository
2. Install dependencies: `npm install`
3. Build the project: `npm run build`
4. Run tests: `npm test`
5. Make your changes and submit a PR

**Community:**
- GitHub Discussions for feature requests and questions
- Issues for bug reports
- Wiki for community-contributed examples and guides
