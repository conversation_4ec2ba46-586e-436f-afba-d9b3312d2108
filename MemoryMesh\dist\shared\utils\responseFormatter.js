// src/utils/responseFormatter.ts
/**
 * Formats successful tool responses in a consistent, AI-friendly way.
 */
export function formatToolResponse({ data, message, actionTaken, suggestions = [] }) {
    // Create content array for display
    const content = [];
    if (message) {
        content.push({ type: "text", text: message });
    }
    if (actionTaken) {
        content.push({ type: "text", text: `Action taken: ${actionTaken}` });
    }
    if (data !== undefined) {
        content.push({ type: "text", text: `Data: ${JSON.stringify(data, null, 2)}` });
    }
    if (suggestions.length > 0) {
        content.push({ type: "text", text: `Suggestions: ${suggestions.join(', ')}` });
    }
    // Return simplified format compatible with Cursor
    return {
        content: content.length > 0 ? content : [{ type: "text", text: "Operation completed successfully" }],
        isError: false
    };
}
/**
 * Formats error responses in a consistent, AI-friendly way.
 */
export function formatToolError({ operation, error, context, suggestions = [], recoverySteps = [] }) {
    const content = [
        { type: "text", text: `Error during ${operation}: ${error}` }
    ];
    if (context) {
        content.push({ type: "text", text: `Context: ${JSON.stringify(context, null, 2)}` });
    }
    if (suggestions.length > 0) {
        content.push({ type: "text", text: `Suggestions: ${suggestions.join(', ')}` });
    }
    if (recoverySteps.length > 0) {
        content.push({ type: "text", text: `Recovery steps: ${recoverySteps.join(', ')}` });
    }
    // Return simplified format compatible with Cursor
    return {
        content,
        isError: true
    };
}
/**
 * Creates an informative message for partial success scenarios.
 */
export function formatPartialSuccess({ operation, attempted, succeeded, failed, details }) {
    const content = [
        {
            type: "text",
            text: `Partial success for ${operation}: ${succeeded.length} succeeded, ${failed.length} failed`
        },
        {
            type: "text",
            text: `Details: ${JSON.stringify(details, null, 2)}`
        }
    ];
    // Add succeeded items info
    if (succeeded.length > 0) {
        content.push({
            type: "text",
            text: `Succeeded items: ${JSON.stringify(succeeded, null, 2)}`
        });
    }
    // Add failed items info
    if (failed.length > 0) {
        const failedInfo = failed.map(item => ({
            item,
            reason: details[String(item)] || 'Unknown error'
        }));
        content.push({
            type: "text",
            text: `Failed items: ${JSON.stringify(failedInfo, null, 2)}`
        });
    }
    content.push({
        type: "text",
        text: "Suggestions: Review failed items and their reasons, Consider retrying failed operations individually, Verify requirements for failed items"
    });
    return {
        content,
        isError: true
    };
}
//# sourceMappingURL=responseFormatter.js.map