{"version": 3, "file": "SchemaProcessor.js", "sourceRoot": "", "sources": ["../../../src/core/schema/SchemaProcessor.ts"], "names": [], "mappings": "AAAA,uCAAuC;AAEvC,OAAO,EAAC,kBAAkB,EAAE,eAAe,EAAe,MAAM,kBAAkB,CAAC;AAuBnF;;GAEG;AACH,SAAS,mBAAmB,CAAC,KAAa,EAAE,KAAkC;IAC1E,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACvB,OAAO,GAAG,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC3C,CAAC;IACD,OAAO,GAAG,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;AACxC,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CAClC,IAAc,EACd,MAAoB,EACpB,QAAgB;IAEhB,IAAI,CAAC;QACD,MAAM,EAAC,cAAc,EAAE,aAAa,EAAC,GAAG,MAAM,CAAC;QAC/C,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,KAAK,GAAW,EAAE,CAAC;QACzB,MAAM,KAAK,GAAW,EAAE,CAAC;QAEzB,6BAA6B;QAC7B,MAAM,cAAc,GAAG,IAAI,GAAG,CAAS;YACnC,MAAM;YACN,GAAG,cAAc,CAAC,cAAc;YAChC,GAAG,cAAc,CAAC,cAAc;YAChC,GAAG,CAAC,cAAc,CAAC,aAAa,IAAI,EAAE,CAAC;SAC1C,CAAC,CAAC;QAEH,IAAI,aAAa,EAAE,CAAC;YAChB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3E,CAAC;QAED,0BAA0B;QAC1B,KAAK,MAAM,KAAK,IAAI,cAAc,CAAC,cAAc,EAAE,CAAC;YAChD,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,mBAAmB,KAAK,cAAc,CAAC,CAAC;YAC5D,CAAC;YACD,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC1C,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3D,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,KAAK,MAAM,KAAK,IAAI,cAAc,CAAC,cAAc,EAAE,CAAC;YAChD,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACzE,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3D,CAAC;QACL,CAAC;QAED,wBAAwB;QACxB,IAAI,aAAa,EAAE,CAAC;YAChB,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC1D,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;wBACvB,KAAK,MAAM,MAAM,IAAI,KAAK,EAAE,CAAC;4BACzB,KAAK,CAAC,IAAI,CAAC;gCACP,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE,IAAI,CAAC,IAAI;gCACf,EAAE,EAAE,MAAM;gCACV,QAAQ,EAAE,MAAM,CAAC,QAAQ;6BAC5B,CAAC,CAAC;wBACP,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,KAAK,CAAC,IAAI,CAAC;4BACP,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,IAAI;4BACf,EAAE,EAAE,KAAe;4BACnB,QAAQ,EAAE,MAAM,CAAC,QAAQ;yBAC5B,CAAC,CAAC;oBACP,CAAC;oBACD,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;gBACrD,CAAC;YACL,CAAC;QACL,CAAC;QAED,4BAA4B;QAC5B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBAClD,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;YACnD,CAAC;QACL,CAAC;QAED,uBAAuB;QACvB,MAAM,IAAI,GAAS;YACf,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ;YACR,QAAQ;SACX,CAAC;QACF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjB,OAAO,EAAC,KAAK,EAAE,KAAK,EAAC,CAAC;IAC1B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,MAAM,KAAK,CAAC;IAChB,CAAC;AACL,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,gBAAgB,CAClC,OAAiB,EACjB,WAAiB,EACjB,MAAoB,EACpB,YAAmB;IAEnB,MAAM,EAAC,cAAc,EAAE,aAAa,EAAC,GAAG,MAAM,CAAC;IAC/C,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAkB,CAAC;IAC3C,MAAM,WAAW,GAAG;QAChB,MAAM,EAAE,EAAY;QACpB,GAAG,EAAE,EAAY;KACpB,CAAC;IAEF,4CAA4C;IAC5C,MAAM,YAAY,GAAG,IAAI,GAAG,CAAS;QACjC,GAAG,cAAc,CAAC,cAAc;QAChC,GAAG,cAAc,CAAC,cAAc;QAChC,GAAG,CAAC,cAAc,CAAC,aAAa,IAAI,EAAE,CAAC;QACvC,MAAM;QACN,UAAU;KACb,CAAC,CAAC;IAEH,2CAA2C;IAC3C,IAAI,aAAa,EAAE,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;IACzE,CAAC;IAED,yCAAyC;IACzC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAC/D,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACpD,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,CAAC,GAAW,EAAE,KAAc,EAAE,EAAE;QACxD,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/E,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,cAAc,CAAC,CAAC;IACpD,CAAC,CAAC;IAEF,mCAAmC;IACnC,MAAM,eAAe,GAAG,CAAC,GAAG,cAAc,CAAC,cAAc,EAAE,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;IAC7F,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;QAClC,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC5E,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC;IAED,oDAAoD;IACpD,IAAI,aAAa,EAAE,CAAC;QAChB,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YAC1D,kDAAkD;YAClD,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC;gBAC/B,mEAAmE;gBACnE,MAAM,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACnD,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI;oBAC9B,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CACpC,CAAC;gBAEF,wEAAwE;gBACxE,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;gBAE1C,gBAAgB;gBAChB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC7B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBACvB,KAAK,CAAC,OAAO,CAAC,CAAC,MAAc,EAAE,EAAE;wBAC7B,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC;4BACjB,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,WAAW,CAAC,IAAI;4BACtB,EAAE,EAAE,MAAM;4BACV,QAAQ,EAAE,MAAM,CAAC,QAAQ;yBAC5B,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC;gBACP,CAAC;qBAAM,IAAI,KAAK,EAAE,CAAC;oBACf,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC;wBACjB,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,WAAW,CAAC,IAAI;wBACtB,EAAE,EAAE,KAAe;wBACnB,QAAQ,EAAE,MAAM,CAAC,QAAQ;qBAC5B,CAAC,CAAC;gBACP,CAAC;gBAED,mBAAmB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;IACL,CAAC;IAED,kDAAkD;IAClD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QACjD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YAChD,mBAAmB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACpC,CAAC;IACL,CAAC;IAED,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAC9D,MAAM,cAAc,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAClE,OAAO,GAAG,cAAc,KAAK,KAAK,EAAE,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,OAAO;QACH,QAAQ,EAAE,eAAe;QACzB,WAAW;KACd,CAAC;AACN,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACpC,OAAiB,EACjB,MAAoB,EACpB,QAAgB,EAChB,kBAAsC;IAEtC,IAAI,CAAC;QACD,+CAA+C;QAC/C,MAAM,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;QAE5C,iCAAiC;QACjC,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,SAAS,EAAE,CAAC;QACvD,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAO,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;QAEnG,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,MAAM,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACpC,OAAO,eAAe,CAAC;gBACnB,SAAS,EAAE,cAAc;gBACzB,KAAK,EAAE,GAAG,QAAQ,KAAK,OAAO,CAAC,IAAI,aAAa;gBAChD,OAAO,EAAE,EAAC,OAAO,EAAE,QAAQ,EAAC;gBAC5B,WAAW,EAAE,CAAC,wBAAwB,EAAE,yBAAyB,CAAC;aACrE,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC;YACD,kBAAkB;YAClB,MAAM,EAAC,QAAQ,EAAE,WAAW,EAAC,GAAG,MAAM,gBAAgB,CAClD,OAAO,EACP,IAAI,EACJ,MAAM,EACN,SAAS,CACZ,CAAC;YAEF,wBAAwB;YACxB,MAAM,WAAW,GAAS;gBACtB,GAAG,IAAI;gBACP,QAAQ;aACX,CAAC;YACF,MAAM,kBAAkB,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YAEpD,6CAA6C;YAC7C,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,MAAM,kBAAkB,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACvD,CAAC;YAED,kDAAkD;YAClD,MAAM,kBAAkB,CAAC,MAAM,EAAE,CAAC;YAElC,OAAO,kBAAkB,CAAC;gBACtB,IAAI,EAAE;oBACF,WAAW;oBACX,WAAW;iBACd;gBACD,WAAW,EAAE,WAAW,QAAQ,KAAK,WAAW,CAAC,IAAI,EAAE;aAC1D,CAAC,CAAC;QAEP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,0CAA0C;YAC1C,MAAM,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACpC,MAAM,KAAK,CAAC;QAChB,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,IAAI,kBAAkB,CAAC,eAAe,EAAE,EAAE,CAAC;YACvC,MAAM,kBAAkB,CAAC,QAAQ,EAAE,CAAC;QACxC,CAAC;QAED,OAAO,eAAe,CAAC;YACnB,SAAS,EAAE,cAAc;YACzB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;YACxE,OAAO,EAAE,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAC;YACpC,WAAW,EAAE;gBACT,wCAAwC;gBACxC,mCAAmC;aACtC;YACD,aAAa,EAAE;gBACX,4BAA4B;gBAC5B,oCAAoC;aACvC;SACJ,CAAC,CAAC;IACP,CAAC;AACL,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACpC,QAAgB,EAChB,QAAgB,EAChB,kBAAsC;IAEtC,IAAI,CAAC;QACD,MAAM,KAAK,GAAG,MAAM,kBAAkB,CAAC,SAAS,EAAE,CAAC;QACnD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAO,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QAE3F,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO,eAAe,CAAC;gBACnB,SAAS,EAAE,cAAc;gBACzB,KAAK,EAAE,GAAG,QAAQ,KAAK,QAAQ,aAAa;gBAC5C,OAAO,EAAE,EAAC,QAAQ,EAAE,QAAQ,EAAC;gBAC7B,WAAW,EAAE,CAAC,2BAA2B,CAAC;aAC7C,CAAC,CAAC;QACP,CAAC;QAED,MAAM,kBAAkB,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEjD,OAAO,kBAAkB,CAAC;YACtB,WAAW,EAAE,WAAW,QAAQ,KAAK,QAAQ,EAAE;SAClD,CAAC,CAAC;IACP,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,eAAe,CAAC;YACnB,SAAS,EAAE,cAAc;YACzB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;YACxE,OAAO,EAAE,EAAC,QAAQ,EAAE,QAAQ,EAAC;YAC7B,WAAW,EAAE;gBACT,mBAAmB;gBACnB,2BAA2B;aAC9B;YACD,aAAa,EAAE;gBACX,iCAAiC;gBACjC,2BAA2B;aAC9B;SACJ,CAAC,CAAC;IACP,CAAC;AACL,CAAC"}