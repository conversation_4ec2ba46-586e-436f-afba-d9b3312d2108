// src/core/graph/SmartEntityNormalizer.ts
/**
 * 智能實體標準化器
 * 使用多種算法動態處理實體去重，不依賴固定對照表
 */
export class SmartEntityNormalizer {
    /**
     * 使用Unicode標準化進行文本正規化
     */
    static unicodeNormalize(text) {
        if (!text)
            return '';
        // Unicode正規化：將不同編碼的相同字符統一
        let normalized = text.normalize('NFKC'); // 兼容性分解後重組
        // 移除變音符號和特殊標記
        normalized = normalized.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
        return normalized.toLowerCase().trim();
    }
    /**
     * 語音相似度檢測（針對中文）
     * 基於聲母韻母的相似性
     */
    static phoneticSimilarity(text1, text2) {
        // 簡化的拼音相似度（可以集成真正的拼音庫）
        const pinyin1 = this.toPinyinApprox(text1);
        const pinyin2 = this.toPinyinApprox(text2);
        return this.levenshteinSimilarity(pinyin1, pinyin2);
    }
    /**
     * 簡化拼音近似（實際應用中可以使用 pinyin 庫）
     */
    static toPinyinApprox(text) {
        // 這裡是簡化版本，實際可以集成 node-pinyin 庫
        const commonMappings = {
            '愛': 'ai', '艾': 'ai', '哀': 'ai',
            '麗': 'li', '丽': 'li', '理': 'li', '李': 'li',
            '絲': 'si', '丝': 'si', '思': 'si', '司': 'si',
            '聖': 'sheng', '圣': 'sheng', '生': 'sheng',
            '輝': 'hui', '辉': 'hui', '慧': 'hui', '會': 'hui',
            '夏': 'xia', '俠': 'xia', '侠': 'xia',
            '爾': 'er', '尔': 'er', '兒': 'er', '儿': 'er'
        };
        let result = '';
        for (const char of text) {
            result += commonMappings[char] || char;
        }
        return result;
    }
    /**
     * 結構相似度檢測
     * 檢測姓名結構、分隔符等模式
     */
    static structuralSimilarity(text1, text2) {
        // 提取結構特徵
        const struct1 = this.extractStructure(text1);
        const struct2 = this.extractStructure(text2);
        // 比較結構相似度
        let matches = 0;
        const totalFeatures = Math.max(struct1.length, struct2.length);
        for (let i = 0; i < Math.min(struct1.length, struct2.length); i++) {
            if (struct1[i] === struct2[i])
                matches++;
        }
        return totalFeatures > 0 ? matches / totalFeatures : 0;
    }
    /**
     * 提取文本結構特徵
     */
    static extractStructure(text) {
        const features = [];
        // 長度特徵
        features.push(`len:${text.length}`);
        // 分隔符特徵
        if (text.includes('·'))
            features.push('dot-sep');
        if (text.includes('·'))
            features.push('mid-dot');
        if (text.includes(' '))
            features.push('space-sep');
        if (text.includes('-'))
            features.push('dash-sep');
        // 字符類型特徵
        const hasTraditional = /[\u4e00-\u9fff]/.test(text) && this.hasTraditionalChars(text);
        if (hasTraditional)
            features.push('traditional');
        const hasLatin = /[a-zA-Z]/.test(text);
        if (hasLatin)
            features.push('latin');
        const hasNumbers = /\d/.test(text);
        if (hasNumbers)
            features.push('numbers');
        return features;
    }
    /**
     * 檢查是否包含繁體字（使用統計方法）
     */
    static hasTraditionalChars(text) {
        // 常見繁體字特徵（比固定對照表更靈活）
        const traditionalIndicators = [
            /[\u4e00-\u4eff].*[\u9fa6-\u9fff]/, // 高位Unicode區間
            /[麗絲聖輝華強偉軍傑龍鳳豪廣東來對會時國發現見質價資訊題類種樣級層]/,
            /.*[\u2e80-\u2eff].*/, // CJK部首補充
        ];
        return traditionalIndicators.some(pattern => pattern.test(text));
    }
    /**
     * 語義距離檢測（可以集成詞向量）
     */
    static semanticSimilarity(text1, text2) {
        // 簡化的語義相似度（實際可以集成 word2vec 或其他NLP模型）
        const words1 = this.extractSemanticTokens(text1);
        const words2 = this.extractSemanticTokens(text2);
        let commonTokens = 0;
        const totalTokens = new Set([...words1, ...words2]).size;
        for (const token of words1) {
            if (words2.includes(token))
                commonTokens++;
        }
        return totalTokens > 0 ? (commonTokens * 2) / (words1.length + words2.length) : 0;
    }
    /**
     * 提取語義標記
     */
    static extractSemanticTokens(text) {
        // 簡化的分詞（實際可以使用 jieba 或其他中文分詞庫）
        const tokens = [];
        // 提取有意義的字符組合
        for (let i = 0; i < text.length - 1; i++) {
            const bigram = text.substr(i, 2);
            tokens.push(bigram);
        }
        return tokens;
    }
    /**
     * 綜合相似度計算
     * 結合多種算法的加權平均
     */
    static calculateComprehensiveSimilarity(text1, text2) {
        const unicode = this.levenshteinSimilarity(this.unicodeNormalize(text1), this.unicodeNormalize(text2));
        const phonetic = this.phoneticSimilarity(text1, text2);
        const structural = this.structuralSimilarity(text1, text2);
        const semantic = this.semanticSimilarity(text1, text2);
        // 動態權重計算（根據內容類型調整）
        const weights = this.calculateDynamicWeights(text1, text2);
        // 基礎權重（針對中文優化）
        const baseWeights = {
            unicode: 0.25, // Unicode正規化
            phonetic: 0.45, // 語音相似度重要（中文特色）
            structural: 0.25, // 結構特徵
            semantic: 0.05 // 輕微語義權重
        };
        // 合併基礎權重和動態權重
        const finalWeights = {
            unicode: baseWeights.unicode * weights.unicode,
            phonetic: baseWeights.phonetic * weights.phonetic,
            structural: baseWeights.structural * weights.structural,
            semantic: baseWeights.semantic * weights.semantic
        };
        const similarity = unicode * finalWeights.unicode +
            phonetic * finalWeights.phonetic +
            structural * finalWeights.structural +
            semantic * finalWeights.semantic;
        return {
            similarity,
            breakdown: { unicode, phonetic, structural, semantic },
            weights: finalWeights,
            confidence: this.calculateConfidence(unicode, phonetic, structural, semantic)
        };
    }
    /**
     * 編輯距離相似度
     */
    static levenshteinSimilarity(s1, s2) {
        const distance = this.levenshteinDistance(s1, s2);
        const maxLength = Math.max(s1.length, s2.length);
        return maxLength > 0 ? 1 - (distance / maxLength) : 1;
    }
    /**
     * 計算編輯距離
     */
    static levenshteinDistance(s1, s2) {
        if (s1.length === 0)
            return s2.length;
        if (s2.length === 0)
            return s1.length;
        const matrix = [];
        for (let i = 0; i <= s2.length; i++) {
            matrix[i] = [i];
        }
        for (let j = 0; j <= s1.length; j++) {
            matrix[0][j] = j;
        }
        for (let i = 1; i <= s2.length; i++) {
            for (let j = 1; j <= s1.length; j++) {
                if (s2.charAt(i - 1) === s1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                }
                else {
                    matrix[i][j] = Math.min(matrix[i - 1][j - 1] + 1, // substitution
                    matrix[i][j - 1] + 1, // insertion
                    matrix[i - 1][j] + 1 // deletion
                    );
                }
            }
        }
        return matrix[s2.length][s1.length];
    }
    /**
     * 動態權重計算
     * 根據文本特徵調整權重
     */
    static calculateDynamicWeights(text1, text2) {
        const len1 = text1.length;
        const len2 = text2.length;
        const avgLen = (len1 + len2) / 2;
        // 長度差異影響權重
        const lengthDiff = Math.abs(len1 - len2) / Math.max(len1, len2);
        // 中文字符比例
        const chineseRatio1 = (text1.match(/[\u4e00-\u9fff]/g) || []).length / len1;
        const chineseRatio2 = (text2.match(/[\u4e00-\u9fff]/g) || []).length / len2;
        const avgChineseRatio = (chineseRatio1 + chineseRatio2) / 2;
        // 數字和特殊字符比例
        const specialRatio1 = (text1.match(/[0-9\W]/g) || []).length / len1;
        const specialRatio2 = (text2.match(/[0-9\W]/g) || []).length / len2;
        const avgSpecialRatio = (specialRatio1 + specialRatio2) / 2;
        return {
            unicode: 1.0 + (lengthDiff > 0.3 ? 0.2 : 0), // 長度差異大時增加Unicode權重
            phonetic: 1.0 + (avgChineseRatio * 0.3), // 中文多時增加語音權重
            structural: 1.0 + (avgLen > 10 ? 0.2 : 0), // 長文本增加結構權重
            semantic: 1.0 + (avgSpecialRatio < 0.2 ? 0.3 : 0) // 純文本增加語義權重
        };
    }
    /**
     * 計算檢測置信度
     */
    static calculateConfidence(unicode, phonetic, structural, semantic) {
        // 各算法結果的一致性
        const scores = [unicode, phonetic, structural, semantic];
        const mean = scores.reduce((a, b) => a + b) / scores.length;
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
        const stdDev = Math.sqrt(variance);
        // 標準差越小，置信度越高
        return Math.max(0, 1 - stdDev);
    }
    /**
     * 智能重複檢測（主入口）- 改進版
     */
    static isSmartDuplicate(existingNames, newName, options = {}) {
        const { threshold = 0.75, strictMode = false, contextType = 'general' } = options;
        // 特殊處理：章節類型的檢測邏輯
        if (contextType === 'chapter' || this.isChapterName(newName)) {
            return this.checkChapterDuplicate(existingNames, newName, threshold);
        }
        // 根據上下文調整閾值
        const adjustedThreshold = this.adjustThresholdByContext(threshold, contextType, strictMode);
        let bestMatch = null;
        for (const existingName of existingNames) {
            const analysis = this.calculateComprehensiveSimilarity(existingName, newName);
            // 考慮置信度的檢測
            const effectiveSimilarity = analysis.similarity * (analysis.confidence || 1);
            if (effectiveSimilarity >= adjustedThreshold) {
                if (!bestMatch || effectiveSimilarity > bestMatch.effectiveSimilarity) {
                    bestMatch = {
                        matchedName: existingName,
                        similarity: analysis.similarity,
                        confidence: analysis.confidence,
                        effectiveSimilarity,
                        analysis: analysis.breakdown,
                        weights: analysis.weights
                    };
                }
            }
        }
        if (bestMatch) {
            return {
                isDuplicate: true,
                matchedName: bestMatch.matchedName,
                similarity: bestMatch.similarity,
                confidence: bestMatch.confidence,
                analysis: bestMatch.analysis,
                recommendation: this.generateRecommendation(bestMatch, newName)
            };
        }
        return { isDuplicate: false };
    }
    /**
     * 檢查是否為章節名稱
     */
    static isChapterName(name) {
        // 檢測章節名稱的常見模式
        const chapterPatterns = [
            /第.+卷\.第.+章/, // 第X卷.第X章
            /第.+章/, // 第X章
            /Chapter\s+\d+/i, // Chapter 1
            /Ch\.\s*\d+/i, // Ch. 1
            /\d+\.\d+/, // 1.1
            /場景組/, // 場景組
            /Scene\s+\d+/i // Scene 1
        ];
        return chapterPatterns.some(pattern => pattern.test(name));
    }
    /**
     * 章節專用的重複檢測邏輯
     */
    static checkChapterDuplicate(existingNames, newName, threshold) {
        // 對於章節，只檢查結構完全相同的情況
        // 提取章節編號和標題部分
        const newChapterInfo = this.parseChapterName(newName);
        for (const existingName of existingNames) {
            const existingChapterInfo = this.parseChapterName(existingName);
            // 如果卷號和章節號完全相同，才認為是重複
            if (newChapterInfo.volume === existingChapterInfo.volume &&
                newChapterInfo.chapter === existingChapterInfo.chapter) {
                return {
                    isDuplicate: true,
                    matchedName: existingName,
                    similarity: 1.0,
                    confidence: 1.0,
                    analysis: {
                        unicode: 1.0,
                        phonetic: 0,
                        structural: 1.0,
                        semantic: 0
                    },
                    recommendation: `章節編號重複：${newChapterInfo.volume ? `第${newChapterInfo.volume}卷` : ''}第${newChapterInfo.chapter}章已存在`
                };
            }
        }
        return { isDuplicate: false };
    }
    /**
     * 解析章節名稱，提取卷號、章節號和標題
     */
    static parseChapterName(name) {
        // 匹配 "第X卷.第X章：標題" 格式
        const volumeChapterMatch = name.match(/第(.+?)卷\.第(.+?)章[：:](.*)/);
        if (volumeChapterMatch) {
            return {
                volume: volumeChapterMatch[1],
                chapter: volumeChapterMatch[2],
                title: volumeChapterMatch[3]
            };
        }
        // 匹配 "第X章：標題" 格式
        const chapterMatch = name.match(/第(.+?)章[：:](.*)/);
        if (chapterMatch) {
            return {
                chapter: chapterMatch[1],
                title: chapterMatch[2]
            };
        }
        // 匹配 "Chapter X: Title" 格式
        const englishMatch = name.match(/Chapter\s+(\d+)[：:]?\s*(.*)/i);
        if (englishMatch) {
            return {
                chapter: englishMatch[1],
                title: englishMatch[2]
            };
        }
        return { title: name };
    }
    /**
     * 根據上下文調整閾值
     */
    static adjustThresholdByContext(baseThreshold, contextType, strictMode) {
        let adjustment = 0;
        switch (contextType) {
            case 'character':
                adjustment = strictMode ? 0.1 : -0.05; // 角色名稱可以更寬鬆
                break;
            case 'setting':
                adjustment = strictMode ? 0.05 : -0.1; // 設定名稱更寬鬆
                break;
            case 'chapter':
                // 章節使用專用邏輯，這裡不會被調用
                adjustment = 0;
                break;
            default:
                adjustment = strictMode ? 0.05 : 0;
        }
        return Math.max(0.5, Math.min(0.95, baseThreshold + adjustment));
    }
    /**
     * 生成改進建議
     */
    static generateRecommendation(match, newName) {
        const similarity = (match.similarity * 100).toFixed(1);
        const confidence = (match.confidence * 100).toFixed(1);
        if (match.similarity > 0.9) {
            return `高度相似 (${similarity}%)，建議直接使用現有名稱 "${match.matchedName}"`;
        }
        else if (match.similarity > 0.8) {
            return `相似度較高 (${similarity}%)，建議添加區別性後綴，如 "${newName}_v2" 或 "${newName}(新版)"`;
        }
        else {
            return `中等相似度 (${similarity}%)，可考慮使用更具區別性的名稱`;
        }
    }
}
//# sourceMappingURL=SmartEntityNormalizer.js.map