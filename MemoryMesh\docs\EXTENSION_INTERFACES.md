# 🔮 MemoryMesh 擴展接口文檔

本文檔詳細說明了MemoryMesh工作流程增強功能中預留的擴展接口，為未來功能開發提供指導。

## 📋 目錄

- [存儲層擴展接口](#存儲層擴展接口)
- [工具系統擴展接口](#工具系統擴展接口)
- [工作流程模板擴展](#工作流程模板擴展)
- [UI擴展接口](#ui擴展接口)
- [事件系統擴展](#事件系統擴展)

---

## 🗄️ 存儲層擴展接口

### IExtendedStorage 接口

位置：`src/infrastructure/storage/IExtendedStorage.ts`

#### 插件化存儲擴展

```typescript
interface StoragePlugin {
    name: string;
    version: string;
    initialize(storage: IExtendedStorage): Promise<void>;
    extensions?: Record<string, any>; // 🔮 預留擴展字段
}

// 使用示例
await storage.registerStoragePlugin({
    name: 'advanced-indexing',
    version: '1.0.0',
    initialize: async (storage) => {
        // 初始化高級索引功能
    }
});
```

#### 自定義查詢接口

```typescript
interface CustomQuery {
    type: string;
    parameters: Record<string, any>;
    extensions?: Record<string, any>; // 🔮 預留查詢擴展字段
}

// 使用示例
const result = await storage.executeCustomQuery({
    type: 'complex-workflow-analysis',
    parameters: {
        workflowType: 'novel',
        dateRange: { start: '2024-01-01', end: '2024-12-31' }
    }
});
```

#### 數據遷移接口

```typescript
interface MigrationResult {
    success: boolean;
    migratedCount: number;
    errors: Error[];
    extensions?: Record<string, any>; // 🔮 預留遷移結果擴展字段
}

// 使用示例
const migrationResult = await storage.migrateData('1.0.0', '2.0.0');
```

---

## 🔧 工具系統擴展接口

### IToolPlugin 接口

位置：`src/integration/tools/interfaces/IExtendedToolSystem.ts`

#### 工具插件開發

```typescript
class CustomWorkflowPlugin implements IToolPlugin {
    readonly name = 'custom-workflow-analyzer';
    readonly version = '1.0.0';
    readonly description = '自定義工作流程分析工具';
    readonly category = 'workflow';

    async initialize(manager: ApplicationManager): Promise<void> {
        // 初始化插件
    }

    getTools(): Tool[] {
        return [
            {
                name: 'workflow_analyze_performance',
                description: '分析工作流程性能指標',
                inputSchema: {
                    type: 'object',
                    properties: {
                        workflowId: { type: 'string' },
                        metrics: { type: 'array', items: { type: 'string' } }
                    }
                }
            }
        ];
    }

    async handleToolCall(toolName: string, args: Record<string, any>): Promise<ToolResponse> {
        // 處理工具調用
        return { toolResult: { isError: false, data: {} } };
    }
}

// 註冊插件
await toolRegistry.registerPlugin(new CustomWorkflowPlugin());
```

#### 工具中間件系統

```typescript
class LoggingMiddleware implements IToolMiddleware {
    readonly name = 'logging';
    readonly priority = 100;

    async beforeExecution(toolName: string, args: any, context: IToolExecutionContext) {
        console.log(`[${context.sessionId}] Executing tool: ${toolName}`);
        return { toolName, args, context };
    }

    async afterExecution(toolName: string, result: IExtendedToolResponse, context: IToolExecutionContext) {
        console.log(`[${context.sessionId}] Tool completed: ${toolName} in ${result.executionTime}ms`);
        return result;
    }
}

// 註冊中間件
toolManager.registerMiddleware(new LoggingMiddleware());
```

---

## 📊 工作流程模板擴展

### 自定義模板註冊

位置：`src/core/workflow/WorkflowTemplates.ts`

#### 創建自定義模板

```typescript
const customTemplate: WorkflowTemplate = {
    id: 'academic-paper-v1',
    name: '學術論文創作流程',
    description: '適用於學術論文寫作的專業流程',
    category: 'custom',
    version: '1.0.0',
    stages: [
        {
            id: 'literature-review',
            name: '文獻回顧',
            description: '收集和分析相關文獻',
            order: 0,
            requiredNodeTypes: ['reference', 'citation'],
            optionalNodeTypes: ['note'],
            completionCriteria: {
                minNodes: 10,
                requiredFields: ['title', 'author', 'year']
            },
            // 🔮 預留階段擴展字段
            extensions: {
                citationStyle: 'APA',
                minimumSources: 20
            }
        }
        // ... 更多階段
    ],
    // 🔮 預留模板擴展字段
    extensions: {
        targetJournal: 'configurable',
        wordCountTarget: 8000
    }
};

// 註冊自定義模板
PredefinedTemplates.registerCustomTemplate(customTemplate);
```

#### 動態模板生成

```typescript
class TemplateGenerator {
    static generateFromUserInput(userRequirements: any): WorkflowTemplate {
        return {
            id: `user-generated-${Date.now()}`,
            name: userRequirements.name,
            description: userRequirements.description,
            category: 'custom',
            version: '1.0.0',
            stages: userRequirements.stages.map((stage: any, index: number) => ({
                id: stage.id,
                name: stage.name,
                description: stage.description,
                order: index,
                requiredNodeTypes: stage.requirements || ['content'],
                optionalNodeTypes: [],
                completionCriteria: {
                    minNodes: stage.minNodes || 1,
                    requiredFields: stage.requiredFields || ['name']
                }
            }))
        };
    }
}
```

---

## 🎨 UI擴展接口

### MemoryViewer 擴展區域

位置：`MemoryViewer.html`

#### 預留的UI擴展區域

```html
<!-- 🔮 預留擴展區域 -->
<div class="workflow-extension-panel">
    <p>預留區域：未來可添加階段操作按鈕、驗證結果、自定義插件等</p>
</div>
```

#### JavaScript 擴展接口

```javascript
// 🔮 預留UI擴展接口
window.MemoryMeshExtensions = {
    // 註冊自定義視圖組件
    registerViewComponent: function(name, component) {
        this.viewComponents = this.viewComponents || {};
        this.viewComponents[name] = component;
    },
    
    // 註冊自定義工作流程操作
    registerWorkflowAction: function(name, action) {
        this.workflowActions = this.workflowActions || {};
        this.workflowActions[name] = action;
    },
    
    // 擴展工作流程詳情顯示
    extendWorkflowDetails: function(workflowId, extensionHtml) {
        const extensionPanel = document.querySelector('.workflow-extension-panel');
        if (extensionPanel) {
            extensionPanel.innerHTML = extensionHtml;
        }
    }
};

// 使用示例
MemoryMeshExtensions.registerWorkflowAction('export-to-pdf', {
    label: '導出為PDF',
    icon: '📄',
    handler: async (workflowId) => {
        // 實現PDF導出邏輯
    }
});
```

#### CSS 擴展樣式

```css
/* 🔮 預留UI擴展樣式 */
.workflow-extension-panel {
    margin-top: 1rem;
    padding: 1rem;
    border: 1px dashed var(--gray-300);
    border-radius: 6px;
    background-color: var(--gray-50);
}

.workflow-plugin-area {
    min-height: 100px;
    border: 2px dashed var(--gray-200);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}
```

---

## 📡 事件系統擴展

### 工作流程事件監聽

```typescript
// 監聽工作流程事件
workflowStateManager.on('workflow_created', (event) => {
    console.log('新工作流程已創建:', event.workflowId);
    // 🔮 可以在這裡添加自定義邏輯
});

workflowStateManager.on('stage_state_changed', (event) => {
    console.log('階段狀態變更:', event);
    // 🔮 可以觸發通知、更新UI等
});

// 自定義事件發射
workflowStateManager.emit('custom_workflow_event', {
    type: 'performance_milestone',
    workflowId: 'wf_123',
    milestone: 'first_chapter_completed',
    timestamp: new Date().toISOString(),
    // 🔮 預留事件擴展字段
    extensions: {
        wordCount: 2500,
        estimatedReadingTime: '10 minutes'
    }
});
```

### 存儲事件監聽

```typescript
// 監聽存儲操作事件
storage.onStorageEvent('workflow_saved', (event) => {
    console.log('工作流程已保存:', event.data);
    // 🔮 可以觸發備份、同步等操作
});

storage.onStorageEvent('plugin_registered', (event) => {
    console.log('插件已註冊:', event.data.plugin);
    // 🔮 可以更新UI、載入插件配置等
});
```

---

## 🚀 擴展開發指南

### 1. 開發新的工作流程類型

```typescript
// 1. 創建自定義模板
const gameDevTemplate: WorkflowTemplate = {
    id: 'game-development-v1',
    name: '遊戲開發流程',
    category: 'custom',
    // ... 定義階段
};

// 2. 註冊模板
PredefinedTemplates.registerCustomTemplate(gameDevTemplate);

// 3. 創建專用工具
class GameDevToolPlugin implements IToolPlugin {
    // ... 實現遊戲開發專用工具
}

// 4. 註冊工具插件
await toolRegistry.registerPlugin(new GameDevToolPlugin());
```

### 2. 添加新的存儲後端

```typescript
class DatabaseStoragePlugin implements StoragePlugin {
    name = 'database-storage';
    version = '1.0.0';

    async initialize(storage: IExtendedStorage): Promise<void> {
        // 初始化數據庫連接
        // 註冊自定義查詢處理器
    }
}

await storage.registerStoragePlugin(new DatabaseStoragePlugin());
```

### 3. 擴展UI功能

```javascript
// 添加自定義工作流程視圖
MemoryMeshExtensions.registerViewComponent('gantt-chart', {
    render: (workflowData) => {
        // 渲染甘特圖
        return '<div class="gantt-chart">...</div>';
    },
    update: (workflowData) => {
        // 更新甘特圖
    }
});
```

---

## 📝 最佳實踐

1. **向後兼容性**：所有擴展都應該保持向後兼容
2. **錯誤處理**：實現完善的錯誤處理和恢復機制
3. **性能考慮**：避免阻塞主要工作流程
4. **文檔化**：為所有擴展提供清晰的文檔
5. **測試覆蓋**：確保擴展功能有充分的測試覆蓋

---

## 🔄 版本兼容性

| 擴展接口版本 | MemoryMesh版本 | 兼容性狀態 |
|-------------|---------------|-----------|
| 1.0.0       | 0.2.8+        | ✅ 完全兼容 |
| 1.1.0       | 0.3.0+        | 🔄 計劃中   |

---

## 📞 支持與貢獻

如果您在使用擴展接口時遇到問題或有改進建議，請：

1. 查看現有的測試用例作為參考
2. 閱讀相關的TypeScript接口定義
3. 參考預留的擴展字段和方法
4. 提交Issue或Pull Request

**記住：所有標記為 🔮 的部分都是為未來擴展預留的接口！**
