import { BaseToolHandler } from './BaseToolHandler.js';
import type { IExtendedStorage } from '../../../infrastructure/storage/IExtendedStorage.js';
import type { ApplicationManager } from '../../../application/managers/ApplicationManager.js';
/**
 * 工作流程工具處理器
 * 專門處理工作流程相關的工具調用
 */
export declare class WorkflowToolHandler extends BaseToolHandler {
    private extendedStorage;
    private workflowStateManager;
    constructor(knowledgeGraphManager: ApplicationManager, extendedStorage: IExtendedStorage);
    handleTool(name: string, args: Record<string, any>): Promise<any>;
    /**
     * 創建新的工作流程
     */
    private createWorkflow;
    /**
     * 獲取工作流程狀態
     */
    private getWorkflowStatus;
    /**
     * 推進工作流程到下一階段
     */
    private advanceWorkflow;
    /**
     * 列出所有工作流程
     */
    private listWorkflows;
    /**
     * 驗證階段完成條件
     */
    private validateStage;
    /**
     * 完成階段
     */
    private completeStage;
    /**
     * 獲取工作流程模板
     */
    private getWorkflowTemplates;
    /**
     * 暫停工作流程
     */
    private pauseWorkflow;
    /**
     * 恢復工作流程
     */
    private resumeWorkflow;
    /**
     * 刪除工作流程
     */
    private deleteWorkflow;
    private handlePluginTool;
    private createCustomTemplate;
    private getStageRequirements;
    private getCompletionCriteria;
    private isStageComplete;
    private checkStageRequirements;
    private createExtendedResponse;
    private createExecutionContext;
    private groupBy;
}
