// tests/mcp/ContinueE2ETest.js
// 使用現有工作流程繼續端到端測試

console.log('🚀 使用現有工作流程繼續端到端測試...');

async function continueE2ETest() {
    try {
        console.log('📦 導入模塊...');
        
        const handlerModule = await import('../../dist/integration/tools/handlers/WorkflowToolHandler.js');
        const storageModule = await import('../../dist/infrastructure/storage/CachedJsonLineStorage.js');
        const appManagerModule = await import('../../dist/application/managers/ApplicationManager.js');
        
        console.log('✅ 所有模塊導入成功');

        const storage = new storageModule.CachedJsonLineStorage();
        const appManager = new appManagerModule.ApplicationManager(storage);
        const handler = new handlerModule.WorkflowToolHandler(appManager, storage);

        console.log('✅ 實例創建成功');

        // 使用已知的工作流程ID（完整節點名稱）
        const workflowId = 'workflow_wf_1751799470616_kram2feyx';
        console.log(`📝 使用工作流程ID: ${workflowId}`);

        // === 第一階段：查看現有狀態 ===
        console.log('\n🔍 第一階段：查看現有狀態');
        const statusResult1 = await handler.handleTool('workflow_status', { workflowId });
        console.log('📄 當前狀態:', JSON.stringify(statusResult1, null, 2));

        if (statusResult1.isError) {
            console.log('❌ 工作流程不存在或無法訪問');
            return false;
        }

        // === 第二階段：添加規劃階段內容 ===
        console.log('\n📚 第二階段：添加規劃階段內容');

        // 添加主角
        const addCharacterResult = await handler.handleTool('add_character', {
            character: {
                name: 'MCP測試主角_艾瑞克',
                role: 'protagonist',
                status: 'Active',
                currentLocation: ['魔法學院'],
                description: '一個年輕的魔法師學徒，擁有罕見的元素控制能力',
                background: '來自偏遠村莊，因為意外展現魔法天賦而被招入學院',
                motivation: '掌握自己的力量，保護家鄉',
                traits: ['勇敢', '好奇', '有責任感', '有時衝動'],
                abilities: ['元素魔法', '快速學習', '直覺敏銳']
            }
        });
        console.log('✅ 主角添加:', addCharacterResult.isError ? '失敗' : '成功');
        if (addCharacterResult.isError) {
            console.log('❌ 主角添加失敗:', JSON.stringify(addCharacterResult, null, 2));
        }

        // 添加設定
        const addSettingResult = await handler.handleTool('add_setting', {
            setting: {
                name: '阿卡迪亞魔法學院',
                type: 'Building',
                description: '一座古老而宏偉的魔法學院，坐落在雲霧繚繞的山峰上',
                status: 'Active',
                significance: 'Critical',
                atmosphere: '神秘而充滿學術氣息，古老的石牆散發著魔法的光芒',
                notableFeatures: ['浮空圖書館', '元素練習場', '古老的召喚陣', '魔法植物園'],
                sensoryDetails: '空氣中瀰漫著魔法能量的微光，遠處傳來咒語練習的聲音'
            }
        });
        console.log('✅ 設定添加:', addSettingResult.isError ? '失敗' : '成功');

        // 添加主題
        const addThemeResult = await handler.handleTool('add_theme', {
            theme: {
                name: '成長與責任',
                type: 'Main Theme',
                description: '探討年輕人如何在面對強大力量時學會承擔責任',
                status: 'Introduced',
                importance: 'Core',
                currentDevelopment: '通過主角在學院的學習和挑戰展現成長過程'
            }
        });
        console.log('✅ 主題添加:', addThemeResult.isError ? '失敗' : '成功');

        // === 第三階段：驗證規劃階段 ===
        console.log('\n✅ 第三階段：驗證規劃階段');
        const validateResult1 = await handler.handleTool('stage_validate', { workflowId });
        console.log('📄 規劃階段驗證:', JSON.stringify(validateResult1, null, 2));

        // === 第四階段：推進到大綱階段 ===
        console.log('\n⏭️ 第四階段：推進到大綱階段');
        const advanceResult1 = await handler.handleTool('workflow_advance', { workflowId });
        console.log('📄 推進結果:', JSON.stringify(advanceResult1, null, 2));

        // === 第五階段：添加大綱階段內容 ===
        console.log('\n📖 第五階段：添加大綱階段內容');

        // 添加情節線
        const addPlotResult = await handler.handleTool('add_plotarc', {
            plotarc: {
                name: '學院試煉主線',
                type: 'Main Plot',
                description: '艾瑞克在魔法學院面臨的一系列試煉和挑戰',
                status: 'Planning',
                importance: 'Critical',
                progressPercentage: '0',
                mainCharacters: ['MCP測試主角_艾瑞克'],
                incitingIncident: '神秘的魔法波動出現在學院',
                climax: '與古老邪惡力量的最終對決',
                resolution: '艾瑞克掌握真正的力量，成為守護者'
            }
        });
        console.log('✅ 情節線添加:', addPlotResult.isError ? '失敗' : '成功');

        // === 第六階段：測試工作流程管理功能 ===
        console.log('\n🔧 第六階段：測試工作流程管理功能');

        // 暫停工作流程
        const pauseResult = await handler.handleTool('workflow_pause', {
            workflowId,
            reason: 'MCP端到端測試暫停'
        });
        console.log('⏸️ 暫停結果:', pauseResult.isError ? '失敗' : '成功');

        // 恢復工作流程
        const resumeResult = await handler.handleTool('workflow_resume', {
            workflowId,
            notes: 'MCP端到端測試恢復'
        });
        console.log('▶️ 恢復結果:', resumeResult.isError ? '失敗' : '成功');

        // === 第七階段：列出所有工作流程 ===
        console.log('\n📋 第七階段：列出所有工作流程');
        const listResult = await handler.handleTool('workflow_list', {});
        console.log('📄 工作流程列表:', JSON.stringify(listResult, null, 2));

        // === 第八階段：獲取最終狀態 ===
        console.log('\n🏁 第八階段：獲取最終狀態');
        const finalStatusResult = await handler.handleTool('workflow_status', { workflowId });
        console.log('📄 最終狀態:', JSON.stringify(finalStatusResult, null, 2));

        // === 第九階段：測試錯誤處理 ===
        console.log('\n❌ 第九階段：測試錯誤處理');

        // 測試不存在的工作流程
        const errorTest1 = await handler.handleTool('workflow_status', {
            workflowId: 'definitely-does-not-exist-12345'
        });
        console.log('🔍 不存在工作流程測試:', errorTest1.isError ? '正確返回錯誤' : '意外成功');

        // 測試無效參數
        const errorTest2 = await handler.handleTool('workflow_advance', {
            workflowId: 'invalid-id',
            invalidParam: 'should-be-ignored'
        });
        console.log('🔍 無效參數測試:', errorTest2.isError ? '正確返回錯誤' : '意外成功');

        // === 第十階段：測試所有核心工具 ===
        console.log('\n🛠️ 第十階段：測試所有核心工具');

        // 測試模板獲取
        const templatesResult = await handler.handleTool('workflow_templates', {});
        console.log('📋 模板獲取:', templatesResult.isError ? '失敗' : '成功');

        // 測試階段完成
        const completeResult = await handler.handleTool('stage_complete', {
            workflowId,
            stageId: `${workflowId}_planning`,
            force: true,
            notes: 'MCP測試強制完成'
        });
        console.log('✅ 階段完成:', completeResult.isError ? '失敗' : '成功');

        // === 清理 ===
        console.log('\n🧹 清理資源...');
        if (storage && typeof storage.cleanup === 'function') {
            storage.cleanup();
            console.log('✅ 存儲已清理');
        }

        console.log('\n🎉 MCP端到端測試完成！');
        console.log(`📊 測試項目ID: ${workflowId}`);
        
        return {
            success: true,
            workflowId,
            testResults: {
                workflowExists: !statusResult1.isError,
                contentAdded: !addCharacterResult.isError && !addSettingResult.isError && !addThemeResult.isError,
                stageValidated: !validateResult1.isError,
                stageAdvanced: !advanceResult1.isError,
                workflowManaged: !pauseResult.isError && !resumeResult.isError,
                errorHandling: errorTest1.isError && errorTest2.isError,
                allToolsTested: !templatesResult.isError
            }
        };

    } catch (error) {
        console.error('❌ MCP端到端測試過程中發生錯誤:', error);
        console.error('錯誤堆棧:', error.stack);
        return { success: false, error: error.message };
    }
}

// 運行測試
continueE2ETest().then(result => {
    if (result.success) {
        console.log('\n✅ MCP端到端測試成功完成！');
        console.log('📊 測試結果摘要:');
        Object.entries(result.testResults).forEach(([test, passed]) => {
            console.log(`  ${passed ? '✅' : '❌'} ${test}`);
        });
        process.exit(0);
    } else {
        console.log('\n❌ MCP端到端測試失敗');
        if (result.error) {
            console.log('錯誤:', result.error);
        }
        process.exit(1);
    }
}).catch(error => {
    console.error('❌ 未捕獲的錯誤:', error);
    process.exit(1);
});
