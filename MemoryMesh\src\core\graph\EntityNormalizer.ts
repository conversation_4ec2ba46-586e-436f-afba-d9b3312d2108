// src/core/graph/EntityNormalizer.ts

export interface EntityNormalizationRule {
    normalize: (text: string) => string;
    similarity: (text1: string, text2: string) => number;
    threshold: number;
}

/**
 * 實體標準化和去重系統
 * 解決繁簡中文、標點符號、空格等導致的重複實體問題
 */
export class EntityNormalizer {
    private static readonly TRADITIONAL_TO_SIMPLIFIED: { [key: string]: string } = {
        // 常用繁簡對照表 - 人名常用字增強版
        '對': '对', '會': '会', '來': '来', '個': '个', '們': '们',
        '時': '时', '國': '国', '過': '过', '說': '说', '問': '问',
        '學': '学', '長': '长', '開': '开', '關': '关', '發': '发',
        '現': '现', '見': '见', '聽': '听', '覺': '觉', '買': '买',
        '賣': '卖', '門': '门', '間': '间', '點': '点', '線': '线',
        '風': '风', '氣': '气', '車': '车', '電': '电', '話': '话',
        '書': '书', '還': '还', '應': '应', '該': '该', '實': '实',
        '際': '际', '經': '经', '歷': '历', '業': '业', '務': '务',
        '員': '员', '師': '师', '導': '导', '領': '领', '機': '机',
        '構': '构', '產': '产', '品': '品', '質': '质', '量': '量',
        '價': '价', '值': '值', '資': '资', '料': '料', '訊': '讯',
        '息': '息', '題': '题', '類': '类', '種': '种', '樣': '样',
        '級': '级', '層': '层', '次': '次', '藝': '艺', '術': '术',
        '創': '创', '造': '造', '設': '设', '計': '计', '畫': '画',
        
        // 人名常用繁體字補充
        '麗': '丽', '絲': '丝', '聖': '圣', '輝': '辉', '愛': '爱',
        '華': '华', '強': '强', '偉': '伟', '軍': '军', '勇': '勇',
        '傑': '杰', '雄': '雄', '龍': '龙', '鳳': '凤', '豪': '豪',
        '英': '英', '健': '健', '明': '明', '智': '智', '慧': '慧',
        '美': '美', '秀': '秀', '芳': '芳', '蘭': '兰', '梅': '梅',
        '竹': '竹', '菊': '菊', '雲': '云', '月': '月', '陽': '阳',
        '春': '春', '夏': '夏', '秋': '秋', '冬': '冬', '東': '东',
        '西': '西', '南': '南', '北': '北', '中': '中', '天': '天',
        '地': '地', '人': '人', '王': '王', '皇': '皇', '帝': '帝',
        '后': '后', '妃': '妃', '公': '公', '主': '主', '侯': '侯',
        '爵': '爵', '將': '将', '相': '相', '臣': '臣', '民': '民'
    };

    /**
     * 標準化實體名稱
     */
    static normalizeEntityName(name: string): string {
        if (!name) return '';
        
        let normalized = name;
        
        // 1. 去除前後空格
        normalized = normalized.trim();
        
        // 2. 標準化標點符號
        normalized = normalized
            .replace(/：/g, ':')      // 中文冒號 → 英文冒號
            .replace(/，/g, ',')      // 中文逗號 → 英文逗號
            .replace(/。/g, '.')      // 中文句號 → 英文句號
            .replace(/？/g, '?')      // 中文問號 → 英文問號
            .replace(/！/g, '!')      // 中文驚嘆號 → 英文驚嘆號
            .replace(/；/g, ';')      // 中文分號 → 英文分號
            .replace(/（/g, '(')      // 中文左括號 → 英文左括號
            .replace(/）/g, ')')      // 中文右括號 → 英文右括號
            .replace(/【/g, '[')      // 中文左方括號 → 英文左方括號
            .replace(/】/g, ']')      // 中文右方括號 → 英文右方括號
            .replace(/"/g, '"')       // 中文左引號 → 英文引號
            .replace(/"/g, '"')       // 中文右引號 → 英文引號
            .replace(/'/g, "'")       // 中文左單引號 → 英文單引號  
            .replace(/'/g, "'");      // 中文右單引號 → 英文單引號
        
        // 3. 標準化空格（將多個空格合併為一個）
        normalized = normalized.replace(/\s+/g, ' ');
        
        // 4. 繁體轉簡體（用於去重比較）
        for (const [trad, simp] of Object.entries(this.TRADITIONAL_TO_SIMPLIFIED)) {
            normalized = normalized.replace(new RegExp(trad, 'g'), simp);
        }
        
        // 5. 轉換為小寫（用於比較）
        normalized = normalized.toLowerCase();
        
        return normalized;
    }

    /**
     * 計算兩個實體名稱的相似度
     */
    static calculateSimilarity(name1: string, name2: string): number {
        const norm1 = this.normalizeEntityName(name1);
        const norm2 = this.normalizeEntityName(name2);
        
        // 精確匹配
        if (norm1 === norm2) {
            return 1.0;
        }
        
        // 編輯距離相似度
        const editDistance = this.levenshteinDistance(norm1, norm2);
        const maxLength = Math.max(norm1.length, norm2.length);
        
        if (maxLength === 0) return 0;
        
        return 1 - (editDistance / maxLength);
    }

    /**
     * 計算編輯距離
     */
    private static levenshteinDistance(str1: string, str2: string): number {
        const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
        
        for (let i = 0; i <= str1.length; i++) {
            matrix[0][i] = i;
        }
        
        for (let j = 0; j <= str2.length; j++) {
            matrix[j][0] = j;
        }
        
        for (let j = 1; j <= str2.length; j++) {
            for (let i = 1; i <= str1.length; i++) {
                const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(
                    matrix[j][i - 1] + 1,     // deletion
                    matrix[j - 1][i] + 1,     // insertion
                    matrix[j - 1][i - 1] + indicator // substitution
                );
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    /**
     * 檢查是否為重複實體
     */
    static isDuplicateEntity(existingNames: string[], newName: string, threshold: number = 0.9): {
        isDuplicate: boolean;
        matchedName?: string;
        similarity?: number;
    } {
        const normalizedNew = this.normalizeEntityName(newName);
        
        for (const existingName of existingNames) {
            const similarity = this.calculateSimilarity(existingName, newName);
            
            if (similarity >= threshold) {
                return {
                    isDuplicate: true,
                    matchedName: existingName,
                    similarity: similarity
                };
            }
        }
        
        return { isDuplicate: false };
    }

    /**
     * 找出所有可能的重複實體組
     */
    static findDuplicateGroups(names: string[], threshold: number = 0.9): string[][] {
        const groups: string[][] = [];
        const processed = new Set<string>();
        
        for (const name of names) {
            if (processed.has(name)) continue;
            
            const group = [name];
            processed.add(name);
            
            for (const otherName of names) {
                if (otherName === name || processed.has(otherName)) continue;
                
                const similarity = this.calculateSimilarity(name, otherName);
                if (similarity >= threshold) {
                    group.push(otherName);
                    processed.add(otherName);
                }
            }
            
            if (group.length > 1) {
                groups.push(group);
            }
        }
        
        return groups;
    }

    /**
     * 選擇最佳的實體名稱（從重複組中）
     */
    static selectBestEntityName(names: string[]): string {
        if (names.length === 0) return '';
        if (names.length === 1) return names[0];
        
        // 優先級規則：
        // 1. 繁體中文優於簡體中文
        // 2. 較短的名稱優於較長的名稱
        // 3. 不包含額外空格的優於包含空格的
        
        return names.sort((a, b) => {
            // 檢查是否包含繁體字
            const aHasTrad = this.hasTraditionalChinese(a);
            const bHasTrad = this.hasTraditionalChinese(b);
            
            if (aHasTrad && !bHasTrad) return -1;
            if (!aHasTrad && bHasTrad) return 1;
            
            // 長度比較
            if (a.length !== b.length) {
                return a.length - b.length;
            }
            
            // 空格數量比較
            const aSpaces = (a.match(/\s/g) || []).length;
            const bSpaces = (b.match(/\s/g) || []).length;
            
            return aSpaces - bSpaces;
        })[0];
    }

    /**
     * 檢查字符串是否包含繁體中文
     */
    private static hasTraditionalChinese(text: string): boolean {
        const traditionalChars = Object.keys(this.TRADITIONAL_TO_SIMPLIFIED);
        return traditionalChars.some(char => text.includes(char));
    }
}