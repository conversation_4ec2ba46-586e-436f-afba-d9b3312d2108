// tests/unit/WorkflowTools.test.ts
// 工作流程工具完整單元測試

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

describe('工作流程工具單元測試', () => {
    let workflowHandler: any;
    let storage: any;
    let appManager: any;

    beforeEach(async () => {
        // 動態導入模塊
        const { WorkflowToolHandler } = await import('../../dist/integration/tools/handlers/WorkflowToolHandler.js');
        const { CachedJsonLineStorage } = await import('../../dist/infrastructure/storage/CachedJsonLineStorage.js');
        const { ApplicationManager } = await import('../../dist/application/managers/ApplicationManager.js');
        
        storage = new CachedJsonLineStorage();
        appManager = new ApplicationManager(storage);
        workflowHandler = new WorkflowToolHandler(appManager, storage);
    });

    afterEach(() => {
        if (storage && typeof storage.cleanup === 'function') {
            storage.cleanup();
        }
    });

    describe('workflow_templates 工具測試', () => {
        it('應該返回所有可用模板', async () => {
            const result = await workflowHandler.handleTool('workflow_templates', {});
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
            expect(result.content).toBeDefined();
            expect(Array.isArray(result.content)).toBe(true);
            expect(result.content.length).toBeGreaterThan(0);
        });

        it('應該支持按類別過濾模板', async () => {
            const result = await workflowHandler.handleTool('workflow_templates', {
                category: 'novel'
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
        });

        it('應該處理無效的類別參數', async () => {
            const result = await workflowHandler.handleTool('workflow_templates', {
                category: 'invalid_category'
            });
            
            expect(result).toBeDefined();
            // 應該返回空結果或錯誤，而不是崩潰
        });
    });

    describe('workflow_create 工具測試', () => {
        it('應該成功創建小說工作流程', async () => {
            const uniqueName = `單元測試小說_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            const result = await workflowHandler.handleTool('workflow_create', {
                name: uniqueName,
                type: 'novel'
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
            expect(result.content).toBeDefined();
        });

        it('應該成功創建文章工作流程', async () => {
            const uniqueName = `單元測試文章_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            const result = await workflowHandler.handleTool('workflow_create', {
                name: uniqueName,
                type: 'article'
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
        });

        it('應該支持自定義階段', async () => {
            const uniqueName = `單元測試自定義_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            const result = await workflowHandler.handleTool('workflow_create', {
                name: uniqueName,
                type: 'custom',
                customStages: ['準備', '執行', '檢查', '完成']
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
        });

        it('應該拒絕缺少必需參數的請求', async () => {
            const result = await workflowHandler.handleTool('workflow_create', {
                type: 'novel'
                // 缺少name參數
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(true);
        });

        it('應該拒絕無效的工作流程類型', async () => {
            const result = await workflowHandler.handleTool('workflow_create', {
                name: '無效類型測試',
                type: 'invalid_type'
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(true);
        });

        it('應該處理重複的工作流程名稱', async () => {
            const name = `重複測試_${Date.now()}`;
            
            // 第一次創建
            const result1 = await workflowHandler.handleTool('workflow_create', {
                name,
                type: 'novel'
            });
            
            // 第二次創建相同名稱
            const result2 = await workflowHandler.handleTool('workflow_create', {
                name,
                type: 'novel'
            });
            
            expect(result1.isError).toBe(false);
            expect(result2.isError).toBe(true); // 應該被重複檢測攔截
        });
    });

    describe('workflow_list 工具測試', () => {
        it('應該返回工作流程列表', async () => {
            const result = await workflowHandler.handleTool('workflow_list', {});
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
            expect(result.content).toBeDefined();
        });

        it('應該支持狀態過濾', async () => {
            const result = await workflowHandler.handleTool('workflow_list', {
                status: 'in_progress'
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
        });

        it('應該支持類型過濾', async () => {
            const result = await workflowHandler.handleTool('workflow_list', {
                type: 'novel'
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
        });

        it('應該支持分頁參數', async () => {
            const result = await workflowHandler.handleTool('workflow_list', {
                limit: 5,
                offset: 0
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
        });
    });

    describe('workflow_status 工具測試', () => {
        let testWorkflowId: string;

        beforeEach(async () => {
            // 創建測試工作流程
            const uniqueName = `狀態測試_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const createResult = await workflowHandler.handleTool('workflow_create', {
                name: uniqueName,
                type: 'novel'
            });
            
            if (!createResult.isError && createResult.content) {
                // 從響應中提取工作流程ID
                const content = createResult.content.find((c: any) => c.text && c.text.includes('workflowId'));
                if (content) {
                    const match = content.text.match(/"workflowId":\s*"([^"]+)"/);
                    if (match) {
                        testWorkflowId = match[1];
                    }
                }
            }
        });

        it('應該返回有效工作流程的狀態', async () => {
            if (!testWorkflowId) {
                console.log('跳過測試：無法創建測試工作流程');
                return;
            }

            const result = await workflowHandler.handleTool('workflow_status', {
                workflowId: testWorkflowId
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
            expect(result.content).toBeDefined();
        });

        it('應該拒絕不存在的工作流程ID', async () => {
            const result = await workflowHandler.handleTool('workflow_status', {
                workflowId: 'non_existent_workflow_12345'
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(true);
        });

        it('應該拒絕缺少workflowId參數的請求', async () => {
            const result = await workflowHandler.handleTool('workflow_status', {});
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(true);
        });
    });

    describe('workflow_advance 工具測試', () => {
        let testWorkflowId: string;

        beforeEach(async () => {
            const uniqueName = `推進測試_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const createResult = await workflowHandler.handleTool('workflow_create', {
                name: uniqueName,
                type: 'novel'
            });
            
            if (!createResult.isError && createResult.content) {
                const content = createResult.content.find((c: any) => c.text && c.text.includes('workflowId'));
                if (content) {
                    const match = content.text.match(/"workflowId":\s*"([^"]+)"/);
                    if (match) {
                        testWorkflowId = match[1];
                    }
                }
            }
        });

        it('應該支持強制推進階段', async () => {
            if (!testWorkflowId) {
                console.log('跳過測試：無法創建測試工作流程');
                return;
            }

            const result = await workflowHandler.handleTool('workflow_advance', {
                workflowId: testWorkflowId,
                force: true
            });
            
            expect(result).toBeDefined();
            // 強制推進應該成功或給出明確的錯誤原因
        });

        it('應該拒絕不存在的工作流程', async () => {
            const result = await workflowHandler.handleTool('workflow_advance', {
                workflowId: 'non_existent_workflow_12345'
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(true);
        });
    });

    describe('workflow_pause 和 workflow_resume 工具測試', () => {
        let testWorkflowId: string;

        beforeEach(async () => {
            const uniqueName = `暫停恢復測試_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const createResult = await workflowHandler.handleTool('workflow_create', {
                name: uniqueName,
                type: 'novel'
            });
            
            if (!createResult.isError && createResult.content) {
                const content = createResult.content.find((c: any) => c.text && c.text.includes('workflowId'));
                if (content) {
                    const match = content.text.match(/"workflowId":\s*"([^"]+)"/);
                    if (match) {
                        testWorkflowId = match[1];
                    }
                }
            }
        });

        it('應該成功暫停工作流程', async () => {
            if (!testWorkflowId) {
                console.log('跳過測試：無法創建測試工作流程');
                return;
            }

            const result = await workflowHandler.handleTool('workflow_pause', {
                workflowId: testWorkflowId,
                reason: '單元測試暫停'
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
        });

        it('應該成功恢復工作流程', async () => {
            if (!testWorkflowId) {
                console.log('跳過測試：無法創建測試工作流程');
                return;
            }

            // 先暫停
            await workflowHandler.handleTool('workflow_pause', {
                workflowId: testWorkflowId,
                reason: '單元測試暫停'
            });

            // 再恢復
            const result = await workflowHandler.handleTool('workflow_resume', {
                workflowId: testWorkflowId,
                notes: '單元測試恢復'
            });
            
            expect(result).toBeDefined();
            expect(result.isError).toBe(false);
        });
    });

    describe('stage_validate 工具測試', () => {
        let testWorkflowId: string;

        beforeEach(async () => {
            const uniqueName = `階段驗證測試_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const createResult = await workflowHandler.handleTool('workflow_create', {
                name: uniqueName,
                type: 'novel'
            });
            
            if (!createResult.isError && createResult.content) {
                const content = createResult.content.find((c: any) => c.text && c.text.includes('workflowId'));
                if (content) {
                    const match = content.text.match(/"workflowId":\s*"([^"]+)"/);
                    if (match) {
                        testWorkflowId = match[1];
                    }
                }
            }
        });

        it('應該驗證當前階段', async () => {
            if (!testWorkflowId) {
                console.log('跳過測試：無法創建測試工作流程');
                return;
            }

            const result = await workflowHandler.handleTool('stage_validate', {
                workflowId: testWorkflowId
            });
            
            expect(result).toBeDefined();
            // 驗證應該成功或返回明確的驗證結果
        });

        it('應該支持詳細驗證模式', async () => {
            if (!testWorkflowId) {
                console.log('跳過測試：無法創建測試工作流程');
                return;
            }

            const result = await workflowHandler.handleTool('stage_validate', {
                workflowId: testWorkflowId,
                detailed: true
            });
            
            expect(result).toBeDefined();
        });
    });

    describe('邊界條件和異常測試', () => {
        it('應該處理空參數', async () => {
            const result = await workflowHandler.handleTool('workflow_list', {});
            expect(result).toBeDefined();
        });

        it('應該處理null參數', async () => {
            const result = await workflowHandler.handleTool('workflow_templates', null as any);
            expect(result).toBeDefined();
        });

        it('應該處理undefined參數', async () => {
            const result = await workflowHandler.handleTool('workflow_templates', undefined as any);
            expect(result).toBeDefined();
        });

        it('應該處理非常長的工作流程名稱', async () => {
            const longName = 'A'.repeat(1000);
            const result = await workflowHandler.handleTool('workflow_create', {
                name: longName,
                type: 'novel'
            });
            
            expect(result).toBeDefined();
            // 應該有適當的長度限制
        });

        it('應該處理特殊字符的工作流程名稱', async () => {
            const specialName = '測試!@#$%^&*()_+-=[]{}|;:,.<>?';
            const result = await workflowHandler.handleTool('workflow_create', {
                name: specialName,
                type: 'novel'
            });
            
            expect(result).toBeDefined();
        });

        it('應該處理併發創建請求', async () => {
            const promises = [];
            for (let i = 0; i < 5; i++) {
                promises.push(
                    workflowHandler.handleTool('workflow_create', {
                        name: `併發測試_${i}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                        type: 'novel'
                    })
                );
            }
            
            const results = await Promise.allSettled(promises);
            expect(results.length).toBe(5);
            
            // 至少應該有一些成功的結果
            const successful = results.filter(r => r.status === 'fulfilled').length;
            expect(successful).toBeGreaterThan(0);
        });
    });

    describe('性能測試', () => {
        it('工具調用應該在合理時間內完成', async () => {
            const startTime = Date.now();
            
            await workflowHandler.handleTool('workflow_templates', {});
            
            const duration = Date.now() - startTime;
            expect(duration).toBeLessThan(5000); // 5秒內完成
        });

        it('應該能夠處理多個連續請求', async () => {
            const startTime = Date.now();
            
            for (let i = 0; i < 10; i++) {
                await workflowHandler.handleTool('workflow_templates', {});
            }
            
            const duration = Date.now() - startTime;
            expect(duration).toBeLessThan(10000); // 10秒內完成10個請求
        });
    });
});
