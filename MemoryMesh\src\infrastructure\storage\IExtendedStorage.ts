// src/infrastructure/storage/IExtendedStorage.ts

import type { Graph, Node, Edge } from '@core/index.js';
import type { IStorage } from './IStorage.js';

/**
 * 工作流程相關的數據結構
 */
export interface WorkflowState {
    workflowId: string;
    name: string;
    status: 'not_started' | 'in_progress' | 'completed' | 'paused';
    currentStage: number;
    totalStages: number;
    progress: number;
    metadata: Record<string, any>;
    createdAt: string;
    updatedAt: string;
    // 🔮 預留擴展字段
    extensions?: Record<string, any>;
}

/**
 * 階段狀態數據結構
 */
export interface StageState {
    stageId: string;
    workflowId: string;
    name: string;
    order: number;
    status: 'pending' | 'active' | 'completed' | 'skipped';
    requiredNodeTypes: string[];
    completionCriteria: Record<string, any>;
    // 🔮 預留擴展字段
    extensions?: Record<string, any>;
}

/**
 * 緩存配置接口
 */
export interface CacheConfig {
    maxSize: number;
    ttl: number; // Time to live in milliseconds
    enablePersistence: boolean;
    // 🔮 預留擴展字段
    extensions?: Record<string, any>;
}

/**
 * 索引配置接口
 */
export interface IndexConfig {
    nodeNameIndex: boolean;
    nodeTypeIndex: boolean;
    workflowIndex: boolean;
    customIndexes: string[];
    // 🔮 預留擴展字段
    extensions?: Record<string, any>;
}

/**
 * 擴展存儲接口 - 為未來功能預留擴展點
 */
export interface IExtendedStorage extends IStorage {
    // === 性能優化相關 ===
    
    /**
     * 增量更新節點（避免全量重寫）
     */
    updateNodesIncremental(updates: Partial<Node>[]): Promise<void>;
    
    /**
     * 批量操作支持
     */
    batchOperation(operations: BatchOperation[]): Promise<BatchResult>;
    
    /**
     * 緩存管理
     */
    configureCaching(config: CacheConfig): Promise<void>;
    invalidateCache(keys?: string[]): Promise<void>;
    
    // === 工作流程相關 ===
    
    /**
     * 工作流程狀態管理
     */
    saveWorkflowState(workflow: WorkflowState): Promise<void>;
    loadWorkflowState(workflowId: string): Promise<WorkflowState | null>;
    listWorkflows(): Promise<WorkflowState[]>;
    
    /**
     * 階段狀態管理
     */
    saveStageState(stage: StageState): Promise<void>;
    loadStageStates(workflowId: string): Promise<StageState[]>;
    
    // === 索引和查詢優化 ===
    
    /**
     * 配置索引
     */
    configureIndexes(config: IndexConfig): Promise<void>;
    
    /**
     * 高效查詢接口
     */
    queryNodesByType(nodeType: string, limit?: number): Promise<Node[]>;
    queryNodesByWorkflow(workflowId: string): Promise<Node[]>;
    
    // === 🔮 未來擴展預留接口 ===
    
    /**
     * 插件化存儲擴展點
     * 允許第三方插件擴展存儲功能
     */
    registerStoragePlugin(plugin: StoragePlugin): Promise<void>;
    
    /**
     * 自定義查詢接口
     * 支持複雜查詢邏輯的擴展
     */
    executeCustomQuery(query: CustomQuery): Promise<any>;
    
    /**
     * 數據遷移接口
     * 支持未來數據結構變更時的遷移
     */
    migrateData(fromVersion: string, toVersion: string): Promise<MigrationResult>;
    
    /**
     * 事件鉤子接口
     * 允許外部監聽存儲操作事件
     */
    onStorageEvent(event: string, handler: StorageEventHandler): void;
}

// === 支持類型定義 ===

export interface BatchOperation {
    type: 'create' | 'update' | 'delete';
    target: 'node' | 'edge' | 'workflow' | 'stage';
    data: any;
}

export interface BatchResult {
    success: boolean;
    results: any[];
    errors: Error[];
}

export interface StoragePlugin {
    name: string;
    version: string;
    initialize(storage: IExtendedStorage): Promise<void>;
    // 🔮 預留插件擴展接口
    extensions?: Record<string, any>;
}

export interface CustomQuery {
    type: string;
    parameters: Record<string, any>;
    // 🔮 預留查詢擴展字段
    extensions?: Record<string, any>;
}

export interface MigrationResult {
    success: boolean;
    migratedCount: number;
    errors: Error[];
    // 🔮 預留遷移結果擴展字段
    extensions?: Record<string, any>;
}

export type StorageEventHandler = (event: StorageEvent) => void;

export interface StorageEvent {
    type: string;
    timestamp: string;
    data: any;
    // 🔮 預留事件擴展字段
    extensions?: Record<string, any>;
}
