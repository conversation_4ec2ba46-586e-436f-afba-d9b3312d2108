// tests/mcp/DirectToolTest.js
// 直接工具測試腳本

import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 動態導入模塊
async function loadModules() {
    try {
        const { WorkflowToolHandler } = await import('../../dist/integration/tools/handlers/WorkflowToolHandler.js');
        const { CachedJsonLineStorage } = await import('../../dist/infrastructure/storage/CachedJsonLineStorage.js');
        const { ApplicationManager } = await import('../../dist/application/managers/ApplicationManager.js');
        
        return { WorkflowToolHandler, CachedJsonLineStorage, ApplicationManager };
    } catch (error) {
        console.error('❌ 模塊導入失敗:', error.message);
        return null;
    }
}

class DirectToolTester {
    constructor() {
        this.testResults = [];
        this.workflowHandler = null;
        this.storage = null;
        this.appManager = null;
    }

    async initialize() {
        console.log('🚀 初始化測試環境...');
        
        const modules = await loadModules();
        if (!modules) {
            throw new Error('無法加載必需的模塊');
        }

        const { WorkflowToolHandler, CachedJsonLineStorage, ApplicationManager } = modules;

        try {
            this.storage = new CachedJsonLineStorage();
            this.appManager = new ApplicationManager(this.storage);
            this.workflowHandler = new WorkflowToolHandler(this.appManager, this.storage);
            
            console.log('✅ 測試環境初始化成功');
            return true;
        } catch (error) {
            console.error('❌ 初始化失敗:', error.message);
            return false;
        }
    }

    async testTool(toolName, params = {}) {
        console.log(`\n🧪 測試工具: ${toolName}`);
        console.log(`📝 參數:`, JSON.stringify(params, null, 2));
        
        try {
            const startTime = Date.now();
            const result = await this.workflowHandler.handleTool(toolName, params);
            const duration = Date.now() - startTime;

            console.log(`⏱️  執行時間: ${duration}ms`);
            console.log(`📥 結果:`, JSON.stringify(result, null, 2));

            const testResult = {
                tool: toolName,
                params,
                success: !result.toolResult.isError,
                duration,
                response: result.toolResult,
                timestamp: new Date().toISOString()
            };

            this.testResults.push(testResult);

            if (result.toolResult.isError) {
                console.log(`❌ 工具調用失敗: ${result.toolResult.error}`);
                return { success: false, error: result.toolResult.error, result };
            } else {
                console.log(`✅ 工具調用成功`);
                return { success: true, result };
            }
        } catch (error) {
            console.log(`❌ 測試失敗: ${error.message}`);
            this.testResults.push({
                tool: toolName,
                params,
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
            return { success: false, error: error.message };
        }
    }

    async runWorkflowTests() {
        console.log('\n🔄 開始工作流程工具測試...');

        // 測試1: 獲取工作流程模板
        console.log('\n=== 測試1: 獲取工作流程模板 ===');
        await this.testTool('workflow_templates', {});

        // 測試2: 創建小說工作流程
        console.log('\n=== 測試2: 創建小說工作流程 ===');
        const createResult = await this.testTool('workflow_create', {
            name: '直接測試小說項目',
            type: 'novel',
            metadata: {
                author: '直接測試',
                genre: '科幻'
            }
        });

        let workflowId = null;
        if (createResult.success && createResult.result.toolResult.data) {
            workflowId = createResult.result.toolResult.data.workflowId;
            console.log(`📝 工作流程ID: ${workflowId}`);
        }

        if (workflowId) {
            // 測試3: 查看工作流程狀態
            console.log('\n=== 測試3: 查看工作流程狀態 ===');
            await this.testTool('workflow_status', { workflowId });

            // 測試4: 驗證當前階段
            console.log('\n=== 測試4: 驗證當前階段 ===');
            await this.testTool('stage_validate', { workflowId });

            // 測試5: 列出所有工作流程
            console.log('\n=== 測試5: 列出所有工作流程 ===');
            await this.testTool('workflow_list', {});

            // 測試6: 暫停工作流程
            console.log('\n=== 測試6: 暫停工作流程 ===');
            await this.testTool('workflow_pause', {
                workflowId,
                reason: '直接測試暫停'
            });

            // 測試7: 恢復工作流程
            console.log('\n=== 測試7: 恢復工作流程 ===');
            await this.testTool('workflow_resume', {
                workflowId,
                notes: '直接測試恢復'
            });

            // 測試8: 強制推進階段
            console.log('\n=== 測試8: 強制推進階段 ===');
            await this.testTool('workflow_advance', {
                workflowId,
                force: true
            });

            // 測試9: 再次查看狀態
            console.log('\n=== 測試9: 再次查看狀態 ===');
            await this.testTool('workflow_status', { workflowId });

            return workflowId;
        }

        return null;
    }

    async runErrorTests() {
        console.log('\n❌ 開始錯誤處理測試...');

        // 測試10: 不存在的工作流程
        console.log('\n=== 測試10: 不存在的工作流程 ===');
        await this.testTool('workflow_status', {
            workflowId: 'non-existent-workflow-123'
        });

        // 測試11: 無效參數
        console.log('\n=== 測試11: 無效參數 ===');
        await this.testTool('workflow_create', {
            // 缺少必需的name參數
            type: 'novel'
        });

        // 測試12: 無效的工作流程類型
        console.log('\n=== 測試12: 無效的工作流程類型 ===');
        await this.testTool('workflow_create', {
            name: '無效類型測試',
            type: 'invalid_type'
        });
    }

    async runIntegrationTests() {
        console.log('\n🔗 開始集成測試...');

        // 創建工作流程
        console.log('\n=== 集成測試: 創建工作流程 ===');
        const createResult = await this.testTool('workflow_create', {
            name: '集成測試項目',
            type: 'novel'
        });

        let workflowId = null;
        if (createResult.success && createResult.result.toolResult.data) {
            workflowId = createResult.result.toolResult.data.workflowId;
        }

        if (workflowId) {
            // 添加角色節點
            console.log('\n=== 集成測試: 添加角色節點 ===');
            await this.testTool('add_character', {
                character: {
                    name: '直接測試角色',
                    role: 'protagonist',
                    status: 'Active',
                    currentLocation: ['測試城市'],
                    description: '用於直接測試的角色'
                }
            });

            // 添加設定節點
            console.log('\n=== 集成測試: 添加設定節點 ===');
            await this.testTool('add_setting', {
                setting: {
                    name: '測試城市',
                    type: 'Urban',
                    description: '用於直接測試的城市設定',
                    status: 'Active'
                }
            });

            // 驗證階段（應該有足夠內容推進）
            console.log('\n=== 集成測試: 驗證階段 ===');
            await this.testTool('stage_validate', { workflowId });

            // 嘗試推進階段
            console.log('\n=== 集成測試: 推進階段 ===');
            await this.testTool('workflow_advance', { workflowId });

            return workflowId;
        }

        return null;
    }

    generateReport() {
        console.log('\n📊 生成測試報告...');
        
        const totalTests = this.testResults.length;
        const successfulTests = this.testResults.filter(r => r.success).length;
        const failedTests = totalTests - successfulTests;
        const successRate = totalTests > 0 ? ((successfulTests / totalTests) * 100).toFixed(1) : 0;

        console.log('\n' + '='.repeat(80));
        console.log('📋 直接工具測試報告');
        console.log('='.repeat(80));
        console.log(`總測試數: ${totalTests}`);
        console.log(`成功: ${successfulTests}`);
        console.log(`失敗: ${failedTests}`);
        console.log(`成功率: ${successRate}%`);
        console.log('='.repeat(80));

        console.log('\n📝 詳細結果:');
        this.testResults.forEach((result, index) => {
            const status = result.success ? '✅' : '❌';
            const duration = result.duration ? ` (${result.duration}ms)` : '';
            console.log(`${index + 1}. ${status} ${result.tool}${duration}`);
            if (!result.success) {
                console.log(`   錯誤: ${result.error || result.response?.error || '未知錯誤'}`);
            }
        });

        // 按工具分組統計
        console.log('\n📈 按工具統計:');
        const toolStats = {};
        this.testResults.forEach(result => {
            if (!toolStats[result.tool]) {
                toolStats[result.tool] = { total: 0, success: 0 };
            }
            toolStats[result.tool].total++;
            if (result.success) {
                toolStats[result.tool].success++;
            }
        });

        Object.entries(toolStats).forEach(([tool, stats]) => {
            const rate = ((stats.success / stats.total) * 100).toFixed(1);
            console.log(`  ${tool}: ${stats.success}/${stats.total} (${rate}%)`);
        });

        return {
            total: totalTests,
            successful: successfulTests,
            failed: failedTests,
            successRate: parseFloat(successRate),
            results: this.testResults,
            toolStats
        };
    }

    async cleanup() {
        console.log('\n🧹 清理資源...');
        if (this.storage && typeof this.storage.cleanup === 'function') {
            this.storage.cleanup();
            console.log('✅ 存儲已清理');
        }
    }
}

// 主測試函數
async function runDirectTests() {
    const tester = new DirectToolTester();
    
    try {
        const initialized = await tester.initialize();
        if (!initialized) {
            console.log('❌ 初始化失敗，無法進行測試');
            return null;
        }

        // 運行工作流程測試
        const workflowId1 = await tester.runWorkflowTests();

        // 運行錯誤處理測試
        await tester.runErrorTests();

        // 運行集成測試
        const workflowId2 = await tester.runIntegrationTests();

        // 生成報告
        const report = tester.generateReport();
        
        console.log('\n💾 測試完成');
        console.log(`🎯 創建的工作流程ID: ${workflowId1}, ${workflowId2}`);

        return report;
        
    } catch (error) {
        console.error('❌ 測試過程中發生錯誤:', error);
        return null;
    } finally {
        await tester.cleanup();
    }
}

// 如果直接運行此腳本
if (import.meta.url === `file://${process.argv[1]}`) {
    runDirectTests().then(report => {
        if (report) {
            console.log(`\n🎉 測試完成！成功率: ${report.successRate}%`);
            process.exit(report.failed > 0 ? 1 : 0);
        } else {
            console.log('\n❌ 測試失敗');
            process.exit(1);
        }
    });
}

export { DirectToolTester, runDirectTests };
