export interface EntityNormalizationRule {
    normalize: (text: string) => string;
    similarity: (text1: string, text2: string) => number;
    threshold: number;
}
/**
 * 實體標準化和去重系統
 * 解決繁簡中文、標點符號、空格等導致的重複實體問題
 */
export declare class EntityNormalizer {
    private static readonly TRADITIONAL_TO_SIMPLIFIED;
    /**
     * 標準化實體名稱
     */
    static normalizeEntityName(name: string): string;
    /**
     * 計算兩個實體名稱的相似度
     */
    static calculateSimilarity(name1: string, name2: string): number;
    /**
     * 計算編輯距離
     */
    private static levenshteinDistance;
    /**
     * 檢查是否為重複實體
     */
    static isDuplicateEntity(existingNames: string[], newName: string, threshold?: number): {
        isDuplicate: boolean;
        matchedName?: string;
        similarity?: number;
    };
    /**
     * 找出所有可能的重複實體組
     */
    static findDuplicateGroups(names: string[], threshold?: number): string[][];
    /**
     * 選擇最佳的實體名稱（從重複組中）
     */
    static selectBestEntityName(names: string[]): string;
    /**
     * 檢查字符串是否包含繁體中文
     */
    private static hasTraditionalChinese;
}
