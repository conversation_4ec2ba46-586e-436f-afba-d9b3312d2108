// tests/mcp/CheckSpecificWorkflow.js
// 檢查特定工作流程

console.log('🔍 檢查特定工作流程...');

async function checkSpecificWorkflow() {
    try {
        const handlerModule = await import('../../dist/integration/tools/handlers/WorkflowToolHandler.js');
        const storageModule = await import('../../dist/infrastructure/storage/CachedJsonLineStorage.js');
        const appManagerModule = await import('../../dist/application/managers/ApplicationManager.js');
        
        const storage = new storageModule.CachedJsonLineStorage();
        const appManager = new appManagerModule.ApplicationManager(storage);
        const handler = new handlerModule.WorkflowToolHandler(appManager, storage);

        console.log('✅ 模塊加載成功');

        // 檢查特定工作流程
        const workflowId = 'workflow_wf_1751799104279_4jsukd1eb';
        console.log(`🔍 檢查工作流程: ${workflowId}`);
        
        const statusResult = await handler.handleTool('workflow_status', { workflowId });
        console.log('📄 狀態結果:', JSON.stringify(statusResult, null, 2));

        // 如果存在，嘗試刪除
        if (!statusResult.isError) {
            console.log('🗑️ 嘗試刪除工作流程...');
            const deleteResult = await handler.handleTool('workflow_delete', { 
                workflowId,
                force: true 
            });
            console.log('📄 刪除結果:', JSON.stringify(deleteResult, null, 2));
        }

        // 再次列出工作流程
        const listResult = await handler.handleTool('workflow_list', {});
        console.log('📄 更新後的工作流程列表:', JSON.stringify(listResult, null, 2));

        // 清理
        if (storage && typeof storage.cleanup === 'function') {
            storage.cleanup();
        }

        return true;

    } catch (error) {
        console.error('❌ 錯誤:', error);
        return false;
    }
}

checkSpecificWorkflow();
